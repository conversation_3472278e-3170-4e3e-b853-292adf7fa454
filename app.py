import ast
import re
import sys
from datetime import datetime

from services.log_config import setup_logging

# Call it before creating the Flask app instance
setup_logging()

from flask import Flask, redirect, url_for
from flask_jwt_extended import JWTManager
from flask_jwt_extended.exceptions import NoAuthorizationError

from auth import auth_bp
from demand import demand_bp
from demand_monitoring import demand_monitoring_bp
from filters import filters_bp
from main import main_bp
from oem import ean_oem_bp
from parser import parser_bp
from percentage import percentage_bp
from price_relevance import price_relevance_bp
from reports import reports_bp
from settings import settings_bp
from item_catalog import item_catalog_bp
from settings_brands import settings_brands_bp
from settings_sups import settings_sups_bp
from suppliers import supplier_bp
from task_management import task_management_bp

# Сброс предела рекурсии
sys.setrecursionlimit(100000)
app = Flask(__name__, static_folder="staticfiles")


# JWT configuration
app.config["JWT_SECRET_KEY"] = "7AF506BF124CF395F52B6909571EE135"
app.config["SECRET_KEY"] = "7AF506BF124CF395F52B6909571EE135"
app.config["JWT_TOKEN_LOCATION"] = ["cookies"]
app.config["JWT_COOKIE_CSRF_PROTECT"] = False  # Disable CSRF protection for simplicity, enable in production

jwt = JWTManager(app)

# Register Blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(main_bp)
app.register_blueprint(filters_bp)
app.register_blueprint(settings_bp)
app.register_blueprint(reports_bp)
app.register_blueprint(percentage_bp)
app.register_blueprint(parser_bp)
app.register_blueprint(demand_bp)
app.register_blueprint(task_management_bp)
app.register_blueprint(demand_monitoring_bp)
app.register_blueprint(ean_oem_bp)
app.register_blueprint(settings_sups_bp)
app.register_blueprint(settings_brands_bp)
app.register_blueprint(supplier_bp)
app.register_blueprint(price_relevance_bp)
app.register_blueprint(item_catalog_bp)
# CORS(app)  # Enable CORS if needed


# Error handler for unauthorized access
@jwt.unauthorized_loader
async def unauthorized_callback(callback):
    return redirect(url_for("auth.login"))


# Error handler for expired token
@jwt.expired_token_loader
def my_expired_token_callback(a, b):
    return redirect(url_for("auth.login"))


@app.errorhandler(NoAuthorizationError)
def internal_error(error):
    return redirect(url_for("auth.login"))


def to_datetime(unix_timestamp, format="%Y-%m-%d %H:%M:%S"):
    if unix_timestamp:
        return datetime.utcfromtimestamp(float(unix_timestamp)).strftime(format)
    return ""


def from_json(s):
    try:
        # Извлечение строки JSON внутри скобок
        match = re.search(r"\((.*)\)", s)
        if match:
            print("aye")
            json_str = match.group(1)
            # Замена одинарных кавычек на двойные кавычки
            json_str = re.sub(r"(?<!\\)'", '"', json_str)
            # Исправление строк внутри списка платформ
            json_str = re.sub(r'"\[', "[", json_str)
            json_str = re.sub(r'\]"', "]", json_str)

            # Преобразование строки в Python объект
            python_obj = ast.literal_eval(json_str)

            # Функция для рекурсивного преобразования set в list
            def convert_set_to_list(obj):
                if isinstance(obj, set):
                    return list(obj)
                elif isinstance(obj, dict):
                    return {k: convert_set_to_list(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_set_to_list(i) for i in obj]
                else:
                    return obj

            # Преобразование всех set в list
            python_obj = convert_set_to_list(python_obj)

            # Преобразование Python объекта в JSON строку и обратно
            print(python_obj)
            return python_obj[0]
        return {}
    except (ValueError, SyntaxError) as e:
        print(f"Error: {e}")
        return {}


app.jinja_env.filters["to_datetime"] = to_datetime
app.jinja_env.filters["from_json"] = from_json

if __name__ == "__main__":
    app.run(debug=True)
