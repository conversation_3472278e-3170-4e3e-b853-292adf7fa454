# Документация по контуру импорта прайс‑листов поставщиков

> Актуально на: **16 июля 2025**

---

## 1. Назначение

Данный документ описывает микросервисную подсистему, которая автоматически собирает прайс‑листы от внешних поставщиков, нормализует данные и загружает их в ClickHouse для последующего использования другими сервисами (аналитика запасов, ценообразование, BI‑отчёты и т.д.). Документ предназначен для разработчиков, которым необходимо быстро погрузиться в логику работы контура.

---

## 2. Архитектура высокого уровня

```text
┌──────────────────┐            ┌────────────────────┐
│ Поставщики       │            │ Поставщики         │
│ (Email)          │            │ (FTP/SFTP)         │
└─────────┬────────┘            └──────────┬─────────┘
          │                                 │
          │                                 │
   1. load_mail                    1. load_ftp
          │                                 │
          └──────────────┬───────────────────┘
                         │  CSV/Excel файл
                         ▼
                2. consolidate
                         │  «consolidate.csv»
                         ▼
                3. Airflow DAG `load_dif_step_1.py`
                         │  nightly @ 23:50
                         ▼
                4. ClickHouse `sup_stat.dif_step_1`
```

*Все микросервисы упакованы в отдельные Docker‑образы

---

## 3. Описание микросервисов

### 3.1 `load_mail`

| Характеристика  | Значение                                                                                                    |
| --------------- |-------------------------------------------------------------------------------------------------------------|
| Язык/рантайм    | Python (FastAPI для health‑check)                                                                           |
| Назначение      | Опрашивает выделенные почтовые ящики и извлекает вложения‑прайс‑листы                                       |
| Протокол        | IMAP + OAuth2                                                                                               |
| Фильтры писем   | Адрес отправителя **и** имя вложения (ссылка на mapping в Django‑админке)                                   |
| Выход           | Файл сохраняется в `mail_backup/{supid}/YYYYMMDD_original.ext` и передаётся в `consolidate` через HTTP‑POST |
| Ретрай политика | 3 попытки с экспоненциальной задержкой; после неудачи → Telegram‑alert + перенос во `failed_files/`         |


### 3.2 `load_ftp`

| Характеристика | Значение                                                                                   |
| -------------- | ------------------------------------------------------------------------------------------ |
| Язык/рантайм   | Python                                                                            |
| Протоколы      | FTP / FTPS / SFTP                                       |
| Креденшлы      | Хранятся в Vault, подставляются Sidecar‑init‑контейнером                                   |
| Выход          | Файл сохраняется в `ftp_backup/{supid}/YYYYMMDD_original.ext` и передаётся в `consolidate` |

### 3.3 `consolidate`

| Характеристика        | Значение                                            |
| --------------------- | --------------------------------------------------- |
| Язык/рантайм          | Python 3.11 + `pandas`                              |
| Вход                  | CSV/XLSX файл + `supid`                             |
| Директория назначения | `/root/daily_consolidation/{supid}/consolidate.csv` |
| Логика                | 1) чтение входного файла;                           |

```
                       2) нормализация колонок `b, a, p, q` (upper‑case, trim, типы);
                       3) валидация (p > 0, q >= 0, дубликаты);
                       4) мердж строк в consolidate.csv (idempotent по ключу `(b,a)`);
                       5) telegram‑уведомление об успехе/ошибке. |
```

\| Очистка данных          | Убираются пустые строки/столбцы, пробелы, спецсимволы; строки с пустым `a` либо `b` отправляются в reject‑лог. |

### 3.4 **Airflow DAG **``

| Характеристика | Значение                                                                        |
| -------------- | ------------------------------------------------------------------------------- |
| Запуск         | Ежедневно в 23:50 Europe/Moscow (UTC+3)                                         |
| Окружение      | Airflow 2.9, KubernetesExecutor                                                 |
| Шаги           | 1) для каждого каталога `daily_consolidation/*/consolidate.csv` считывает файл; |

```
                       2) формирует DataFrame и пишет в ClickHouse через драйвер `clickhouse‑connect` методом `insert_dataframe`;
                       3) после успешной вставки удаляет файл, оставляя каталог; |
```

\| SLA                     | 10 мин; при нарушении SLA — алерт в Telegram |

---

## 4. Хранилище данных

### 4.1 Каталог `daily_consolidation`

```
/root/daily_consolidation/
 ├── 38/
 │   └── consolidate.csv
 ├── 42/
 │   └── consolidate.csv
 └── ...
```

*Файл всегда содержит только «свежий» срез прайс‑листа и перезаписывается при каждой поставке; старые данные уже лежат в ClickHouse.*

### 4.2 ClickHouse

#### 4.2.1 Таблица `sup_stat.dif_step_1`

```sql
CREATE TABLE sup_stat.dif_step_1
(
    `dateupd` Date,
    `supid`   Int32,
    `b`       String,
    `a`       String,
    `p`       Float64,
    `q`       Int32
)
ENGINE = ReplacingMergeTree
PARTITION BY toYYYYMMDD(dateupd)
ORDER BY (dateupd, supid, b, a)
TTL dateupd + toIntervalMonth(15)
SETTINGS index_granularity = 8192;
```

*Особенности:*

- Историчность по дню: для анализа динамики цен/наличия достаточно хранить дату актуальности прайса (`dateupd`).
- TTL = 15 месяцев, после чего партиции автоматически очищаются.

#### 4.2.2 Таблица‑справочник `sup_stat.sup_list`

```sql
CREATE TABLE sup_stat.sup_list
(
    `dif_id` Int32,
    `skl`    String,
    `name`   String,
    `post`   UInt64 COMMENT 'Срок поставки, часов',
    `lists`  Array(String)
)
ENGINE = ReplacingMergeTree
ORDER BY dif_id
SETTINGS index_granularity = 8192;
```

*Используется для обогащения отчётов (наименование поставщика, SLA, группировки «ОПТ‑2» и т.д.).*

---

## 5. Mapping email + attachment → `supid`

- Конфигурация хранится в внутренней Django‑админке (модель `SupplierAttachment`)

```
| id | email                    | filename_mask   | supid |
|----|--------------------------|-----------------|-------|
|  1 | <EMAIL>         | prices*.xlsx    |   38  |
|  2 | <EMAIL>         | tyres_*.csv     |   41  |
```

- Одна и та же почта может иметь несколько `supid`, различающихся маской имени файла.
- Благодаря этому сопоставление всегда однозначно.

---

## 6. Обработка ошибок и резервное копирование

| Стадия           | Каталог‑хранилище                 | Действие при ошибке                  |
| ---------------- | --------------------------------- | ------------------------------------ |
| Получение письма | `mail_backup/`                    | Файл сохраняется до парсинга         |
| Получение FTP    | `ftp_backup/`                     | Аналогично                           |
| Валидация        | `failed_files/{date}/supid_*.csv` | Файл + stacktrace; ссылка в Telegram |

Дополнительно все критичные события (`ERROR`, SLA miss) публикуются ботом в канал **#price‑alerts**.

## 7. Примеры запросов ClickHouse

*Количество уникальных артикулов по неделям за последние 4 месяца для поставщика 38*

```sql
SELECT toStartOfWeek(dateupd)        AS week,
       countDistinct(a)              AS sku_cnt
FROM   sup_stat.dif_step_1
WHERE  supid = 38
  AND  dateupd >= today() - 16*7
GROUP BY week
ORDER BY week;
```

*Средняя цена **`p`** по брендам за вчера*

```sql
SELECT b, avg(p) AS avg_price
FROM   sup_stat.dif_step_1
WHERE  dateupd = yesterday()
GROUP BY b
ORDER BY avg_price DESC
LIMIT 20;
```

