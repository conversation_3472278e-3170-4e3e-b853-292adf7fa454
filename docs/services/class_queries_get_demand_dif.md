# Документация по class_queries.py

## Процесс генерации отчета о потребности

### Общий поток:

1. **Пользовательский интерфейс**: Пользователь взаимодействует со страницей `demand_type.html`, где может:
   - Выбрать бренд (например, SUZUKI)
   - Выбрать между источниками данных DIF или EMEX
   - Сгенерировать отчет о потребности с фильтрами по бренду или без них

2. **Обработка на бэкенде**:
   - Когда пользователь отправляет форму, POST-запрос отправляется на `/get_demand_filtered` с выбранным брендом(ами)
   - Функция `get_demand_report_filtered` в `demand.py` обрабатывает этот запрос
   - Она вызывает либо `get_demand_dif_brands`, либо `get_demand_emex_brands` в зависимости от типа потребности
   - Эти функции являются асинхронными обертками, которые вызывают соответствующие методы в классе `ClicData`

3. **Запрос к базе данных**:
   - Метод `get_demand_dif_brands` в `class_queries.py` выполняет сложный SQL-запрос к базе данных ClickHouse
   - Запрос нормализует названия брендов, объединяет несколько таблиц и вычисляет различные метрики
   - Результаты форматируются в файл Excel и возвращаются пользователю

### Объяснение столбцов отчета о потребности DIF:

На основе SQL-запроса в методе `get_demand_dif_brands` (строки 1040-1164), вот что означает каждый столбец:

1. **sku**: Уникальный идентификатор, объединяющий бренд и артикул (формат: `БРЕНД|АРТИКУЛ`)

2. **supid**: ID поставщика

3. **name**: Название поставщика

4. **q_day_sum**: Общее количество дней, когда товар был в наличии. Рассчитывается путем суммирования значений `q_day` из таблицы `dif.dif_step_4_id_full`.

5. **q_sale_sum**: Общее количество дней, когда товар продавался. Рассчитывается путем суммирования значений `q_sale`из таблицы `dif.dif_step_4_id_full`.

6. **k_**: Коэффициент продаж, рассчитываемый как `(SUM(q_sale) * 30 / NULLIF(SUM(q_day), 0))`. Это отношение дней с продажами к дням с наличием товара, нормализованное на 30-дневный месяц. Показывает, как часто товар продается, когда он есть в наличии.

7. **total_quantity**: Общее количество проданных единиц за весь период. Рассчитывается путем суммирования значений `quantity`.

8. **quantity_m**: Среднемесячное количество продаж, рассчитываемое как `(SUM(quantity) / NULLIF(SUM(q_day), 0) * 30)`. Это среднее количество единиц, проданных за месяц, нормализованное на основе количества дней, когда товар был в наличии.

9. **price**: Средняя цена за единицу, рассчитываемая как `(SUM(price * quantity) / NULLIF(SUM(quantity), 0))`. Это взвешенное среднее, учитывающее разные цены при разных продажах.

### Ключевая логика SQL:

Основной расчет происходит в CTE (Common Table Expression) `aggregated_data` в SQL-запросе:

```sql
aggregated_data AS (
    SELECT
        sku,
        supid,
        name,
        SUM(q_day) AS q_day_sum,   -- Количество дней с остатком
        SUM(q_sale) AS q_sale_sum, -- Количество дней с продажами
        SUM(quantity) AS total_quantity, -- Количество проданных штук суммарно за все время
        (SUM(price * quantity) / NULLIF(SUM(quantity), 0)) AS price, -- Средняя цена за 1 штуку
        (SUM(q_sale) * 30 / NULLIF(SUM(q_day), 0)) AS k_, -- Коэффициент продаж
        (SUM(quantity) / NULLIF(SUM(q_day), 0) * 30) AS quantity_m -- Среднее количество проданных штук в месяц
    FROM
        unique_data
    GROUP BY
        sku, supid, name
    HAVING 
        q_day_sum > 14 AND q_sale_sum > 0
)
```

### Логика фильтрации:

Запрос включает только товары, которые:
1. Были в наличии более 14 дней (`q_day_sum > 14`)
2. Имели хотя бы одну продажу (`q_sale_sum > 0`)
3. Соответствуют указанному бренду(ам)
4. Не находятся в черном списке (`dif.full_sku_black_list` или `dif.full_sku_replace_list`)
5. Имеют поставщиков со значениями post (скорость доставки товара в часах) между 24 и 96 (`sl.post BETWEEN 24 AND 96`)

## Анализ примера данных

Рассмотрим пример данных для бренда SUZUKI:

```
sku	supid	name	q_day_sum	q_sale_sum	k_	total_quantity	quantity_m	price 
SUZUKI|0928044008	402	Элит Про (SKL347)	126,00	15,00	3,57	19,00	4,52	245,16 
```

Для первой строки:
- Этот товар был в наличии 126 дней
- 15 дней с продажами, чаще всего не подряд
- Коэффициент продаж (k_) равен 3,57, что означает, что он продается примерно 3,57 раза в месяц, когда есть в наличии
- Всего за весь период было продано 19 единиц
- Среднемесячные продажи (quantity_m) составляют 4,52 единицы
- Средняя цена за единицу составляет 245,16

Связь между этими значениями соответствует формулам в SQL-запросе:
- k_ = (q_sale_sum * 30) / q_day_sum = (15 * 30) / 126 ≈ 3,57
- quantity_m = (total_quantity / q_day_sum) * 30 = (19 / 126) * 30 ≈ 4,52

Эти данные помогают закупщикам принимать обоснованные решения о том, какие товары закупать и в каких количествах, основываясь на исторических моделях продаж и показателях поставщиков.

## Пример расшифровки отчета

Рассмотрим еще несколько строк из примера отчета:

```
sku	supid	name	q_day_sum	q_sale_sum	k_	total_quantity	quantity_m	price 
SUZUKI|0928044008	402	Элит Про (SKL347)	126,00	15,00	3,57	19,00	4,52	245,16 
SUZUKI|4243159J00	277	Автоскай Транзит2(SKL240)	188,00	12,00	1,91	21,00	3,35	317,95 
SUZUKI|0928248003	227	ЛР Групп (SKL188)	210,00	4,00	0,57	10,00	1,43	712,90 
SUZUKI|2474165J11	319	Джипартс (SKL263)	128,00	1,00	0,23	1,00	0,23	24860,00 
SUZUKI|1151077E02	353	Автостэлс 2 (SKL299)	214,00	12,00	1,68	15,00	2,10	11514,27 
```

### Сравнительный анализ:

1. **SUZUKI|0928044008** (Элит Про):
   - Средний коэффициент продаж (k_ = 3,57)
   - Хорошие среднемесячные продажи (quantity_m = 4,52)
   - Относительно низкая цена (245,16)

2. **SUZUKI|4243159J00** (Автоскай Транзит2):
   - Более низкий коэффициент продаж (k_ = 1,91)
   - Хорошие среднемесячные продажи (quantity_m = 3,35)
   - Средняя цена (317,95)

3. **SUZUKI|0928248003** (ЛР Групп):
   - Низкий коэффициент продаж (k_ = 0,57)
   - Низкие среднемесячные продажи (quantity_m = 1,43)
   - Высокая цена (712,90)

4. **SUZUKI|2474165J11** (Джипартс):
   - Очень низкий коэффициент продаж (k_ = 0,23)
   - Очень низкие среднемесячные продажи (quantity_m = 0,23)
   - Очень высокая цена (24860,00)

5. **SUZUKI|1151077E02** (Автостэлс 2):
   - Средний коэффициент продаж (k_ = 1,68)
   - Средние среднемесячные продажи (quantity_m = 2,10)
   - Очень высокая цена (11514,27)

### Выводы для закупщика:

- Товары с высоким k_ и quantity_m (как SUZUKI|0928044008) являются хорошими кандидатами для регулярных закупок
- Товары с низким k_ и высокой ценой (как SUZUKI|2474165J11) следует закупать только по запросу
- Товары со средними показателями (как SUZUKI|4243159J00) могут быть закуплены в меньших количествах для поддержания ассортимента

Эта информация позволяет оптимизировать закупки, сосредоточившись на товарах с высоким спросом и хорошей оборачиваемостью, избегая при этом избыточных запасов товаров с низким спросом.
