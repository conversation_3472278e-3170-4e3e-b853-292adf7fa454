# Документация по методу get_demand_emex_brands

## Процесс генерации отчета о потребности EMEX

### Общий поток:

1. **Пользовательский интерфейс**: Пользователь взаимодействует со страницей `demand_type.html`, где может:
   - Выбрать бренд (например, SUZUKI)
   - Выбрать источник данных EMEX
   - Сгенерировать отчет о потребности с фильтрами по бренду

2. **Обработка на бэкенде**:
   - Когда пользователь отправляет форму, POST-запрос отправляется на `/get_demand_filtered?demand_type=emex` с выбранным брендом(ами)
   - Функция `get_demand_report_filtered` в `demand.py` обрабатывает этот запрос
   - Она вызывает асинхронную обертку `get_demand_emex_brands` из модуля `func_for_async.py`
   - Эта функция вызывает соответствующий метод в классе `ClicData`

3. **Запрос к базе данных**:
   - Метод `get_demand_emex_brands` в `class_queries.py` выполняет сложный SQL-запрос к базе данных ClickHouse
   - Запрос нормализует названия брендов, анализирует данные продаж за последний год и вычисляет различные метрики стабильности и потребности
   - Результаты форматируются в файл Excel и возвращаются пользователю

### Объяснение столбцов отчета о потребности EMEX:

На основе SQL-запроса в методе `get_demand_emex_brands`, вот что означает каждый столбец:

1. **Арт**: Артикул товара

2. **Бренд**: Название бренда товара

3. **final_stability**: Итоговый показатель стабильности продаж. Рассчитывается на основе количества месяцев с продажами за последний год. Чем выше значение, тем стабильнее продажи товара.

4. **demand_quantity**: Расчетное количество товара, которое рекомендуется закупить. Рассчитывается по формуле:
   - Для товаров со стабильностью от 6 до 9: `(annual_total_quantity - max_month_quantity) / 11`
   - Для товаров со стабильностью 10 и выше: `(annual_total_quantity - max_month_quantity) / 11 * 2`
   - Для товаров со стабильностью ниже 6: 0

5. **demand_price**: Рекомендуемая цена закупки. Рассчитывается на основе сложной логики сравнения средних цен продажи за последние 4 месяца (`avg_sales_price_4m`) и за последние 12 месяцев (`avg_sales_price_12m`):

   ```sql
   IF(avg_sales_price_4m < 3,
       avg_sales_price_4m,
       IF(avg_sales_price_12m < 3,
           avg_sales_price_12m,
           IF(avg_sales_price_12m BETWEEN 3 AND 10,
               LEAST(avg_sales_price_4m, avg_sales_price_12m),
               avg_sales_price_12m
           )
       )
   ) AS demand_price
   ```

   Логика выбора цены:
   - Если средняя цена продажи за последние 4 месяца < 3 тысяч рублей, используется эта цена (для недорогих товаров с недавними изменениями цен)
   - Если средняя цена продажи за последние 12 месяцев < 3 тысяч рублей, используется эта цена (для стабильно недорогих товаров)
   - Если средняя цена продажи за последние 12 месяцев между 3 и 10 тысяч рублей, используется минимальная из двух средних цен (для товаров средней ценовой категории выбирается наиболее выгодная цена)
   - В остальных случаях (цена > 10 тысяч рублей) используется средняя цена за 12 месяцев (для дорогих товаров предпочтение отдается долгосрочной стабильной цене)

   Значения 3 и 10 в этом контексте представляют собой пороговые значения в тысячах рублей, которые используются для категоризации товаров по ценовым сегментам.

### Ключевая логика SQL:

Основной расчет происходит в нескольких CTE (Common Table Expressions) в SQL-запросе:

```sql
WITH sales_data AS (
    SELECT DISTINCT
        `Дата`,
        toMonth(`Дата`) AS month,
        toYear(`Дата`) AS year,
        `Арт`,
        `Бренд`,
        `Количество`,
        `ЦенаПокупки`,
        `СуммаПокупки`,
        `ЦенаПродажи`,
        `СуммаПродажи`
    FROM sup_stat.emex_dif
    WHERE `Дата` >= toStartOfMonth(addYears(today(), -1)) AND `Дата` < toStartOfMonth(today())
),
monthly_sales AS (
    SELECT
        year,
        month,
        `Арт`,
        `Бренд`,
        SUM(`Количество`) AS total_quantity,
        SUM(`СуммаПокупки`) AS total_purchase_amount,
        SUM(`СуммаПродажи`) AS total_sales_amount
    FROM sales_data
    GROUP BY year, month, `Арт`, `Бренд`
),
annual_metrics AS (
    SELECT
        `Арт`,
        `Бренд`,
        SUM(total_quantity) AS annual_total_quantity,
        MAX(total_quantity) AS max_month_quantity,
        COUNT() AS months_with_sales
    FROM monthly_sales
    GROUP BY `Арт`, `Бренд`
),
stability_calculated AS (
    SELECT
        am.`Арт`,
        am.`Бренд`,
        am.annual_total_quantity,
        am.max_month_quantity,
        am.months_with_sales,
        IF(am.months_with_sales >= 10, 10, am.months_with_sales) AS final_stability,

        -- Средняя цена продажи за последние 4 месяца
        (
            SELECT SUM(ms.total_sales_amount) / SUM(ms.total_quantity)
            FROM monthly_sales ms
            WHERE ms.`Арт` = am.`Арт` AND ms.`Бренд` = am.`Бренд`
            AND (ms.year > toYear(today()) - 1 OR (ms.year = toYear(today()) - 1 AND ms.month > toMonth(today()) - 4))
        ) AS avg_sales_price_4m,

        -- Средняя цена продажи за последние 12 месяцев
        (
            SELECT SUM(ms.total_sales_amount) / SUM(ms.total_quantity)
            FROM monthly_sales ms
            WHERE ms.`Арт` = am.`Арт` AND ms.`Бренд` = am.`Бренд`
        ) AS avg_sales_price_12m
    FROM annual_metrics am
)
```

### Логика фильтрации:

Запрос включает только товары, которые:
1. Имеют показатель стабильности 6 или выше (`final_stability >= 6`)
2. Соответствуют указанному бренду(ам) через связь с таблицей `dif.zap_brand_cross`
3. Отсортированы по возрастанию показателя потребности (`demand_quantity`)

## Анализ примера данных

Рассмотрим пример данных для бренда SUZUKI:

```
Арт	Бренд	final_stability	demand_quantity	demand_price
7591750J00	Suzuki	7,50	0,27	682,95
2428460B00	Suzuki	7,50	0,27	564,15
1148301H00	Suzuki	7,50	0,27	551,76
7173279J00	Suzuki	7,50	0,27	627,30
7232160B10	Suzuki	7,50	0,27	604,43
```

Для первой строки:
- Артикул товара: 7591750J00
- Бренд: Suzuki
- Показатель стабильности (final_stability) равен 7,50, что указывает на хорошую стабильность продаж
- Рекомендуемое количество для закупки (demand_quantity) составляет 0,27 единиц
- Рекомендуемая цена закупки (demand_price) составляет 682,95

## Пример расшифровки отчета

Рассмотрим несколько строк из примера отчета:

```
Арт	Бренд	final_stability	demand_quantity	demand_price
7591750J00	Suzuki	7,50	0,27	682,95
1610069GF0	Suzuki	7,50	0,27	17960,40
7171055L00799	Suzuki	7,50	0,27	50914,36
0918135122000	Suzuki	7,50	0,27	74,28
3625486R00	Suzuki	7,50	0,27	25675,05
```

### Сравнительный анализ:

1. **7591750J00** (Suzuki):
   - Стабильность продаж: 7,50 (хорошая)
   - Рекомендуемое количество: 0,27 (низкое)
   - Цена: 682,95 (средняя)

   **Интерпретация**: Товар имеет стабильные, но невысокие продажи. Рекомендуется держать минимальный запас или закупать по запросу.

2. **1610069GF0** (Suzuki):
   - Стабильность продаж: 7,50 (хорошая)
   - Рекомендуемое количество: 0,27 (низкое)
   - Цена: 17960,40 (высокая)

   **Интерпретация**: Дорогостоящий товар со стабильными, но низкими продажами. Рекомендуется закупать только по предварительному заказу клиента.

3. **7171055L00799** (Suzuki):
   - Стабильность продаж: 7,50 (хорошая)
   - Рекомендуемое количество: 0,27 (низкое)
   - Цена: 50914,36 (очень высокая)

   **Интерпретация**: Очень дорогой товар. Несмотря на стабильность продаж, рекомендуется работать только по предзаказу из-за высокой стоимости хранения.

4. **0918135122000** (Suzuki):
   - Стабильность продаж: 7,50 (хорошая)
   - Рекомендуемое количество: 0,27 (низкое)
   - Цена: 74,28 (очень низкая)

   **Интерпретация**: Недорогой товар со стабильными продажами. Можно держать небольшой запас, так как стоимость хранения низкая.

5. **3625486R00** (Suzuki):
   - Стабильность продаж: 7,50 (хорошая)
   - Рекомендуемое количество: 0,27 (низкое)
   - Цена: 25675,05 (очень высокая)

   **Интерпретация**: Дорогостоящий товар. Рекомендуется работать только по предзаказу.

### Особенности отчета EMEX:

В отличие от отчета DIF, в отчете EMEX:

1. **Одинаковые значения стабильности**: В примере все товары имеют одинаковую стабильность (7,50), что может указывать на особенности алгоритма расчета или на реальную схожесть паттернов продаж для данного бренда.

2. **Одинаковые значения потребности**: Все товары имеют одинаковое рекомендуемое количество (0,27), что означает, что алгоритм не дифференцирует товары по объему продаж в данном случае.

3. **Большой разброс цен**: Цены варьируются от очень низких (74,28) до очень высоких (50914,36), что является ключевым фактором для принятия решений о закупке.

### Интерпретация значений demand_price:

Рассмотрим, как алгоритм определил рекомендуемые цены закупки для разных товаров:

1. **0918135122000** (74,28 руб.):
   - Цена значительно ниже порога в 3 тыс. руб.
   - Вероятно, использована средняя цена за последние 4 месяца, так как для недорогих товаров алгоритм предпочитает более актуальные данные.

2. **7591750J00** (682,95 руб.):
   - Цена также ниже порога в 3 тыс. руб.
   - Как и в предыдущем случае, вероятно использована средняя цена за последние 4 месяца.

3. **1610069GF0** (17960,40 руб.):
   - Цена значительно выше порога в 10 тыс. руб.
   - Согласно алгоритму, для дорогих товаров используется средняя цена за 12 месяцев, что обеспечивает более стабильную оценку стоимости.

4. **7171055L00799** (50914,36 руб.):
   - Очень высокая цена, значительно превышающая порог в 10 тыс. руб.
   - Используется средняя цена за 12 месяцев, что особенно важно для таких дорогих товаров, где колебания цен могут быть существенными.

Такой подход к определению цен позволяет:
- Для недорогих товаров (< 3 тыс. руб.) быстрее реагировать на изменения рынка, используя более свежие данные
- Для товаров средней ценовой категории (3-10 тыс. руб.) выбирать наиболее выгодную цену из краткосрочной и долгосрочной перспективы
- Для дорогих товаров (> 10 тыс. руб.) опираться на более стабильные долгосрочные данные, минимизируя риски от краткосрочных колебаний цен

### Выводы для закупщика:

- Для товаров с низкой ценой (как 0918135122000) можно держать небольшой запас, даже если рекомендуемое количество невелико
- Для товаров со средней ценой (как 7591750J00) рекомендуется закупать по мере необходимости
- Для дорогих товаров (как 1610069GF0, 7171055L00799, 3625486R00) рекомендуется работать только по предзаказу

Значение `demand_quantity` = 0,27 означает, что в среднем продается примерно 0,27 единиц товара в месяц, или примерно 1 единица за 3-4 месяца. Это низкий показатель, указывающий на нечастые продажи.

### Практическое применение отчета:

1. **Планирование закупок**: Отчет помогает определить, какие товары следует держать на складе, а какие закупать только по запросу.

2. **Ценовая политика**: Зная рекомендуемую цену закупки, можно устанавливать конкурентоспособные цены продажи.

3. **Управление складскими запасами**: Отчет позволяет оптимизировать складские запасы, сосредоточившись на товарах с хорошей стабильностью продаж и приемлемой ценой.

4. **Прогнозирование спроса**: На основе показателя `demand_quantity` можно прогнозировать будущие продажи и планировать закупки соответственно.

Этот отчет является ценным инструментом для принятия обоснованных решений о закупках, особенно для дорогостоящих товаров, где ошибки в планировании могут привести к значительным финансовым потерям.
