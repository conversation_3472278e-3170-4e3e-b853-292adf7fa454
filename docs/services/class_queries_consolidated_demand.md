# Документация по классу class_queries.py

## Отчет о консолидированной потребности (DIF + EMEX)

Этот отчет объединяет данные о продажах и наличии товаров из двух источников: системы DIF и системы EMEX. Отчет строится для выбранных брендов и заданного диапазона дат. Для данных DIF применяется фильтр по коэффициенту регулярности продаж (k_). В отчет включаются товары, по которым была хотя бы одна продажа в выбранный пользователем период.

**Важное примечание:** Некоторые метрики (такие как `final_stability`, `quantity_m` и `price`) рассчитываются по-разному для данных DIF и EMEX, отражая особенности исходных данных и логики расчета потребности для каждого источника. Эти различия указаны в описании соответствующих столбцов.

### Объяснение столбцов отчета:

1.  **sku**: Объединенный идентификатор товара в формате `БРЕНД|АРТИКУЛ`.
    *   **Для данных DIF:** Формируется на основе нормализованного бренда (из `dif.unique_brands`) и артикула (из `dif.dif_step_4_id_full`).
    *   **Для данных EMEX:** Формируется на основе бренда и артикула из исходных данных `emex_dif`.

2.  **supid**: Уникальный числовой идентификатор поставщика.
    *   **Для данных из системы DIF:** Идентификатор поставщика из таблицы `sup_stat.sup_list`.
    *   **Для данных EMEX:** Всегда равен `1`, обозначая данные из этого источника.

3.  **name**: Название поставщика.
    *   **Для данных из системы DIF:** Название поставщика из таблицы `sup_stat.sup_list`.
    *   **Для данных EMEX:** Всегда "EMEX".

4.  **q_day_sum**: Общее количество дней с остатком (наличием товара).
    *   **Для данных DIF:** Сумма значений `q_day` из таблицы `dif.dif_step_4_id_full` для данного товара и поставщика в пределах **выбранного пользователем диапазона дат**.
    *   **Для данных EMEX:** Всегда `0.0`, так как эта метрика специфична для структуры данных DIF.

5.  **q_sale_sum**: Общее количество дней с продажами.
    *   **Для данных DIF:** Сумма значений `q_sale` из таблицы `dif.dif_step_4_id_full` для данного товара и поставщика в пределах **выбранного пользователем диапазона дат**.
    *   **Для данных EMEX:** Всегда `0.0`, так как эта метрика специфична для структуры данных DIF.

6.  **k_**: Коэффициент регулярности продаж. Показывает, как часто товар продается в месяц, когда он есть в наличии.
    *   **Для данных DIF:** Рассчитывается как `(SUM(q_sale) * 30 / NULLIF(SUM(q_day), 0))` на основе данных в пределах **выбранного пользователем диапазона дат**. Фильтруется по значению, заданному пользователем.
    *   **Для данных EMEX:** Всегда `100.0`, чтобы не применять фильтр k_ к данным EMEX (фильтрация данных EMEX происходит только по наличию продаж в выбранный период).

7.  **total_quantity**: Общее количество проданных единиц товара.
    *   **Для данных DIF:** Сумма значений `quantity` из таблицы `dif.dif_step_4_id_full` для данного товара и поставщика в пределах **выбранного пользователем диапазона дат**.
    *   **Для данных EMEX:** Всегда `0.0`, так как эта метрика специфична для структуры данных DIF.

8.  **quantity_m**: Рекомендуемое среднемесячное количество для закупки (потребность).
    *   **Для данных DIF:** Среднемесячное количество проданных штук в месяц, рассчитываемое как `(SUM(quantity) / NULLIF(SUM(q_day), 0) * 30)` на основе данных в пределах **выбранного пользователем диапазона дат**.
    *   **Для данных EMEX:** Расчетное количество товара, которое рекомендуется закупить, основанное на анализе продаж за **последние 12 месяцев до текущей даты**. Эта метрика (`demand_quantity` из старого отчета EMEX) переименована в `quantity_m` для соответствия формату консолидированного отчета.

9.  **price**: Средняя цена за единицу.
    *   **Для данных DIF:** Взвешенная средняя цена, учитывающая разные цены при разных продажах в пределах **выбранного пользователем диапазона дат**. Рассчитывается как `(SUM(price * quantity) / NULLIF(SUM(quantity), 0))`.
    *   **Для данных EMEX:** Рекомендуемая цена закупки, рассчитанная на основе анализа средних цен продажи за последние **4 и 12 месяцев до текущей даты**. Эта метрика (`demand_price` из старого отчета EMEX) используется для заполнения столбца `price` в консолидированном отчете.

10. **Арт**: Артикул товара.
    *   **Для данных DIF:** Часть `sku` после символа '|'.
    *   **Для данных EMEX:** Оригинальный артикул товара из данных `emex_dif`.

11. **Бренд**: Название бренда товара.
    *   **Для данных DIF:** Часть `sku` до символа '|' (нормализованный бренд).
    *   **Для данных EMEX:** Оригинальное название бренда из данных `emex_dif`.
