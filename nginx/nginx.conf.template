user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    client_max_body_size 20G;
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  1d;
    send_timeout 1d;
    client_body_timeout 1d;
    client_header_timeout 1d;
    types_hash_max_size 2048;

    include /etc/nginx/conf.d/*.conf;

    server {
        listen       ${PORT_NGINX};
        server_name  87.249.37.86;

        location /static/ {
            alias /app/static/;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        location / {
            proxy_buffers 16 64k;  # Увеличить количество и размер буферов
            proxy_buffer_size 128k;  # Увеличить размер буфера для заголовков
            proxy_busy_buffers_size 128k;  # Увеличить размер буферов в состоянии "busy"
            proxy_temp_file_write_size 128k;  # Увеличить размер записи во временный файл
            proxy_max_temp_file_size 1024m;  # Увеличить максимальный размер временного файла
            proxy_pass http://web:${PORT_WEB};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Таймауты
            proxy_connect_timeout 1d; # Таймаут на подключение к бэкенду
            proxy_send_timeout 1d;    # Таймаут на отправку данных бэкенду
            proxy_read_timeout 1d;    # Таймаут на чтение данных от бэкенда
        }
    }
}
