from flask import Blueprint, render_template, request, jsonify
from flask_jwt_extended import jwt_required
from clickhouse_driver import Client
from services.config_info import ClickInfo
from services.class_queries import ClicData
from func_for_async import check_emex_file, save_emex_file, download_summary_sups, get_sup_lists, get_sup_records, add_sup_record, update_sup_record, del_sup_record
from urllib.parse import urlparse
import json
import pandas as pd
import datetime
settings_sups_bp = Blueprint('settings_sups_bp', __name__)
# Подключение к ClickHouse

# Маршрут для рендеринга HTML страницы
@settings_sups_bp.route('/sups/records', methods=['GET'])
async def render_records_page():
    # Выполняем запрос в ClickHouse для получения всех записей
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})
    records = await get_sup_records(client)
    return render_template('sups_records.html', records=records)

# Получение всех записей (для AJAX)
@settings_sups_bp.route('/sups/records/data', methods=['GET'])
@jwt_required()
async def get_records():
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={'database': 'sup_stat', 'table': 'sup_list'}, filters={})
    records = await get_sup_records(client)
    return jsonify(records)

# Добавление записи
@settings_sups_bp.route('/sups/records', methods=['POST'])
@jwt_required()
async def add_record():
    data = request.json
    dif_id = data.get('dif_id')
    lists = data.get('lists', [])
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={'database': 'sup_stat', 'table': 'sup_list'}, filters={})
    result = await add_sup_record(client, data)
    if not result['error']: 
        return jsonify({'message': 'Record added successfully'})
    else:
        return jsonify({'message': result['message']})
# Обновление записи
@settings_sups_bp.route('/sups/records/<int:dif_id>', methods=['PUT'])
@jwt_required()
async def update_record(dif_id):
    data = request.json
    lists = data.get('lists', [])
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={'database': 'sup_stat', 'table': 'sup_list'}, filters={})
    result = await update_sup_record(client, dif_id, data=data)
    if not result['error']:
        return jsonify({'message': 'Record updated successfully'})
    else:  
        return jsonify({'message': result['message']})

# Удаление записи
@settings_sups_bp.route('/sups/records/<int:dif_id>', methods=['DELETE'])
@jwt_required()
async def delete_record(dif_id):
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={'database': 'sup_stat', 'table': 'sup_list'}, filters={})
    result = await del_sup_record(client, dif_id)
    return jsonify({'message': 'Record deleted successfully'})

@settings_sups_bp.route('/sups/<int:sup_id>/coefficients', methods=['GET'])
def view_coefficients(sup_id):
    # Получение коэффициентов для указанного `sup_id`
    query = f"SELECT sup_id, country, k FROM sup_stat.sups_coef WHERE sup_id = {sup_id};"
    coefficients = client.query(query).result_rows
    return render_template('coefficients.html', sup_id=sup_id, coefficients=coefficients)

@settings_sups_bp.route('/sups/coefficients/<int:id>', methods=['PUT'])
def update_coefficient(id):
    data = request.json
    # Проверка входных данных
    if 'country' not in data or 'k' not in data:
        return jsonify({"error": "Invalid input"}), 400

    # Валидация данных
    if not isinstance(data['k'], float):
        return jsonify({"error": "Invalid coefficient type"}), 400

    query = f"UPDATE sup_stat.sups_coef SET country = '{data['country']}', k = {data['k']} WHERE sup_id = {id};"
    client.command(query)
    return jsonify({"success": True})

@settings_sups_bp.route('/sups/coefficients', methods=['POST'])
def add_coefficient():
    data = request.json
    # Проверка входных данных
    if 'sup_id' not in data or 'country' not in data or 'k' not in data:
        return jsonify({"error": "Invalid input"}), 400

    # Валидация данных
    if not isinstance(data['k'], float):
        return jsonify({"error": "Invalid coefficient type"}), 400

    query = f"INSERT INTO sup_stat.sups_coef (sup_id, country, k) VALUES ({data['sup_id']}, '{data['country']}', {data['k']});"
    client.command(query)
    return jsonify({"success": True})

@settings_sups_bp.route('/distinct_countries', methods=['GET'])
def distinct_countries():
    query = "SELECT DISTINCT region FROM sup_stat.region_brand;"
    result = client.query(query).result_rows
    countries = [row[0] for row in result]
    return jsonify(countries)