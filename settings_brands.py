from flask import Blueprint, render_template, request, jsonify
from flask_jwt_extended import jwt_required
from clickhouse_driver import Client
from services.config_info import ClickInfo
from services.class_queries import ClicData
from func_for_async import get_brand_records, delete_brand, add_brand, delete_alias, add_alias
from urllib.parse import urlparse
import json
import pandas as pd
import datetime
settings_brands_bp = Blueprint('settings_brands_bp', __name__)
# Подключение к ClickHouse

# Маршрут для рендеринга HTML страницы

@settings_brands_bp.route('/brands', methods=['GET'])
async def render_brands_page():
    return render_template('brands_records.html')

@settings_brands_bp.route('/brands/all', methods=['GET'])
async def all_brands():
    # Выполняем запрос в ClickHouse для получения всех записей
    client = Client(ClickInfo.host, user=ClickInfo.settings['user'], password=ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})
    formatted_records = await get_brand_records(client)
    return jsonify(formatted_records)


@settings_brands_bp.route('/brands/search', methods=['GET'])
async def search_brands():
    search_brand = request.args.get('query', '').strip()  # Получаем строку поиска из параметра запроса
    client = Client(ClickInfo.host, user=ClickInfo.settings['user'], password=ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={'search_brand' : search_brand})
    formatted_records = await get_brand_records(client)

    return jsonify(formatted_records)

@settings_brands_bp.route('/brands/update', methods=['POST'])
async def update_brands():
    client = Client(ClickInfo.host, user=ClickInfo.settings['user'], password=ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})
    data = request.json
    updated_data = data.get('updated', {})
    deleted_data = data.get('deleted', {})

    # Удаление записей
    for parent_id, fields in deleted_data.items():
        for platform in fields:
            await delete_brand(client, platform, parent_id)

    # Удаление и добавление существующих записей
    for parent_id, fields in updated_data.items():
        for platform, brand in fields.items():
            await delete_brand(client, platform, parent_id)
            await add_brand(client, platform, parent_id, brand)

    return jsonify({"status": "success", "message": "Данные успешно обработаны"}), 200


@settings_brands_bp.route('/brands/update_aliases', methods=['POST'])
async def update_aliases():
    client = Client(ClickInfo.host, user=ClickInfo.settings['user'], password=ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})
    data = request.json
    parent_id = data.get('recordId')
    updated_aliases = data.get('updated', [])
    deleted_aliases = data.get('deleted', [])

    # Удаление старых псевдонимов
    for alias in deleted_aliases:
        await delete_alias(client, parent_id, alias)

    # Добавление новых псевдонимов
    for alias in updated_aliases:
        await add_alias(client, parent_id, alias)

    return jsonify({"status": "success", "message": "Псевдонимы успешно обновлены"}), 200





