from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required
import aiohttp
from services.config_info import ParserInfo
import os
from dotenv import load_dotenv


load_dotenv()
app_key = os.getenv('CLIENT_APP_KEY_ADMIN')
task_management_bp = Blueprint('task_management', __name__)

parser = ParserInfo().host

@task_management_bp.route('/restart_task/<task_id>', methods=['POST'])
@jwt_required()
async def restart_task(task_id):
    async with aiohttp.ClientSession() as session:
        headers = {
            "app-key": app_key
        }
        async with session.post(f'{parser}/restart_task/{task_id}', headers=headers) as response:
            response_json = await response.text()
            print(response_json)
            return jsonify({"error": False}), 200

@task_management_bp.route('/stop_task/<task_id>', methods=['POST'])
@jwt_required()
async def stop_task(task_id):
    async with aiohttp.ClientSession() as session:
        headers = {
            "app-key": app_key
        }
        async with session.post(f'{parser}/stop_task/{task_id}', headers=headers) as response:
            response_json = await response.text()
            print(response_json)
            return jsonify({"error": False}), 200

@task_management_bp.route('/force_delete_task/<task_id>', methods=['DELETE'])
@jwt_required()
async def force_delete_task(task_id):
    async with aiohttp.ClientSession() as session:
        headers = {
            "app-key": app_key
        }
        async with session.delete(f'{parser}/force_delete_task/{task_id}', headers=headers) as response:
            response_json = await response.text()
            print(response_json)
            return jsonify({"error": False}), 200


@task_management_bp.route('/delete_task/<task_id>', methods=['DELETE'])
@jwt_required()
async def delete_task(task_id):
    async with aiohttp.ClientSession() as session:
        headers = {
            "app-key": app_key
        }
        async with session.delete(f'{parser}/delete_task/{task_id}', headers=headers) as response:
            response_json = await response.text()
            print(response_json)
            return jsonify({"error": False}), 200