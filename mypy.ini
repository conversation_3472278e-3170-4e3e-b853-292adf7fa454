[mypy]

# allow arguments with default value None without requiring Optional.
no_implicit_optional = False

#disable_error_code = index

# Disallow redefinition of variables and functions.
# This helps to avoid errors and makes your code more readable and maintainable.
allow_redefinition = false

# Check for untyped definitions.
# This helps to catch errors early and prevent them from causing problems in production.
check_untyped_defs = true

# Disallow incomplete definitions.
# This helps to ensure that your code is always well-defined and complete.
disallow_incomplete_defs = true

# Disallow untyped definitions.
# This helps to improve the type safety of your code and make it less error-prone.
disallow_untyped_defs = true

ignore_missing_imports = true

# Enables incremental type checking.
# This can speed up type checking significantly for large codebases.
incremental = true

# Enables strict checking of optional types.
# This helps to ensure that optional arguments and return values are handled correctly.
strict_optional = true

# Warns about unused `# type: ignore` comments.
# This can help to catch typos and prevent them from causing problems.
warn_unused_ignores = true

# Warns about redundant casts.
# This can help to improve the readability and maintainability of your code.
warn_redundant_casts = true

# Warns about unused mypy configuration options.
# This can help to catch typos and prevent them from causing problems.
warn_unused_configs = true

# Warns about unreachable code.
# This can help to catch errors and improve the quality of your code.
warn_unreachable = true

# Shows a traceback when an error occurs.
# This can be helpful for debugging purposes.
show_traceback = true

# Forces all built-in function and variable names to be uppercase.
# This can help to improve the consistency of your code.
force_uppercase_builtins = true

# Forces mypy to use union types instead of intersection types.
# This can make your code more concise and readable.
force_union_syntax = true

#plugins =
#    mypy_django_plugin.main

[mypy.plugins.django-stubs]
django_settings_module = "mp_monitor.settings"

# Ignore incomplete hints in yaml-stubs
[mypy-yaml.*]
disallow_untyped_defs = false
disallow_incomplete_defs = false

# Turn off mypy for all django migration packages via naming convention.
[mypy-*.migrations.*]
ignore_errors: True