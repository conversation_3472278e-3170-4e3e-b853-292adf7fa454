from flask import Blueprint, request, render_template, jsonify, send_file
import pandas as pd
from urllib.parse import urlparse
from flask_jwt_extended import jwt_required
from io import BytesIO
from clickhouse_driver import Client
from services.config_info import ClickInfo
from services.class_queries import ClicData
from func_for_async import get_recommended_quantities
from werkzeug.utils import secure_filename
demand_monitoring_bp = Blueprint('demand_monitoring', __name__)

@demand_monitoring_bp.route('/demand_quantity_form', methods=['GET'])
@jwt_required()
def demand_quantity_form():
    return render_template('demand_quantity.html')
@demand_monitoring_bp.route('/demand_quantity', methods=['POST'])
@jwt_required()
async def demand_quantity():
    client = Client(ClickInfo.host, user=ClickInfo.settings['user'], password=ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})

    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    try:
        # Secure the filename and check its extension
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[-1].lower()

        if file_extension == 'csv':
            # Read CSV file
            df = pd.read_csv(file)
        elif file_extension in ['xls', 'xlsx']:
            # Read Excel file
            df = pd.read_excel(file, engine='openpyxl' if file_extension == 'xlsx' else 'xlrd')
        else:
            return jsonify({'error': 'Unsupported file format. Please upload a CSV or Excel file.'}), 400

        # Validate columns
        required_columns = ['Бренд', 'Артикул']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': 'Invalid file format. Expected columns: Бренд, Артикул'}), 400

        if df.columns[:2].tolist() != required_columns:
            return jsonify({'error': 'Column names must be Бренд and Артикул in that order'}), 400

        # Generate SKU list and get recommended purchase quantities
        df['sku'] = df['Бренд'] + '-' + df['Артикул']
        sku_list = df['sku'].unique()
        #Лежит url
        o = urlparse(request.base_url)
        result_url = await get_recommended_quantities(client=client, sku_list=sku_list, input_df=df)
        return jsonify({'url': f"http://{o.hostname}:8060/{result_url}"})

    except Exception as e:
        return jsonify({'error': str(e)}), 500
