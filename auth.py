from flask import Blueprint, render_template, request, redirect, url_for, jsonify, make_response
from flask_jwt_extended import create_access_token, create_refresh_token, set_access_cookies, set_refresh_cookies, unset_jwt_cookies
from db import get_user
import json
from datetime import timedelta
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
async def login():
    if request.method == 'POST':
        data = json.loads(request.data)
        username = data['username']
        password = data['password']
        user = get_user(username)
        if user and user[2] == password:  # user[2] - password column
            access_token = create_access_token(identity=username, expires_delta=timedelta(days=5))
            refresh_token = create_refresh_token(identity=username)
            print(access_token)
            print(refresh_token)
            resp = make_response(redirect(url_for('main.home')))
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            return jsonify({'error': 'Invalid username or password'}), 401
    return render_template('login.html')

@auth_bp.route('/logout', methods=['POST'])
def logout():
    response = jsonify({"msg": "logout successful"})
    unset_jwt_cookies(response)
    return response
