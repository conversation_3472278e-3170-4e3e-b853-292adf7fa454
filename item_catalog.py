import os
from typing import Any

import clickhouse_connect
import numpy as np
import pandas as pd
from clickhouse_connect.driver.client import Client
from dotenv import load_dotenv
from flask import Blueprint, render_template, send_file, request, flash, redirect, url_for, current_app
from loguru import logger

from services.config_info import ClickInfo

load_dotenv()
port_nginx = os.getenv('PORT_NGINX')

item_catalog_bp = Blueprint("item_catalog", __name__, template_folder="templates")

# Updated column mapping to match the 'dif.item_catalog' table
COLUMN_MAPPING: dict[str, str] = {
    "Бренд": "brand",
    "Артикул": "part_number",
    "Номенклатура": "nomenclature",
    "Габаритная": "is_bulky",
    "Номенклатура англ.": "name_eng",
    "Вес кг": "weight_kg",
    "ДлинаММ": "length_mm",
    "ВысотаММ": "height_mm",
    "ШиринаММ": "width_mm",
    "Объем, куб.cм": "volume_cm3",
}

def get_clickhouse_client() -> Client:
    """Establishes a connection to the ClickHouse database."""
    if port_nginx == '8060': # production
        host = ClickInfo.host
        user = ClickInfo.settings['user']
        password = ClickInfo.settings['password']
    else:  # test
        host = ClickInfo.host_test
        user = ClickInfo.settings_test['user']
        password = ClickInfo.settings_test['password']

    return clickhouse_connect.get_client(
        host=host,
        user=user,
        password=password,
        connect_timeout=120,
        send_receive_timeout=99999,
    )

def prepare_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """Renames columns, sets data types, and cleans the DataFrame."""
    df.rename(columns=COLUMN_MAPPING, inplace=True)
    
    # Handle missing optional columns
    for col in COLUMN_MAPPING.values():
        if col not in df.columns:
            df[col] = None

    # Explicitly convert string columns to prevent misinterpretation of numeric-like strings
    string_cols = ['brand', 'part_number', 'nomenclature', 'name_eng']
    for col in string_cols:
        if col in df.columns:
            df[col] = df[col].astype(pd.StringDtype())

    # Data type conversions for boolean and numeric
    df['is_bulky'] = df['is_bulky'].apply(lambda x: str(x).lower() in ['true', '1', 'да', 'yes', 'y'])
    
    numeric_cols = ['weight_kg', 'length_mm', 'height_mm', 'width_mm', 'volume_cm3']
    for col in numeric_cols:
        if col in df.columns:
            # First, convert to string and replace commas to handle European-style decimals
            df[col] = df[col].astype(str).str.replace(',', '.', regex=False)
            df[col] = pd.to_numeric(df[col], errors='coerce')
            # Treat 0 as a null/empty value
            df[col] = df[col].replace(0, np.nan)

    # Replace empty strings with NaN to ensure proper handling in default mode
    df.replace('', np.nan, inplace=True)
    
    return df

def run_admin_mode(client: Client, df: pd.DataFrame) -> tuple[int, int]:
    """Directly inserts data, letting ReplacingMergeTree handle overwrites."""
    logger.info("Running admin mode...")
    # Exclude database-managed columns before insert
    cols_to_insert = [col for col in df.columns if col not in ['created_at', 'updated_at']]
    client.insert_df("dif.item_catalog", df[cols_to_insert])
    return len(df), 0

def run_default_mode(client: Client, df: pd.DataFrame) -> tuple[int, int]:
    """Updates only empty/NULL values for existing records and inserts new ones."""
    logger.info("Running default mode...")
    # 1. Get the primary keys from the uploaded file.
    keys = df[['brand', 'part_number']].to_records(index=False).tolist()

    # 2. Fetch all records from the database that match these keys.
    existing_df = client.query_df(
        "SELECT * FROM dif.item_catalog WHERE (brand, part_number) IN %(keys)s",
        {'keys': keys}
    )

    # De-duplicate the data from the database in case of data corruption/stale state
    if not existing_df.empty:
        existing_df.drop_duplicates(subset=['brand', 'part_number'], keep='last', inplace=True)

    # 3. Separate the uploaded data into two DataFrames:
    #    - `new_records_df`: Rows that do not exist in the database.
    #    - `update_df`: Rows that match existing records and need to be merged.
    new_records_df = df[~df.set_index(['brand', 'part_number']).index.isin(existing_df.set_index(['brand', 'part_number']).index)]
    update_df = df[df.set_index(['brand', 'part_number']).index.isin(existing_df.set_index(['brand', 'part_number']).index)]

    inserted_count = len(new_records_df)
    updated_count = 0

    if not existing_df.empty and not update_df.empty:
        # 4. For existing records, implement the "update only if null" logic.
        existing_df.set_index(['brand', 'part_number'], inplace=True)
        update_df.set_index(['brand', 'part_number'], inplace=True)

        # --- Performant Update Count --- #
        is_null_mask = existing_df.isnull()
        is_not_null_mask = update_df.notnull()
        will_update_mask = is_null_mask & is_not_null_mask
        updated_rows_mask = will_update_mask.any(axis=1)
        updated_count = updated_rows_mask.sum()
        # --- End Count --- #

        for col in update_df.columns:
            # The logic: if a value is null in the old data, take the new value from the new data.
            # This may cause a FutureWarning, but it is functionally correct.
            existing_df[col] = existing_df[col].combine_first(update_df[col])
        final_update_df = existing_df.reset_index()
    else:
        final_update_df = pd.DataFrame()

    # 5. Combine the updated records with the brand-new records into one final DataFrame.
    final_df = pd.concat([final_update_df, new_records_df], ignore_index=True)

    # 6. Insert the final DataFrame into the database. ReplacingMergeTree handles the upsert.
    if not final_df.empty:
        cols_to_insert = [col for col in final_df.columns if col not in ['created_at', 'updated_at']]
        client.insert_df("dif.item_catalog", final_df[cols_to_insert])

    return inserted_count, updated_count


@item_catalog_bp.route("/item_catalog/upload", methods=["GET", "POST"])
def upload_page() -> Any:
    if request.method == "POST":
        file = request.files['file']
        if not file or not file.filename:
            flash("Файл не был выбран.", "warning")
            return redirect(request.url)

        if not file.filename.endswith('.xlsx'):
            flash("Неверный формат файла. Пожалуйста, загрузите .xlsx файл.", "danger")
            return redirect(request.url)

        try:
            logger.info(f"Upload request received. Admin mode: {'admin_mode' in request.form}. Form data: {request.form}")
            client = get_clickhouse_client()
            
            df = pd.read_excel(file)
            
            if not all(col in df.columns for col in COLUMN_MAPPING.keys()):
                required_cols_str = ", ".join(COLUMN_MAPPING.keys())
                flash(f"Файл не содержит все необходимые столбцы: {required_cols_str}", "danger")
                return redirect(request.url)

            df = prepare_dataframe(df)
            
            if "admin_mode" in request.form:
                inserted, _ = run_admin_mode(client, df)
                flash(f"Файл успешно обработан в режиме администратора. Строк в файле: {len(df)}. Добавлено/перезаписано строк: {inserted}.", "success")
            else:
                inserted, updated = run_default_mode(client, df)
                flash(f"Файл успешно обработан. Строк в файле: {len(df)}. Добавлено новых строк: {inserted}. Обновлено существующих строк: {updated}.", "success")

        except Exception as e:
            logger.exception("An error occurred during file processing.")
            flash(f"Произошла внутренняя ошибка при обработке файла. Обратитесь к администратору.", "danger")
        
        return redirect(url_for("item_catalog.upload_page"))

    return render_template("item_catalog_upload.html", reports={})


@item_catalog_bp.route("/item_catalog/download_template")
def download_template() -> Any:
    template_path = os.path.join(current_app.root_path, "static", "item_catalog_template.xlsx")
    return send_file(template_path, as_attachment=True)