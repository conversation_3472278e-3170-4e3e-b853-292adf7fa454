from flask_jwt_extended import jwt_required
from flask import Blueprint, render_template, request, jsonify
from clickhouse_driver import Client
from services.config_info import ClickInfo
from services.class_queries import ClicData
from func_for_async import del_sup_list, get_list_of_sups, del_sup_from_list, get_all_sups_names_ids, set_sup_in_list, get_sup_lists

reports_bp = Blueprint('reports', __name__)

#Обработчик выдачи списка поставщиков группы
@reports_bp.route('/list_of_sups/<name>', methods=['GET'])
@jwt_required()
async def get_list_sups(name):
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    settings = {'name': name}
    client = ClicData(client=client, settings=settings, filters={})
    sups = await get_list_of_sups(client)
    return jsonify({'sups': sups})

#Обработчик удаления списка групп поставщиков
@reports_bp.route('/del_sup_list', methods = ['DELETE'])
@jwt_required()
async def del_sup_lists():
    global del_sup_list
    name = request.json['name']
    print(name)
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    settings = {'name': name}
    client = ClicData(client=client, settings=settings, filters={})
    await del_sup_list(client)
    return jsonify({'status': 200})

#Обработчик удаления поставщика из списка
@reports_bp.route('/del_sup_from_list', methods = ['DELETE'])
@jwt_required()
async def del_sup_from_lists():
    global del_sup_from_list
    name = request.json['name']
    dif_id = request.json['dif_id']
    print(name)
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    settings = {'name': name, 'dif_id': dif_id}
    client = ClicData(client=client, settings=settings, filters={})
    await del_sup_from_list(client)
    return jsonify({'status': 200})


#Обработчик добавления поставщика в указанный список
@reports_bp.route('/set_sup_in_list', methods = ['POST'])
@jwt_required()
async def set_sup_in_lists():
    global set_sup_in_list
    settings = request.json
    print(settings)
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings=settings, filters={})
    await set_sup_in_list(client)
    return jsonify({'status': 200})

#Обработчик получения списка поставщиков
@reports_bp.route('/get_all_sups', methods=['GET'])
@jwt_required()
async def get_all_sups():
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})
    sups = await get_all_sups_names_ids(client)
    return jsonify({'sups': sups})

#Обработчик получения списка групп поставщиков
@reports_bp.route('/get_sup_lists', methods=['GET'])
@jwt_required()
async def get_sup_lists_():
    client = Client(ClickInfo.host, user = ClickInfo.settings['user'], password= ClickInfo.settings['password'])
    client = ClicData(client=client, settings={}, filters={})
    sups = list(await get_sup_lists(client))
    return jsonify({'sups': sups})