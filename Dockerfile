# Используем Python 3.12
FROM python:3.12

# Устанавливаем рабочую директорию
WORKDIR /app

# Копируем все файлы в контейнер
COPY . /app

# Устанавливаем зависимости из requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Устанавливаем дополнительные зависимости
RUN pip install numpy
RUN pip install pandas

RUN python3 ./init_db.py

# Указываем переменную окружения для Flask
ENV FLASK_APP=wsgi.py

# Ожидаем, что переменная PORT_WEB будет передана
EXPOSE ${PORT_WEB}

# Используем envsubst для замены переменной PORT_WEB
CMD ["sh", "-c", "exec gunicorn --bind 0.0.0.0:${PORT_WEB} wsgi:app --workers 7 --threads 4 --worker-class=gthread --timeout 9999"]
