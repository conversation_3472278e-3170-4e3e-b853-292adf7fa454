from flask import Blueprint, render_template, jsonify, request, redirect, url_for, make_response
from urllib.parse import urlparse
from flask_jwt_extended import jwt_required
from clickhouse_driver import Client
from services.config_info import ClickInfo, ParserInfo
from services.class_queries import ClicData
from func_for_async import generate_sql_query_percentage, get_sup_lists
from urllib.parse import urlparse
from openpyxl import load_workbook
import pandas as pd
import socket
import zipfile
import os
import io
import json
import tempfile
import aiohttp
from datetime import datetime
from services.config_info import HostInfo, SharedDirectoryHostInfo
from services.sets import type_scheduller_cross
from io import StringIO
from collections import OrderedDict
from pytz import timezone
from dotenv import load_dotenv


load_dotenv()
moscow_tz = timezone('Europe/Moscow')
app_key = os.getenv('CLIENT_APP_KEY_ADMIN')
ean_oem_bp = Blueprint('ean_oem', __name__)


@ean_oem_bp.route('/ean_oem', methods=['GET'])
@jwt_required()
async def parser():
    return render_template('ean_oem.html')

@ean_oem_bp.route('/upload_ean_oem', methods=['POST'])
@jwt_required()
async def get_ean_oem():
    # Получаем файл из формы
    file = request.files.get('excelFile')

    # Проверяем, что файл существует
    if not file:
        return jsonify({'error': 'Файл не загружен'}), 400

    # Получаем имя файла и его расширение
    filename = file.filename
    file_ext = filename.rsplit('.', 1)[1].lower()

    # Обрабатываем .xlsx файл
    if file_ext == 'xlsx':
        try:
            df = pd.read_excel(file, engine='openpyxl')
        except Exception as e:
            return jsonify({'error': f'Ошибка чтения файла: {str(e)}'}), 400

    # Обрабатываем .xls файл
    elif file_ext == 'xls':
        try:
            df = pd.read_excel(file, engine='xlrd')
        except Exception as e:
            return jsonify({'error': f'Ошибка чтения файла: {str(e)}'}), 400

    # Обрабатываем .csv файл
    elif file_ext == 'csv':
        try:
            df = pd.read_csv(file, delimiter=';')
        except pd.errors.EmptyDataError:
            return jsonify({'error': 'Ошибка чтения файла: Прикрепите непустой файл формата .csv'}), 400
        except Exception as e:
            return jsonify({'error': f'Ошибка чтения файла: {str(e)}'}), 400

    # Неподдерживаемый формат файла
    else:
        return jsonify(
            {'error': 'Неподдерживаемый формат файла. Пожалуйста, прикрепите файл формата .xlsx, .xls или .csv'}), 400

    # Проверяем соответствие шаблону
    expected_columns = ['Бренд', 'Артикул']
    df = df.fillna('')
    actual_columns = df.columns.tolist()
    incorrect_columns = [expected_col for expected_col, actual_col in zip(expected_columns, actual_columns) if
                         expected_col != actual_col]
    if incorrect_columns:
        return jsonify(
            {'error': f'Файл не соответствует шаблону. Неверная позиция поля(ей): {", ".join(incorrect_columns)}'}), 400
    if len(expected_columns) > len(actual_columns):
        return jsonify({'error': f'Не хватает столбцов: {", ".join(set(expected_columns) - set(actual_columns))}'}), 400
    if len(expected_columns) < len(actual_columns):
        return jsonify({'error': f'Лишние поля: {", ".join(set(actual_columns) - set(expected_columns))}'}), 400

    # Преобразуем DataFrame в словарь
    df_dict = df.to_dict(orient='records')
    df_json = json.dumps({
        'data': df_dict,
        'filename': filename,
    })
    parser = ParserInfo().host
    async with aiohttp.ClientSession() as session:
        headers = {
            "app-key": app_key
        }
        async with session.post(f'{parser}/start_ean_oem', json=df_json, headers=headers) as response:
            response_json = await response.text()
            print(response_json)
            return jsonify({"error": False}), 200


@ean_oem_bp.route('/task_status_ean_oem', methods=['GET'])
@jwt_required()
async def task_status_ean_oem():
    parser = ParserInfo().host
    flower_url = f"{parser}/tasks/ean_oem"
    async with aiohttp.ClientSession() as session:
        headers = {
            "app-key": app_key
        }
        async with session.get(flower_url, headers=headers) as response:
            tasks_info = await response.json()

            # Сортировка по времени 'received' в убывающем порядке
            sorted_task_ids = sorted(
                tasks_info,
                key=lambda k: datetime.strptime(tasks_info[k].get('received', ''), '%a, %d %b %Y %H:%M:%S GMT')
                if tasks_info[k].get('received') else datetime.min,
                reverse=True
            )

            # Создание OrderedDict для сохранения порядка
            sorted_tasks = OrderedDict((task_id, tasks_info[task_id]) for task_id in sorted_task_ids)

    # Если формат 'json', возвращаем данные
    if request.args.get('format') == 'json':
        return jsonify(sorted_tasks)  # Возвращаем отсортированные данные как JSON

    # Рендерим HTML с отсортированными задачами
    return render_template('tasks_oem_status.html', tasks_info=sorted_tasks)



