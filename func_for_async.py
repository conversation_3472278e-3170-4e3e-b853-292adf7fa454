import datetime

from loguru import logger


async def get_fields(client):
    logger.info("Получение полей.")
    return client.get_fields()


async def get_data_from_dynamic_sql(client):
    logger.info("Получение данных из динамического SQL-запроса.")
    return client.execute_and_save_to_excel()


async def get_sup_lists(client):
    logger.info("Получение списков поставщиков.")
    return client.get_sup_lists()


async def del_sup_list(client):
    logger.info("Удаление списка поставщиков.")
    return client.del_sup_list()


async def get_list_of_sups(client):
    logger.info("Получение списка поставщиков в группе.")
    return client.get_list_sups_group()


async def del_sup_from_list(client):
    logger.info("Удаление поставщика из списка.")
    return client.del_sup_from_list()


async def get_all_sups_names_ids(client):
    logger.info("Получение всех имен и ID поставщиков.")
    return client.get_all_sups_names_ids()


async def set_sup_in_list(client):
    logger.info("Добавление поставщика в список.")
    return client.set_sup_in_list()


async def generate_sql_query_percentage(client):
    logger.info("Генерация SQL-запроса для проценки.")
    return client.generate_sql_query_percentage()


async def check_emex_file(client, df_new):
    logger.info("Проверка файла EMEX.")
    end_date = (datetime.datetime.today().replace(day=1) - datetime.timedelta(days=1)).date()
    start_date = datetime.datetime(2022, 11, 1).date()
    return client.check_discrepancies(df_new, start_date, end_date)


async def save_emex_file(client, df_new):
    logger.info("Сохранение файла EMEX.")
    return client.update_emex_dif(df_new)


async def get_actualization_date(client):
    logger.info("Получение даты актуализации.")
    return client.get_actualization()


async def get_demand_emex_brands(client, brands):
    logger.info(f"Получение потребности EMEX для брендов: {brands}")
    return client.get_demand_emex_brands(brands)


async def get_interval_to_original_months_emex(client, type):
    logger.info(f"Получение интервалов для оригинальных месяцев EMEX, тип: {type}")
    return client.get_interval_to_original_months_emex(type)


async def get_demand_dif_brands(client, brands):
    logger.info(f"Получение потребности DIF для брендов: {brands}")
    return client.get_demand_dif_brands(brands)


async def get_demand_emex_not_filtered(client):
    logger.info("Получение потребности EMEX без фильтров.")
    return client.get_demand_emex_not_filtered()


async def get_demand_dif_not_filtered(client):
    logger.info("Получение потребности DIF без фильтров.")
    return client.get_demand_dif_not_filtered()


async def get_consolidated_demand_with_filters(
    client: "ClicData",
    brands: list[str],
    k_filter: float,
    start_date: str | None = None,
    end_date: str | None = None,
    selected_supids: list[int] | None = None,
) -> dict[str, str | None]:
    """
        Wrapper function to generate the consolidated demand report using the ClicData method.
        Filters for brands, k_, date range, and selected suppliers.
    """
    logger.info("Вызов ClicData.get_consolidated_demand_with_filters...")
    return client.get_consolidated_demand_with_filters(
        brands=brands, k_filter=k_filter, start_date=start_date, end_date=end_date, selected_supids=selected_supids
    )


async def download_summary_sups(client):
    logger.info("Загрузка сводного отчета по поставщикам.")
    return client.download_summary_excel()


async def get_recommended_quantities(client, sku_list, input_df):
    logger.info("Получение рекомендуемых количеств для закупки.")
    return client.get_recommended_purchase_quantities(sku_list, input_df)


async def get_sup_records(client):
    logger.info("Получение записей о поставщиках.")
    return client.get_sup_records()


async def get_brand_records(client):
    logger.info("Получение записей о брендах.")
    return client.get_brand_records()


async def add_brand(client, platform, parent_id, brand):
    logger.info(f"Добавление бренда '{brand}' на платформу '{platform}' с parent_id {parent_id}")
    return client.add_brand(platform, parent_id, brand)


async def delete_brand(client, platform, parent_id):
    logger.info(f"Удаление бренда с parent_id {parent_id} с платформы '{platform}'")
    return client.delete_brand(platform, parent_id)


async def delete_alias(client, parent_id, alias):
    logger.info(f"Удаление псевдонима '{alias}' для parent_id {parent_id}")
    return client.delete_alias(parent_id, alias)


async def add_alias(client, parent_id, alias):
    logger.info(f"Добавление псевдонима '{alias}' для parent_id {parent_id}")
    return client.add_alias(parent_id, alias)


async def add_sup_record(client, data):
    logger.info(f"Добавление записи о поставщике: {data}")
    return client.add_sup_record(data)


async def update_sup_record(client, dif_id, data):
    logger.info(f"Обновление записи о поставщике {dif_id}: {data}")
    return client.update_sup_record(dif_id, data)


async def del_sup_record(client, dif_id):
    logger.info(f"Удаление записи о поставщике {dif_id}")
    return client.del_sup_record(dif_id)


def validate_emex_chunk(chunk: bytes) -> dict:
    """
    Validates a chunk of an EMEX TXT file.

    Args:
        chunk: A bytes object representing the first chunk of the file.

    Returns:
        A dictionary with a success status and an error message if validation fails.
    """
    logger.info("Начало проверки формата чанка EMEX.")
    expected_columns = 16
    try:
        text = chunk.decode("cp1251")
        lines = text.splitlines()

        if len(lines) < 2:
            logger.warning("Проверка не удалась: меньше 2 строк.")
            return {
                "success": False,
                "error": "Файл не содержит достаточного количества строк для проверки (необходим заголовок и хотя бы одна строка данных).",
            }

        # Check header
        header_columns = lines[0].split("\t")
        logger.info(f"В заголовке чанка найдено {len(header_columns)} колонок.")
        if len(header_columns) != expected_columns:
            error_msg = f"Ошибка в заголовке. Ожидалось {expected_columns} колонок, но найдено {len(header_columns)}."
            logger.warning(f"Проверка чанка не удалась: {error_msg}")
            return {"success": False, "error": error_msg}

        # Check first data row
        data_columns = lines[1].split("\t")
        logger.info(f"В первой строке данных чанка найдено {len(data_columns)} колонок.")
        if len(data_columns) != expected_columns:
            error_msg = f"Ошибка в первой строке данных. Ожидалось {expected_columns} колонок, но найдено {len(data_columns)}."
            logger.warning(f"Проверка чанка не удалась: {error_msg}")
            return {"success": False, "error": error_msg}

        logger.info("Проверка чанка EMEX прошла успешно.")
        return {"success": True}

    except UnicodeDecodeError:
        logger.warning("Проверка чанка не удалась: неверная кодировка.")
        return {"success": False, "error": "Ошибка декодирования. Файл должен быть в кодировке cp1251."}
    except Exception as e:
        logger.info(f"Неожиданная ошибка при проверке чанка EMEX: {e}")
        return {"success": False, "error": "Произошла непредвиденная ошибка при проверке чанка."}
