import pandas as pd
from clickhouse_driver import Client

# Шаг 1: Загрузка данных из Excel
file_path = r'C:\Users\<USER>\Desktop\report1.xlsx'  # Укажите путь к вашему файлу Excel
df = pd.read_excel(file_path)

# Шаг 2: Подключение к ClickHouse
client = Client(host='**************', user='torgzap_vlastelin', password='5483her!areA@')  # Замените на адрес вашего ClickHouse

# Получение уникальных брендов и их идентификаторов
unique_brands = client.execute('SELECT brand, id FROM dif.unique_brands')
unique_brands_dict = {brand.upper(): id for brand, id in unique_brands}

# Шаг 3: Обработка данных из Excel
for _, row in df.iterrows():
    brand = str(row['Бренд']).strip()  # Удаляем лишние пробелы
    alias = str(row['Алиас(Синоним)']).strip()

    # Приведение к нижнему регистру для сравнения
    brand_key = brand.upper()
    brand_escaped = brand.replace("'", "''")
    # Проверка на существование бренда в уникальных брендах
    if brand_key not in unique_brands_dict:
        # Проверка на существование бренда в zap_brand_cross
        existing_parent_id = client.execute(f"SELECT parent_id FROM dif.zap_brand_cross WHERE brand = '{brand_escaped}'")
        
        if existing_parent_id:
            # Если бренд найден, используем его parent_id
            new_id = existing_parent_id[0][0]
        else:
            # Получение максимального id
            max_id = client.execute('SELECT max(id) FROM dif.unique_brands')[0][0]
            new_id = max_id + 1 if max_id is not None else 1  # Приращение id

            # Вставка нового бренда
            client.execute('INSERT INTO dif.unique_brands (brand, id) VALUES', [(brand, new_id)])

        # Обновление словаря уникальных брендов
        unique_brands_dict[brand_key] = new_id
    else:
        new_id = unique_brands_dict[brand_key]

    # Вставка алиаса в таблицу zap_brand_cross
    # Проверка на дубликаты перед вставкой
    existing_aliases = client.execute(f'SELECT brand FROM dif.zap_brand_cross WHERE parent_id = {new_id}')
    existing_aliases_set = {alias[0].upper() for alias in existing_aliases}

    if alias.upper() not in existing_aliases_set:
        client.execute('INSERT INTO dif.zap_brand_cross (brand, parent_id) VALUES', [(alias, new_id)])

print("Данные успешно загружены в ClickHouse.")


