from clickhouse_driver import Client
from flask import Blueprint, render_template, redirect, url_for, request, jsonify
from flask_jwt_extended import jwt_required
from flask_jwt_extended.exceptions import NoAuthorizationError

from func_for_async import get_actualization_date
from services.class_queries import ClicData
from services.config_info import ClickInfo
from services.sets import reports

main_bp = Blueprint("main", __name__)


@main_bp.errorhandler(NoAuthorizationError)
def handle_auth_error(error):
    print("Authorization error occurred:", error)

    return redirect(url_for("auth.login"))


@main_bp.route("/", methods=["GET", "POST"])
@jwt_required()
async def home():
    try:
        if request.method == "POST":
            report_type = request.form.get("report_type", "")
            if report_type:
                return redirect(url_for("filters.filter", filter=report_type))
        return render_template("index.html", reports=reports)
    except:
        return redirect(url_for("auth.login"))


@main_bp.route("/demand_report", methods=["GET"])
@jwt_required()
def demand_report():
    return render_template("demand_report.html")


@main_bp.route("/get_price_stats", methods=["GET"])
@jwt_required()
async def get_price_stats():
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings={}, filters={})
    actualization_sups = await get_actualization_date(client)
    return jsonify({"data": actualization_sups})


@main_bp.route("/parser_history", methods=["GET", "POST"])
@jwt_required()
def parser_history():
    if request.method == "POST":
        try:
            coefficient = float(request.form.get("coefficient", ""))
            # Здесь можно обработать коэффициент, если требуется
            # Например, вызвать функцию для получения отчёта
            return jsonify({"message": "Report generation started", "coefficient": coefficient})
        except ValueError:
            return jsonify({"error": "Invalid input"}), 400
    return render_template("parser_history.html")
