from os import environ
from urllib.parse import urlparse

from clickhouse_driver import Client
from flask import Blueprint, render_template, jsonify, request, redirect, url_for
from flask_jwt_extended import jwt_required
from flask_jwt_extended.exceptions import NoAuthorizationError

from custom_exceptions import QueryUsageLimit
from func_for_async import get_fields, get_data_from_dynamic_sql
from services.class_queries import ClicData
from services.config_info import ClickInfo
from services.dict_tranform import transform_cortage_for_fields
from services.sets import reports

filters_bp = Blueprint("filters", __name__)


@filters_bp.errorhandler(NoAuthorizationError)
def internal_error(error):
    return redirect(url_for("auth.login"))


@filters_bp.route("/filters/<filter>", methods=["GET"])
@jwt_required()
async def filter(filter):
    settings = reports[f"{filter}"]
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings=settings, filters={})
    fields_values_for_filter = await get_fields(client=client)
    fields_values_for_filter = transform_cortage_for_fields(fields_values_for_filter)
    print(fields_values_for_filter)
    return render_template("filters.html", fields_values_for_filter=fields_values_for_filter, report_type=filter)


@filters_bp.route("/apply_filters", methods=["POST"])
@jwt_required()
async def apply_filters():
    selected_filters = {}
    params = request.get_json()
    print(params)
    settings = reports[params["report_type"]]
    settings["filters"] = params["filters"]
    settings["report_type"] = params["report_type"]
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings=settings, filters={})
    o = urlparse(request.base_url)
    try:
        port = environ.get("PORT_NGINX", "8060")
        file_name = f"http://{o.hostname}:{port}/static/{await get_data_from_dynamic_sql(client=client)}"
    except (QueryUsageLimit, ValueError):
        return jsonify({"error": "Слишком большая выборка. Добавьте фильтры"})
    return jsonify({"file_url": file_name})
