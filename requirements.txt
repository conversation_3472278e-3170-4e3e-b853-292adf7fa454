dask[dataframe]
aioflask==0.4.0
aiohttp==3.9.5
aiosignal==1.3.1
annotated-types==0.7.0
anyio==3.7.1
asgiref==3.8.1
attrs==23.2.0
blinker==1.8.2
cachetools==5.3.2
certifi==2024.2.2
charset-normalizer==3.3.2
click==8.1.7
clickhouse-driver==0.2.6
colorama==0.4.6
et-xmlfile==1.1.0
fastapi==0.104.1
Flask==3.0.3
Flask-Cors==4.0.1
Flask-JWT-Extended==4.6.0
frozenlist==1.4.1
greenlet==3.0.3
greenletio==0.11.0
h11==0.14.0
idna==3.7
isort==6.0.0b2
itsdangerous==2.2.0
Jinja2==3.1.4
loguru==0.7.2
MarkupSafe==2.1.5
multidict==6.0.5
numpy
openpyxl==3.1.4
orjson==3.9.10
pandas
pydantic==2.7.1
PyJWT==2.8.0
python-dateutil==2.9.0.post0
pytz==2024.1
requests==2.31.0
six==1.16.0
sniffio==1.3.1
starlette==0.27.0
typing_extensions==4.12.0
tzdata==2024.1
tzlocal==5.2
urllib3==2.2.1
uvicorn==0.24.0.post1
Werkzeug==3.0.3
win32-setctime==1.1.0
wtf==0.1
yarl==1.9.4
psutil
gunicorn
clickhouse-connect
dask
xlrd
xlsxwriter
python-dotenv
pytz
dask[dataframe]
redis==4.5.1
pytest==8.3.5
pytest-mock==3.15.0
pyarrow==21.0.0
tqdm==4.67.1