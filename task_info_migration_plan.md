# Task Information Migration Plan: Redis to PostgreSQL

## Overview
This document outlines the plan for migrating task information storage from Redis to PostgreSQL. The goal is to ensure that task information is properly stored in PostgreSQL and that the task status page in the admin interface displays all tasks correctly.

## Current Issues
- Task information stored in Redis is being lost
- The HTML table in admin_flask service that displays task status is not showing all tasks
- The issue started after Redis was introduced

## Solution Approach
Create a PostgreSQL table to store task information and modify the code to use this table instead of Redis for task information storage and retrieval.

## 1. Database Schema Changes

### 1.1 Create New Task Info Table in PostgreSQL

Add the following table to `config/postgres/init.sql` in the parser repository:

```sql
CREATE TABLE IF NOT EXISTS task_info (
    task_id VARCHAR(255) PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    platforms JSONB NOT NULL,
    status VARCHAR(50) NOT NULL,
    result_url TEXT,
    rows_without_result JSONB,
    received_date TIMESTAMP,
    completed_date TIMES<PERSON>MP,
    runtime VARCHAR(50),
    pid INTEGER,
    auto BOOLEAN NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for frequently queried fields
CREATE INDEX IF NOT EXISTS idx_task_info_status ON task_info(status);
CREATE INDEX IF NOT EXISTS idx_task_info_source_type ON task_info(source_type);
CREATE INDEX IF NOT EXISTS idx_task_info_auto ON task_info(auto);
```

Note: We're not including a `progress` field in the table since progress is updated frequently and is better handled through the existing Redis mechanism. The PostgreSQL table will store persistent task information, while real-time progress updates will continue to use Redis.

### 1.2 Keep Existing task_pid_table for Backward Compatibility

Keep the existing `task_pid_table` for backward compatibility during the transition.

## 2. Changes in Parser Repository

### 2.1 Create TaskInfoDB Class in src/general/db.py

Create a simple class to handle database operations for task information:

```python
class TaskInfoDB:
    """Database operations for task information."""
    
    @staticmethod
    async def save_task_info(task_id, task_info):
        """Save task information to PostgreSQL."""
        try:
            async with db_connection() as conn:
                # Convert platforms to JSON if it's a list
                platforms = task_info.get('platforms', [])
                if isinstance(platforms, list):
                    platforms_json = json.dumps(platforms)
                else:
                    platforms_json = json.dumps([platforms]) if platforms else json.dumps([])
                
                # Convert rows_without_result to JSON if it exists
                rows_without_result = task_info.get('rows_without_result')
                if rows_without_result is not None:
                    rows_without_result_json = json.dumps(rows_without_result)
                else:
                    rows_without_result_json = None
                
                # Parse dates
                received_date = None
                if task_info.get('received'):
                    try:
                        received_date = datetime.strptime(task_info['received'], '%a, %d %b %Y %H:%M:%S GMT')
                    except Exception as e:
                        logging.warning(f"Failed to parse received date: {e}")
                
                completed_date = None
                if task_info.get('succeeded'):
                    try:
                        completed_date = datetime.strptime(task_info['succeeded'], '%a, %d %b %Y %H:%M:%S GMT')
                    except Exception as e:
                        logging.warning(f"Failed to parse completed date: {e}")
                
                # Insert or update task info
                await conn.execute("""
                    INSERT INTO task_info (
                        task_id, filename, platforms, status,
                        result_url, rows_without_result, received_date, completed_date,
                        runtime, pid, auto, source_type
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    ON CONFLICT (task_id) DO UPDATE SET
                        filename = EXCLUDED.filename,
                        platforms = EXCLUDED.platforms,
                        status = EXCLUDED.status,
                        result_url = EXCLUDED.result_url,
                        rows_without_result = EXCLUDED.rows_without_result,
                        received_date = EXCLUDED.received_date,
                        completed_date = EXCLUDED.completed_date,
                        runtime = EXCLUDED.runtime,
                        pid = EXCLUDED.pid,
                        auto = EXCLUDED.auto,
                        source_type = EXCLUDED.source_type
                """, 
                task_id, 
                task_info.get('filename', 'N/A'),
                platforms_json,
                task_info.get('status', 'UNKNOWN'),
                task_info.get('url', ''),
                rows_without_result_json,
                received_date,
                completed_date,
                task_info.get('runtime', 'N/A'),
                task_info.get('pid', None),
                task_info.get('auto', False),
                task_info.get('source_type', 'unknown')
                )
                
                logging.debug(f"Task {task_id} info saved to PostgreSQL")
                return True
        except Exception as e:
            logging.error(f"Error saving task info to PostgreSQL: {e}")
            return False
    
    @staticmethod
    async def get_task_info(task_id):
        """Get task information from PostgreSQL by task_id."""
        try:
            async with db_connection() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM task_info WHERE task_id = $1
                """, task_id)
                
                if row:
                    # Convert row to dict
                    task_info = dict(row)
                    
                    # Convert JSONB fields back to Python objects
                    if task_info.get('platforms'):
                        task_info['platforms'] = json.loads(task_info['platforms'])
                    
                    if task_info.get('rows_without_result'):
                        task_info['rows_without_result'] = json.loads(task_info['rows_without_result'])
                    
                    # Format dates for consistency with Redis format
                    if task_info.get('received_date'):
                        task_info['received'] = task_info['received_date'].strftime('%a, %d %b %Y %H:%M:%S GMT')
                    
                    if task_info.get('completed_date'):
                        task_info['succeeded'] = task_info['completed_date'].strftime('%a, %d %b %Y %H:%M:%S GMT')
                    
                    # Rename url field for consistency
                    if 'result_url' in task_info:
                        task_info['url'] = task_info.pop('result_url')
                    
                    return task_info
                return None
        except Exception as e:
            logging.error(f"Error getting task info from PostgreSQL: {e}")
            return None
    
    @staticmethod
    async def get_all_tasks(source_type=None, auto=None):
        """Get all task information from PostgreSQL with optional filtering."""
        try:
            async with db_connection() as conn:
                query = "SELECT * FROM task_info"
                params = []
                
                # Add filters if provided
                conditions = []
                if source_type:
                    conditions.append("source_type = $1")
                    params.append(source_type)
                
                if auto is not None:
                    param_index = len(params) + 1
                    conditions.append(f"auto = ${param_index}")
                    params.append(auto)
                
                if conditions:
                    query += " WHERE " + " AND ".join(conditions)
                
                # Add order by created_at desc
                query += " ORDER BY created_at DESC"
                
                rows = await conn.fetch(query, *params)
                
                tasks = {}
                for row in rows:
                    task_id = row['task_id']
                    task_info = dict(row)
                    
                    # Convert JSONB fields back to Python objects
                    if task_info.get('platforms'):
                        task_info['platforms'] = json.loads(task_info['platforms'])
                    
                    if task_info.get('rows_without_result'):
                        task_info['rows_without_result'] = json.loads(task_info['rows_without_result'])
                    
                    # Format dates for consistency with Redis format
                    if task_info.get('received_date'):
                        task_info['received'] = task_info['received_date'].strftime('%a, %d %b %Y %H:%M:%S GMT')
                    
                    if task_info.get('completed_date'):
                        task_info['succeeded'] = task_info['completed_date'].strftime('%a, %d %b %Y %H:%M:%S GMT')
                    
                    # Rename url field for consistency
                    if 'result_url' in task_info:
                        task_info['url'] = task_info.pop('result_url')
                    
                    tasks[task_id] = task_info
                
                return tasks
        except Exception as e:
            logging.error(f"Error getting all tasks from PostgreSQL: {e}")
            return {}
    
    @staticmethod
    async def delete_task_info(task_id):
        """Delete task information from PostgreSQL."""
        try:
            async with db_connection() as conn:
                await conn.execute("DELETE FROM task_info WHERE task_id = $1", task_id)
                logging.debug(f"Task {task_id} info deleted from PostgreSQL")
                return True
        except Exception as e:
            logging.error(f"Error deleting task info from PostgreSQL: {e}")
            return False
```

### 2.2 Modify Celery Task Functions to Save Task Info

Update the Celery task functions in `src/flask_api/tasks.py` to save task information to PostgreSQL at the beginning and end of task execution:

```python
@celery.task(bind=True)
def scrape_data(self, settings):
    async def _scrape_data():
        # Сохранение PID задачи в Redis
        pid = os.getpid()
        task_key = f"task_pid:{self.request.id}"
        redis_client.set(task_key, pid)
        logging.debug(f"Task {self.request.id} PID saved to Redis: {pid}")

        # Сохранение PID задачи в PostgreSQL
        try:
            async with db_connection() as conn:
                await conn.execute(
                    "INSERT INTO task_pid_table (task_id, pid) VALUES ($1, $2)",
                    self.request.id, pid
                )
                logging.debug(f"Task {self.request.id} PID saved to PostgreSQL: {pid}")
                
                # Save initial task info to task_info table
                task_info = {
                    'filename': settings.get('filename', 'N/A'),
                    'platforms': settings.get('platforms', []),
                    'status': 'STARTED',
                    'url': '',
                    'rows_without_result': None,
                    'pid': pid,
                    'auto': settings.get('auto', False),
                    'source_type': settings.get('source_type', 'parser'),
                    'received': settings.get('received', datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT'))
                }
                await TaskInfoDB.save_task_info(self.request.id, task_info)
        except Exception as e:
            logging.error(f"Error saving task info to PostgreSQL: {e}")

        try:
            logger.debug("Старт таска парсеров")
            progress, rows_without_result, data_to_write = await scrape_data_async(self, settings)
            export_analogs = settings['filters'].get('export_analogs', False)
            file_name = create_excel_file(data_to_write, True if export_analogs else False)
            result_url = f"{parser}/static/{file_name}"
            
            # Update task info in PostgreSQL with final results
            try:
                task_info = {
                    'filename': settings.get('filename', 'N/A'),
                    'platforms': settings.get('platforms', []),
                    'status': 'SUCCESS',
                    'url': result_url,
                    'rows_without_result': rows_without_result,
                    'pid': pid,
                    'auto': settings.get('auto', False),
                    'source_type': settings.get('source_type', 'parser'),
                    'received': settings.get('received', datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')),
                    'succeeded': datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
                }
                await TaskInfoDB.save_task_info(self.request.id, task_info)
            except Exception as e:
                logging.error(f"Error updating task info in PostgreSQL: {e}")

            return {'progress': progress, 'url': result_url, 'rows_without_result': rows_without_result}
        except Exception as e:
            # Update task info in PostgreSQL with error status
            try:
                task_info = {
                    'filename': settings.get('filename', 'N/A'),
                    'platforms': settings.get('platforms', []),
                    'status': 'FAILURE',
                    'url': '',
                    'rows_without_result': None,
                    'pid': pid,
                    'auto': settings.get('auto', False),
                    'source_type': settings.get('source_type', 'parser'),
                    'received': settings.get('received', datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')),
                    'succeeded': datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
                }
                await TaskInfoDB.save_task_info(self.request.id, task_info)
            except Exception as db_error:
                logging.error(f"Error updating task info in PostgreSQL: {db_error}")
                
            logging.error(f"Error task {e}")
            raise
        finally:
            # Удаление PID из Redis после завершения задачи
            redis_client.delete(task_key)
            logging.debug(f"Task {self.request.id} PID removed from Redis")

    loop = asyncio.get_event_loop()
    return loop.run_until_complete(_scrape_data())
```

Apply similar changes to the other Celery task functions: `scrape_data_history`, `generate_sql_query_percentage`, and `scrape_ean_oem`.

### 2.3 Update Task Management Functions

Modify the task management functions in `src/flask_api/utils.py` to also update the `task_info` table:

```python
async def delete_task_metadata(self, task_id):
    """
    Удаляет метаданные задачи из Redis и PostgreSQL.
    """
    task_key = f"celery-task-meta-{task_id}"
    if self.redis_client_db1.exists(task_key):
        self.redis_client_db1.delete(task_key)
        logger.info(f"Task {task_id} deleted from Redis.")

        # Удаление из PostgreSQL task_pid_table и task_info
        try:
            async with db_connection() as conn:
                await conn.execute(
                    "DELETE FROM task_pid_table WHERE task_id = $1",
                    task_id
                )
                logger.info(f"Task {task_id} deleted from PostgreSQL task_pid_table.")
                
                await conn.execute(
                    "DELETE FROM task_info WHERE task_id = $1",
                    task_id
                )
                logger.info(f"Task {task_id} deleted from PostgreSQL task_info.")
        except Exception as e:
            logger.error(f"Error deleting task from PostgreSQL: {e}")

        return f"Task {task_id} deleted successfully."
    else:
        logger.warning(f"Task {task_id} not found in Redis.")
        return f"Task {task_id} not found in Redis."
```

Apply similar changes to `force_delete_task` and `stop_task` methods.

### 2.4 Modify API Endpoints to Use PostgreSQL

Update the API endpoints in `src/flask_api/app.py` to fetch task information from PostgreSQL:

```python
@app.route('/tasks', methods=['GET'])
@require_app_key
async def get_tasks():
    try:
        auto_value = request.args.get('auto', 'True')
        
        # First try to get tasks from Redis for real-time progress
        tasks_progress = await fetch_tasks_progress(auto_value, source_type='parser')
        
        # Then get tasks from PostgreSQL for persistent data
        postgres_tasks = await TaskInfoDB.get_all_tasks(source_type='parser', auto=(auto_value.lower() == 'true'))
        
        # Merge the data, preferring Redis data for active tasks
        for task_id, task_info in postgres_tasks.items():
            if task_id not in tasks_progress:
                # Only add from PostgreSQL if not in Redis
                tasks_progress[task_id] = task_info
        
        return jsonify(tasks_progress)
    except Exception as e:
        logger.exception(f"progress error: {str(e)}")
        return jsonify({"error": str(e)}), 500
```

Apply similar changes to the other task endpoints: `get_task_history`, `get_tasks_percentage_cross`, and `get_tasks_ean_oem`.

## 3. Changes in Admin_Flask Repository

No changes are needed in the admin_flask repository since it already fetches task information through API calls to the parser service. The changes in the parser service will ensure that the admin_flask service receives the correct task information.

## 4. Migration Strategy

1. **Deploy Database Changes**:
   - Add the new `task_info` table to PostgreSQL
   - Keep the existing `task_pid_table` for backward compatibility

2. **Deploy Code Changes**:
   - Update the parser service to save task information to PostgreSQL
   - Update the API endpoints to fetch task information from both Redis and PostgreSQL

3. **Verify**:
   - Check that new tasks are properly saved to PostgreSQL
   - Verify that the admin_flask service displays all tasks correctly

4. **Monitor**:
   - Monitor the system for any issues
   - Check that task information is not being lost

## 5. Future Improvements

1. **Complete Migration to PostgreSQL**:
   - Once the system is stable, consider migrating all task-related data to PostgreSQL
   - Remove Redis dependencies for task information storage

2. **Data Cleanup**:
   - Implement a cleanup job to remove old task information from PostgreSQL
   - Add a `last_updated` field to track when task information was last updated

3. **Performance Optimization**:
   - Monitor database performance and optimize queries if needed
   - Consider adding more indexes if query performance is an issue
