version: '3.8'

services:
  web:
    build: .
    expose:
      - "${PORT_WEB}"
    volumes:
      - ./static:/app/static
      - ./logs:/app/logs
    environment:
      FLASK_ENV: development
      PORT_WEB: ${PORT_WEB}
      CLIENT_APP_KEY_ADMIN: ${CLIENT_APP_KEY_ADMIN}
      REDIS_HOST: redis
      REDIS_PORT: 6379
    restart: always
    depends_on:
      - redis

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    environment:
      PORT_NGINX: ${PORT_NGINX}
      PORT_WEB: ${PORT_WEB}
    volumes:
      - ./static:/app/static
    ports:
      - "${PORT_NGINX}:${PORT_NGINX}"
    depends_on:
      - web
    restart: always

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: always

volumes:
  redis_data:
