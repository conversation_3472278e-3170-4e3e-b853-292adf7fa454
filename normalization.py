from flask import Blueprint, render_template, jsonify, request, redirect, url_for, make_response
from urllib.parse import urlparse
from flask_jwt_extended import jwt_required
from flask_jwt_extended.exceptions import NoAuthorizationError
from clickhouse_driver import Client
from services.sets import reports
from services.config_info import ClickInfo
from services.class_queries import ClicData
from services.dict_tranform import transform_cortage_for_fields
from func_for_async import get_fields, get_data_from_dynamic_sql
from custom_exceptions import QueryUsageLimit
from click_queries import get_client_no_settings
import pandas as pd
import json
import asyncio
import concurrent.futures
normalization_bp = Blueprint('normalization', __name__)

@normalization_bp.errorhandler(NoAuthorizationError)
def internal_error(error):
    return redirect(url_for('auth.login'))


@normalization_bp.route('/normalization', methods=['GET'])
@jwt_required()
async def normalization(filter):
    settings = reports[f'{filter}']
    client = Client(ClickInfo.host, settings=ClickInfo.settings)
    client = ClicData(client=client, settings=settings, filters={})
    fields_values_for_filter = await get_fields(client=client)
    fields_values_for_filter = transform_cortage_for_fields(fields_values_for_filter)
    return render_template('filters.html', fields_values_for_filter=fields_values_for_filter, report_type=filter)


def build_brand_mapping(client: Client, brands: list[str]) -> dict[str, str]:
    """
    Для списка уникальных brands возвращает словарь {оригинал: нормализованное}.
    Если для бренда нет соответствия в таблицах cross/unique_brands, 
    возвращается сама строка-ключ.
    """
    # Подготовим входной список в виде ('brand1','brand2',...)
    # и разобьём на чанки по, скажем, 500 значений, чтобы не было проблем с слишком длинным IN.
    mapping: dict[str,str] = {}
    chunk_size = 500
    for i in range(0, len(brands), chunk_size):
        chunk = brands[i:i+chunk_size]
        # Экранируем одинарные кавычки
        safe = [b.replace("'", "\\'") for b in chunk]
        in_list = ",".join(f"'{b}'" for b in safe)

        query = f"""
        SELECT 
          lower(zc.brand) AS orig,
          ub.brand        AS norm
        FROM dif.zap_brand_cross zc
        JOIN dif.unique_brands ub 
          ON zc.parent_id = ub.id
        WHERE lower(zc.brand) IN ({in_list})
        """
        rows = client.execute(query)
        # rows = list of (orig, norm)
        for orig, norm in rows:
            mapping[orig] = norm

    # для тех, которых не оказалось в mapping, пусть значением будет сама исходная строка
    return mapping

def normalize_brands_in_df(df: pd.DataFrame, brand_col_name: str) -> pd.DataFrame:
    """
    Берёт df с колонкой 'brand', возвращает копию с нормализованной колонкой.
    """
    client = get_client_no_settings()

    # 1) Собираем уникальные бренды из датафрейма
    unique_brands = df[brand_col_name].dropna().astype(str).str.lower().unique().tolist()

    # 2) Строим словарь нормализации
    mapping = build_brand_mapping(client, unique_brands)

    # 3) Применяем map() к колонке (приводим тоже к lower, чтобы key lookup работал)
    df = df.copy()
    df[brand_col_name] = (
        df[brand_col_name]
        .fillna('')
        .astype(str)
        .str.lower()
        .map(mapping)             # None, если не было ключа
        .fillna(df[brand_col_name])      # в None заменяем на оригинал
    )

    client.disconnect()
    return df

def normalize_brands(df: pd.DataFrame) -> pd.DataFrame:
    settings = {'use_numpy': True, 'user': 'torgzap_vlastelin', 'password': '5483her!areA@'}
    client = Client('87.242.110.159', user=settings['user'], password=settings['password'])
    # Шаг 1: Получаем данные из ClickHouse
    zap_brand_cross_query = "SELECT brand, parent_id FROM dif.zap_brand_cross"
    unique_brands_query = "SELECT id, brand FROM dif.unique_brands"

    zap_brand_cross_df = pd.DataFrame(
        client.execute(zap_brand_cross_query), 
        columns=['brand', 'parent_id']
    )
    unique_brands_df = pd.DataFrame(
        client.execute(unique_brands_query), 
        columns=['id', 'brand']
    )

    # Шаг 2: Переименовываем колонку для ясности
    unique_brands_df.rename(columns={'brand': 'normalized_brand'}, inplace=True)

    # Соединяем таблицы по parent_id = id
    brand_mapping = zap_brand_cross_df.merge(
        unique_brands_df, left_on='parent_id', right_on='id'
    )[["brand", "normalized_brand"]]

    # Шаг 3: Создаем словарь для замены с ключами в нижнем регистре
    brand_dict = {
        key.lower(): value 
        for key, value in brand_mapping.set_index("brand")["normalized_brand"].to_dict().items()
    }

    # Шаг 4: Заменяем данные в столбце 'b', используя сравнение без учёта регистра.
    # Если нормализованное значение не найдено, оставляем исходное.
    df["brand"] = df["brand"].apply(lambda x: brand_dict.get(str(x).lower(), x))

    # Результат
    client.disconnect()
    return df

def normalize_brands_b(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    settings = {'use_numpy': True, 'user': 'torgzap_vlastelin', 'password': '5483her!areA@'}
    client = Client('87.242.110.159', user=settings['user'], password=settings['password'])
    # Шаг 1: Получаем данные из ClickHouse
    zap_brand_cross_query = "SELECT brand, parent_id FROM dif.zap_brand_cross"
    unique_brands_query = "SELECT id, brand FROM dif.unique_brands"

    zap_brand_cross_df = pd.DataFrame(
        client.execute(zap_brand_cross_query), 
        columns=['brand', 'parent_id']
    )
    unique_brands_df = pd.DataFrame(
        client.execute(unique_brands_query), 
        columns=['id', 'brand']
    )

    # Шаг 2: Переименовываем колонку для ясности
    unique_brands_df.rename(columns={'brand': 'normalized_brand'}, inplace=True)

    # Соединяем таблицы по parent_id = id
    brand_mapping = zap_brand_cross_df.merge(
        unique_brands_df, left_on='parent_id', right_on='id'
    )[["brand", "normalized_brand"]]

    # Шаг 3: Создаем словарь для замены с ключами в нижнем регистре
    brand_dict = {
        key.lower(): value 
        for key, value in brand_mapping.set_index("brand")["normalized_brand"].to_dict().items()
    }

    # Шаг 4: Заменяем данные в столбце 'b', используя сравнение без учёта регистра.
    # Если нормализованное значение не найдено, оставляем исходное.
    df["b"] = df["b"].apply(lambda x: brand_dict.get(str(x).lower(), x))

    # Результат
    client.disconnect()
    return df
