/* Общие стили */
body {
    font-family: Arial, sans-serif;
    font-size: 18px;
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
    overflow-y: auto;
}

.container {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    background: var(--container-bg);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    transition: background-color 0.3s ease;
}

.form-group {
    margin-bottom: 15px;
}

.excluded-providers {
    margin-top: 10px;
    max-height: 150px; /* Добавлен скролл для исключённых поставщиков */
    overflow-y: auto; /* Вертикальный скролл */
    border: 1px solid #ddd;
    padding: 10px;
    background: #fff;
    border-radius: 5px;
}

#dynamicForms {
    max-height: 300px; /* Добавлен скролл для секции форм */
    overflow-y: auto; /* Вертикальный скролл */
}

.form-group label {
    display: block;
    margin-bottom: 10px;
}

.form-group input[type="file"],
.form-group select,
.form-group input[type="number"] {
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 16px;
    box-sizing: border-box;
}

.theme-toggle {
    position: fixed;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000; /* Поместить выше других элементов */
}

.theme-toggle input[type="checkbox"] {
    display: none;
}

.theme-toggle label {
    cursor: pointer;
    font-size: 24px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    color: var(--toggle-color);
    transition: color 0.3s ease;
}

.theme-toggle label:hover {
    color: var(--toggle-hover-color);
}

#responseMessage {
    margin-top: 20px;
    font-weight: bold;
    text-align: center;
    display: none;
}

.loader {
    border: 8px solid #f3f3f3;
    border-top: 8px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
    margin-top: 20px;
    display: none;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

:root {
    --bg-color-light: #f4f4f4;
    --text-color-light: #333;
    --container-bg-light: #fff;
    --toggle-color-light: #f1c40f;
    --toggle-hover-color-light: #e67e22;

    --bg-color-dark: #333;
    --text-color-dark: #f4f4f4;
    --container-bg-dark: #444;
    --toggle-color-dark: #f39c12;
    --toggle-hover-color-dark: #e74c3c;
}

body {
    font-family: 'Arial', sans-serif;
    transition: background-color 0.3s, color 0.3s;
}

body.light-theme {
    --bg-color: var(--bg-color-light);
    --text-color: var(--text-color-light);
    --container-bg: var(--container-bg-light);
    --toggle-color: var(--toggle-color-light);
    --toggle-hover-color: var(--toggle-hover-color-light);
}

.container {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    background: var(--container-bg);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    transition: background-color 0.3s ease;
    max-height: calc(100vh - 40px); /* Высота контейнера, с учетом отступов */
    overflow-y: auto; /* Добавляет вертикальный скролл, если контент превышает высоту */
}

body.dark-theme {
    --bg-color: var(--bg-color-dark);
    --text-color: var(--text-color-dark);
    --container-bg: var(--container-bg-dark);
    --toggle-color: var(--toggle-color-dark);
    --toggle-hover-color: var(--toggle-hover-color-dark);
}

.instructions {
    margin-top: 10px;
    font-size: 16px;
    color: #666;
}

.error-message {
    color: red;
    font-size: 16px;
    margin-top: 10px;
}

.provider-list {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.provider-list input {
    flex: 1;
    padding: 8px;
    font-size: 16px;
}

.provider-list button {
    margin-left: 10px;
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 16px;
    border-radius: 5px;
}

.provider-list button:hover {
    background-color: #c82333;
}

.provider-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 100%;
    box-sizing: border-box;
}

.provider-item span {
    flex-grow: 1;
}

.provider-item button {
    margin-left: 10px;
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 16px;
    border-radius: 5px;
}

#platformFiltersContainer {
    max-height: 300px;
    overflow-y: auto; /* Добавлен вертикальный скролл */
}

@media (max-width: 600px) {
    .container {
        padding: 15px;
    }

    .form-group input,
    .form-group select,
    .form-group button {
        font-size: 14px;
    }

    .theme-toggle label {
        font-size: 20px;
    }

    .loader {
        width: 40px;
        height: 40px;
    }
}

.return-button {
    display: inline-block;
    padding: 10px 20px;
    font-size: 16px;
    color: #fff;
    background-color: #007BFF;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    margin: 5px 0 0 5px;
}

.return-button:hover {
    background-color: #0056b3;
}

/* Custom styles for excluded providers */

/* Add subtle background colors to accordion headers for better distinction */
#excludedProvidersAccordion .accordion-button {
    background-color: #f8f9fa; /* Very light gray */
}

/* Different subtle colors for each parser */
#headingEmex .accordion-button:not(.collapsed),
#headingEmex .accordion-button.collapsed {
    border-left: 4px solid #007bff; /* Bootstrap primary */
}

#headingPart_kom .accordion-button:not(.collapsed),
#headingPart_kom .accordion-button.collapsed {
    border-left: 4px solid #28a745; /* Bootstrap success */
}

#headingPart_kom_OPT .accordion-button:not(.collapsed),
#headingPart_kom_OPT .accordion-button.collapsed {
    border-left: 4px solid #fd7e14; /* Bootstrap orange */
}

#headingAutopiter .accordion-button:not(.collapsed),
#headingAutopiter .accordion-button.collapsed {
    border-left: 4px solid #6f42c1; /* Bootstrap purple */
}

/* Add padding to compensate for the border */
#excludedProvidersAccordion .accordion-button {
    padding-left: 16px;
}