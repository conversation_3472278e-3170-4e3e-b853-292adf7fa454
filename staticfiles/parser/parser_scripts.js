// part 1 of the script file

// Global variable to store the current state of excluded providers
let excludedProviders = {};
let originalExcludedProviders = {};
let hasUnsavedChanges = false;
let newlyAddedProviders = {}; // Track newly added, unsaved providers

function updateSaveIndicator() {
    const saveIndicator = document.getElementById('saveIndicator');
    if (hasUnsavedChanges) {
        saveIndicator.style.display = 'block';
        // Add a subtle pulse animation
        saveIndicator.classList.add('save-indicator-pulse');
    } else {
        saveIndicator.style.display = 'none';
        saveIndicator.classList.remove('save-indicator-pulse');
    }
}

function checkForChanges() {
    hasUnsavedChanges = JSON.stringify(excludedProviders) !== JSON.stringify(originalExcludedProviders);
    updateSaveIndicator();
    return hasUnsavedChanges;
}


function initExcludedProviders() {
    document.getElementById('saveExcludedProviders').addEventListener('click', saveExcludedProviders);
    document.getElementById('cancelExcludedProviders').addEventListener('click', cancelChanges);
    document.getElementById('toggleExcludedProviders').addEventListener('mousedown', function () {
        loadExcludedProviders();
    });
}

// commenting it out in favor of mouseover on toggleExludedProviders button to snappier load feeling
// function initOffcanvasEvents() {
//     // Load data when offcanvas is shown
//     const offcanvasElement = document.getElementById('excludedProvidersOffcanvas');
//     offcanvasElement.addEventListener('show.bs.offcanvas', function () {
//         loadExcludedProviders();
//     });
// }

function toggleExcludedProvidersSection() {
    loadExcludedProviders();
}

function loadExcludedProviders() {
    const loadingElement = document.getElementById('excludedProvidersLoading');
    const contentElement = document.getElementById('excludedProvidersContent');
    const messageElement = document.getElementById('excludedProvidersMessage');

    loadingElement.style.display = 'block';
    contentElement.style.display = 'none';
    messageElement.style.display = 'none';

    // Fetch excluded providers from the API
    fetch('/get_excluded_providers')
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch excluded providers');
            }
            return response.json();
        })
        .then(data => {
            excludedProviders = data;
            originalExcludedProviders = JSON.parse(JSON.stringify(data)); // Deep copy
            renderExcludedProviders();

            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';
        })
        .catch(error => {
            console.error('Error fetching excluded providers:', error);
            showMessage('Ошибка при загрузке данных: ' + error.message, 'danger');
            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';

            renderExcludedProviders();
        });
}

function showMessage(message, type = 'success') {
    const messageElement = document.getElementById('excludedProvidersMessage');
    messageElement.className = `alert alert-${type} mt-3`;
    messageElement.textContent = message;
    messageElement.style.display = 'block';

    // Auto-hide message after 5 seconds
    // setTimeout(() => {
    //     messageElement.style.display = 'none';
    // }, 5000);
}

// Keep track of the currently open accordion item
let currentOpenParser = null;

function renderExcludedProviders() {
    // Remember which parser was open before re-rendering
    if (!currentOpenParser) {
        // If no parser is currently tracked as open, check if any is actually open
        const openItem = document.querySelector('.accordion-collapse.show');
        if (openItem) {
            currentOpenParser = openItem.id.replace('collapse', '');
        }
    }

    const container = document.getElementById('providersTabContent');
    container.innerHTML = '';

    // Create tabs for each parser
    const parsers = Object.keys(excludedProviders);

    if (parsers.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">Нет данных об исключенных поставщиках</div>';
        return;
    }

    // Create accordion for each parser
    let accordionHtml = '<div class="accordion" id="excludedProvidersAccordion">';

    parsers.forEach((parser, index) => {
        const providers = excludedProviders[parser];
        const parserId = parser.replace(/-/g, '_'); // Replace all hyphens with underscores
        const headingId = `heading${parserId}`;
        const collapseId = `collapse${parserId}`;

        // Check if this parser should be open
        const shouldBeOpen = currentOpenParser === parserId ||
            (!currentOpenParser && index === 0);

        accordionHtml += `
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="${headingId}">
                            <button class="accordion-button ${shouldBeOpen ? '' : 'collapsed'}" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#${collapseId}"
                                    aria-expanded="${shouldBeOpen ? 'true' : 'false'}" aria-controls="${collapseId}">
                                ${parser} (${providers.length} поставщиков)
                            </button>
                        </h2>
                        <div id="${collapseId}" class="accordion-collapse collapse ${shouldBeOpen ? 'show' : ''}"
                             aria-labelledby="${headingId}" data-bs-parent="#excludedProvidersAccordion">
                            <div class="accordion-body">
                                <div class="mb-3">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="newProvider${parserId}"
                                               placeholder="ID поставщика">
                                        <button class="btn btn-outline-primary" type="button"
                                                onclick="addProvider('${parser}')">Добавить</button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <thead>
                                            <tr>
                                                <th>
                                                <abbr title="Примеры:\nдля Autopiter: 38614, 6174\nдля Emex: LRLB, FCER\nдля Partkom: 17319, 25144" style="text-underline-offset: 0.2em;">
                                                ID поставщика
                                                </abbr>
                                                </th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody id="providersList${parserId}">
                                            ${providers.map(provider => `
                                                <tr class="${newlyAddedProviders[parser]?.has(provider) ? 'provider-unsaved' : ''}">
                                                    <td>
                                                        ${provider}
                                                        ${newlyAddedProviders[parser]?.has(provider) ? 
                                                            '<span class="unsaved-badge" title="Не сохранено">*</span>' : ''}
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-danger"
                                                                onclick="removeProvider('${parser}', '${provider}')">
                                                            Удалить
                                                        </button>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
    });

    accordionHtml += '</div>';
    container.innerHTML = accordionHtml;

    // Set up event listeners to track which accordion item is open
    setTimeout(() => {
        const accordionItems = document.querySelectorAll('.accordion-collapse');
        accordionItems.forEach(item => {
            item.addEventListener('shown.bs.collapse', function () {
                currentOpenParser = this.id.replace('collapse', '');
            });
        });
    }, 0);
}

function addProvider(parser) {
    const inputElement = document.getElementById(`newProvider${parser.replace('-', '_')}`);
    const providerId = inputElement.value.trim();

    if (!providerId) {
        showMessage('Введите ID поставщика', 'warning');
        return;
    }

    if (!excludedProviders[parser].includes(providerId)) {
        excludedProviders[parser].push(providerId);
        // Track newly added provider
        if (!newlyAddedProviders[parser]) {
            newlyAddedProviders[parser] = new Set();
        }
        newlyAddedProviders[parser].add(providerId);
        checkForChanges();
        renderExcludedProviders();
    } else {
        showMessage('Этот поставщик уже в списке исключенных', 'warning');
    }

    inputElement.value = '';
}

function removeProvider(parser, provider) {
    const initialLength = excludedProviders[parser].length;
    excludedProviders[parser] = excludedProviders[parser].filter(p => p !== provider);
    
    // Remove from newlyAddedProviders if it was there
    if (newlyAddedProviders[parser]?.has(provider)) {
        newlyAddedProviders[parser].delete(provider);
        if (newlyAddedProviders[parser].size === 0) {
            delete newlyAddedProviders[parser];
        }
    }
    
    // Only trigger change if something was actually removed
    if (excludedProviders[parser].length !== initialLength) {
        checkForChanges();
    }
    renderExcludedProviders();
}

function cancelChanges() {
    // Reset to original values
    excludedProviders = JSON.parse(JSON.stringify(originalExcludedProviders));
    newlyAddedProviders = {}; // Clear all newly added providers
    hasUnsavedChanges = false;
    updateSaveIndicator();
    renderExcludedProviders();
    showMessage('Изменения отменены', 'info');
}

function saveExcludedProviders() {
    if (!hasUnsavedChanges) {
        showMessage('Нет изменений для сохранения', 'info');
        return;
    }
    
    const saveButton = document.getElementById('saveExcludedProviders');
    const originalButtonText = saveButton.innerHTML;
    
    // Disable save button and show loading state
    saveButton.disabled = true;
    saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Сохранение...';

    const loadingElement = document.getElementById('excludedProvidersLoading');
    const contentElement = document.getElementById('excludedProvidersContent');
    const messageElement = document.getElementById('excludedProvidersMessage');

    loadingElement.style.display = 'block';
    contentElement.style.display = 'none';
    messageElement.style.display = 'none';

    // Track successful updates
    let successCount = 0;
    let failCount = 0;
    const totalParsers = Object.keys(excludedProviders).length;

    // Helper function to reset the save button state
    const resetSaveButton = () => {
        saveButton.disabled = false;
        saveButton.innerHTML = originalButtonText;
    };

    // Save each parser's excluded providers
    const savePromises = Object.keys(excludedProviders).map(parser => {
        return fetch(`/update_excluded_providers/${parser}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                providers: excludedProviders[parser]
            })
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || `Ошибка при сохранении для ${parser}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                successCount++;
                return data;
            })
            .catch(error => {
                console.error(`Error updating excluded providers for ${parser}:`, error);
                failCount++;
                throw error;
            });
    });

    // Wait for all save operations to complete
    Promise.allSettled(savePromises)
        .then(results => {
            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';
            resetSaveButton();

            if (failCount === 0) {
                // All updates were successful
                originalExcludedProviders = JSON.parse(JSON.stringify(excludedProviders));
                hasUnsavedChanges = false;
                newlyAddedProviders = {}; // Clear newly added providers on successful save
                updateSaveIndicator();
                // Re-render to update the UI and remove the 'unsaved' indicators
                renderExcludedProviders();
                showMessage('Изменения успешно сохранены', 'success');
            } else if (successCount === 0) {
                // All updates failed
                showMessage('Не удалось сохранить изменения', 'danger');
            } else {
                // Some updates succeeded, some failed
                showMessage(`Сохранено ${successCount} из ${totalParsers} парсеров`, 'warning');
                // Reload to get the current state
                loadExcludedProviders();
            }
        })
        .catch(error => {
            console.error('Error during save operation:', error);
            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';
            resetSaveButton();
            showMessage('Произошла ошибка при сохранении', 'danger');
        });
}

// Add styles for the save indicator and unsaved providers
const style = document.createElement('style');
style.textContent = `
    @keyframes saveIndicatorPulse {
        0% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
        50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
        100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
    }
    .save-indicator-pulse {
        animation: saveIndicatorPulse 2s infinite;
    }
    
    /* Style for unsaved providers */
    .provider-unsaved {
        position: relative;
    }
    
    .provider-unsaved td:first-child {
        position: relative;
        padding-left: 1.25rem;
    }
    
    .unsaved-badge {
        position: absolute;
        left: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        color: #fd7e14;
        font-weight: bold;
        font-size: 1.1em;
        line-height: 1;
    }
    
    .provider-unsaved:hover .unsaved-badge::after {
        content: attr(title);
        position: absolute;
        left: 1.5rem;
        top: 50%;
        transform: translateY(-50%);
        background: #fff3cd;
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        white-space: nowrap;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
        font-weight: normal;
    }
`;
document.head.appendChild(style);

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    // Initialize Select2
    if ($('#platforms').length) {
        $('#platforms').select2();
    }
    if ($('#schedulleType').length) {
        $('#schedulleType').select2();
    }

    // Initialize excluded providers functionality if the container exists
    if (document.querySelector('.excluded-providers-container')) {
        initExcludedProviders();
    }
});


// part 2 of the script file

$(document).ready(function () {
    $('#platforms').select2();
    const platformFiltersContainer = $('#platformFiltersContainer');

    $('#platforms').on('change', function () {
        const selectedPlatforms = $(this).val() || [];

        // Удаляем фильтры для площадок, которые были сняты
        platformFiltersContainer.children('.form-group').each(function () {
            const platform = $(this).attr('id').replace('-filter', '');
            if (!selectedPlatforms.includes(platform)) {
                $(this).remove();
            }
        });
    });

    document.getElementById('uploadForm').addEventListener('submit', function(event) {
        event.preventDefault();

        const fileInput = document.getElementById('fileInput');
        const schedulleType = $('#schedulleType').val(); // Get schedule type values
        const errorMessages = document.getElementById('errorMessages');
        errorMessages.innerHTML = '';

        if (!fileInput.files.length) {
            errorMessages.innerHTML = 'Пожалуйста, выберите файл для загрузки.';
            return;
        }

        const file = fileInput.files[0];
        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (!['xlsx', 'csv'].includes(fileExtension)) {
            errorMessages.innerHTML = 'Пожалуйста, выберите файл в формате .xlsx или .csv.';
            return;
        }

        const platforms = $('#platforms').val();
        const deliveryValue = parseInt($('#deliveryValue').val(), 10) || null;
        const percent = parseInt($('#percent').val(), 10) || null;
        const exportAnalogs = document.getElementById('exportAnalogs').checked;

        const filters = {
            delivery_value: deliveryValue,
            percent: percent,
            export_analogs: exportAnalogs
        };

        const formData = new FormData();
        formData.append('excelFile', file);
        formData.append('platforms', JSON.stringify(platforms));
        formData.append('filters', JSON.stringify(filters));

        const validateAndSend = () => {
            showLoading(true);

            // Check which page we're on and adjust endpoint and redirect accordingly
            let endpoint = '/upload_file';
            let redirectUrl = 'task_status_parser?auto=false';

            // Debug: Log the page type to help troubleshoot
            console.log("Current page type:", window.pageType);

            // Alternative detection: Check if schedulleType element exists on the page
            const isSchedulerPage = document.getElementById('schedulleType') !== null;
            console.log("Is scheduler page (detected by element):", isSchedulerPage);

            // If we're on the scheduler page, use different endpoint and redirect
            // Check if window.pageType is defined, and if it matches 'parser_scheduller'
            // OR if we detected the scheduler page by the presence of the schedulleType element
            if ((typeof window.pageType !== 'undefined' && window.pageType === 'parser_scheduller') || isSchedulerPage) {
                endpoint = '/upload_file_scheduller';
                redirectUrl = '/schedulle_tasks_parser';

                // Add schedule type to form data for scheduler page
                if (schedulleType && schedulleType.length > 0) {
                    formData.append('schedule_type', JSON.stringify(schedulleType));
                }
            }

            fetch(endpoint, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    showLoading(false);
                    if (!data.error) {
                        window.location.href = redirectUrl;
                    } else {
                        errorMessages.innerHTML = `Ошибка: ${data.error}`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showLoading(false);
                    errorMessages.innerHTML = 'Произошла ошибка при загрузке файла.';
                });
        };

        if (fileExtension === 'xlsx') {
            const reader = new FileReader();
            reader.onload = function(e) {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, {type: 'array'});
                const firstSheet = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheet];
                const headers = XLSX.utils.sheet_to_json(worksheet, {header: 1})[0];
                const expectedHeaders = ['Бренд', 'Артикул'];

                if (JSON.stringify(headers) !== JSON.stringify(expectedHeaders)) {
                    errorMessages.innerHTML = 'Файл должен содержать шапку: 1 - Бренд, 2 - Артикул';
                } else {
                    validateAndSend();
                }
            };
            reader.readAsArrayBuffer(file);
        } else if (fileExtension === 'csv') {
            Papa.parse(file, {
                header: false,
                complete: function(results) {
                    const headers = results.data[0];
                    const expectedHeaders = ['Бренд', 'Артикул'];

                    if (JSON.stringify(headers) !== JSON.stringify(expectedHeaders)) {
                        errorMessages.innerHTML = 'Файл должен содержать шапку: 1 - Бренд, 2 - Артикул';
                    } else {
                        validateAndSend();
                    }
                }
            });
        }
    });

    const themeSwitch = document.getElementById('themeSwitch');
    const themeIcon = document.getElementById('themeIcon');

    themeSwitch.addEventListener('change', function () {
        if (themeSwitch.checked) {
            document.body.classList.remove('light-theme');
            document.body.classList.add('dark-theme');
            themeIcon.innerText = '\u{1F319}';
        } else {
            document.body.classList.remove('dark-theme');
            document.body.classList.add('light-theme');
            themeIcon.innerText = '\u{2600}';
        }
    });

    function showLoading(isLoading) {
        const loader = document.querySelector('.loader');
        const responseMessage = document.getElementById('responseMessage');

        if (isLoading) {
            loader.style.display = 'block';
            responseMessage.style.display = 'none';
        } else {
            loader.style.display = 'none';
            responseMessage.style.display = 'block';
        }
    }
});
