import pandas as pd
from cachetools import TTLCache, cached
from clickhouse_driver import Client
from flask import Blueprint, render_template, jsonify
from loguru import logger

from services.config_info import ClickInfo
from services.utils import get_list_inactive_supid

price_relevance_bp = Blueprint("price_relevance", __name__)

suppliers_cache = TTLCache(maxsize=1, ttl=3600)  # кеш на 1 час


def get_suppliers_uncached():
    logger.info("Получение свежих данных о прайс-листах поставщиков без использования кэша.")
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    query = """
    SELECT 
        ds.supid,
        sl.name as sup_name,
        ds.dateupd,
        ds.num_rows
    FROM (
        SELECT 
            supid,
            dateupd,
            COUNT(*) AS num_rows
        FROM sup_stat.dif_step_1
        WHERE (supid, dateupd) IN (
            SELECT 
                supid,
                MAX(dateupd) as max_date
            FROM sup_stat.dif_step_1
            WHERE dateupd <= today()
            GROUP BY supid
        )
        GROUP BY supid, dateupd
    ) ds
    JOIN sup_stat.sup_list sl ON ds.supid = sl.dif_id
    ORDER BY ds.dateupd
    """
    result = client.execute(query)
    suppliers = [{"supid": row[0], "sup_name": row[1], "dateupd": row[2], "num_rows": row[3]} for row in result]
    logger.success(f"Успешно получено {len(suppliers)} записей о поставщиках.")
    return suppliers


@cached(cache=suppliers_cache)
def get_cached_suppliers():
    logger.info("Получение данных о прайс-листах поставщиков с кэшированием.")
    return get_suppliers_uncached()


@price_relevance_bp.route("/price_relevance")
def price_relevance() -> str:
    """
    Renders the price relevance page with only active suppliers.

    - Fetches all suppliers from cache (ClickHouse).
    - Fetches the list of inactive supplier IDs.
    - Filters out inactive suppliers.
    - Passes the filtered list to the template.

    Returns:
        str: Rendered HTML for the price relevance page.
    """
    logger.info("Отображение страницы актуальности цен.")
    all_suppliers: list[dict] = get_cached_suppliers()
    logger.info(f"Найдено {len(all_suppliers)} поставщиков. Фильтрация неактивных...")

    inactive_supid_list: list[int] = get_list_inactive_supid()
    suppliers_df = pd.DataFrame(all_suppliers)
    active_suppliers_df = suppliers_df[~suppliers_df["supid"].isin(inactive_supid_list)]
    active_suppliers = active_suppliers_df.to_dict(orient="records")

    logger.success(f"Найдено {len(active_suppliers)} активных поставщиков после фильтрации.")
    return render_template("price_relevance.html", suppliers=active_suppliers)


@price_relevance_bp.route("/price_relevance/refresh")
def refresh_price_relevance():
    logger.info("Обновление данных об актуальности цен.")
    suppliers = get_suppliers_uncached()
    suppliers_cache.clear()
    get_cached_suppliers()
    logger.success("Данные об актуальности цен успешно обновлены.")
    return jsonify(suppliers)