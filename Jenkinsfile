pipeline {
    agent any

    environment {
        DOCKER_COMPOSE_FILE = '/media/click/admin_flask_test/docker-compose.yaml' // путь к вашему docker-compose файлу
    }

    stages {
        stage('Prepare Workspace') {
            steps {
                script {
                    def workspaceDir = '/media/click/admin_flask_test'

                    if (fileExists("${workspaceDir}/.git")) {
                        echo "Directory is already a Git repository. Performing git pull."
                        dir(workspaceDir) {
                            sh 'git reset --hard'
                            sh 'git clean -fd'
                            sh 'git pull origin main_test'
                        }
                    } else {
                        echo "Directory is not a Git repository. Cloning repository."
                        withCredentials([string(credentialsId: 'c0797765-6299-4316-a4b2-55aa4472f7f2', variable: 'GITHUB_PAT')]) {
                            sh 'git clone -b main_test https://${GITHUB_PAT}@github.com/IgorMonchTorg-Zap/admin_flask.git /media/click/admin_flask_test'
                        }
                    }
                }
            }
        }

        stage('Build and Deploy') {
            steps {
                script {
                    echo "Building and deploying containers."
                    sh 'docker compose -f $DOCKER_COMPOSE_FILE pull'
                    sh 'docker compose -f $DOCKER_COMPOSE_FILE up -d --build'
                }
            }
        }
    }

    post {
        always {
            echo "Cleaning up Docker."
            sh 'docker system prune -f'
        }
        success {
            echo 'Deployment was successful!'
        }
        failure {
            echo 'Deployment failed.'
        }
    }
}