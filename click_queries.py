import time

import pandas as pd
from clickhouse_driver import Client
from clickhouse_driver import errors as ch_errors
from loguru import logger

from services.config_info import ClickInfo


def get_client_no_settings():
    return Client(
        host=ClickInfo.host,                             # замените на ваш хост
        user=ClickInfo.settings['user'],                 # замените на имя пользователя
        password=ClickInfo.settings['password'],         # замените на пароль
        connect_timeout=120,                             # Таймаут на подключение (секунды)
        send_receive_timeout=99999,                      # Таймаут на отправку/получение данных (секунды)
        sync_request_timeout=99999,                      # Таймаут на синхронный запрос (секунды)
        settings=ClickInfo.settings_no_np
	)


def get_client():
    return Client(
        host=ClickInfo.host,                             # замените на ваш хост
        user=ClickInfo.settings['user'],                 # замените на имя пользователя
        password=ClickInfo.settings['password'],         # замените на пароль
        connect_timeout=120,                             # Таймаут на подключение (секунды)
        send_receive_timeout=99999,                      # Таймаут на отправку/получение данных (секунды)
        sync_request_timeout=99999,                      # Таймаут на синхронный запрос (секунды)
        settings=ClickInfo.settings
    )


def execute_query_with_retry(query, max_retries=3, delay=1):
    global client
    attempts = 0
    while attempts < max_retries:
        try:
            return client.query_dataframe(query)
        except ConnectionResetError as e:
            attempts += 1
            logger.warning(f"Ошибка ConnectionResetError: {e}. Попытка {attempts}/{max_retries}...")
            time.sleep(delay)
            client = get_client()
    logger.error(f"Не удалось выполнить запрос после {max_retries} попыток.")
    raise Exception(f"Query failed after {max_retries} retries.")


def insert_with_retry(
                    table_name: str,
                    df_all_data: pd.DataFrame,
                    max_retries: int = 3,
                    initial_backoff: float = 2.0) -> dict:
    """
    Пишет df_all_data в ClickHouse, с повторами при ConnectionResetError.

    :param table_name: название таблицы Clickhouse, в которую вставляем данные
    :param df_all_data: DataFrame для вставки.
    :param max_retries: максимальное число попыток.
    :param initial_backoff: начальная задержка между попытками (сек).
    :return: результат client.insert_dataframe.
    :raises RuntimeError: если все попытки неудачны.
    """
    attempt = 0
    backoff = initial_backoff
    logger.info(f"Попытка вставки {len(df_all_data)} строк в таблицу '{table_name}'.")

    while attempt < max_retries:
        attempt += 1
        client = get_client()
        try:
            res = client.insert_dataframe(f"INSERT INTO {table_name} VALUES", df_all_data)
            logger.success(f"Успешно вставлено {res} строк в таблицу '{table_name}'.")
            return res

        except ConnectionResetError as e:
            # отлавливаем сброс соединения
            logger.warning(
                f"[Попытка {attempt}/{max_retries}] ConnectionResetError: {e!r}. "
                f"Будем ждать {backoff:.1f}s и повторим попытку."
            )
            time.sleep(backoff)
            backoff *= 2  # экспоненциальный backoff

        except ch_errors.NetworkError as e:
            # если вы пользуетесь clickhouse_driver, там могут быть специфичные сетевые ошибки
            logger.warning(
                f"[Попытка {attempt}/{max_retries}] ClickHouse NetworkError: {e!r}. "
                f"Повтор через {backoff:.1f}s."
            )
            time.sleep(backoff)
            backoff *= 2

        finally:
            try:
                client.disconnect()
            except Exception:
                pass  # игнорируем ошибки при дисконнекте

    # если мы сюда дошли — все попытки исчерпаны
    logger.critical(f"Не удалось вставить данные в таблицу '{table_name}' после {max_retries} попыток.")
    raise RuntimeError(f"Не удалось вставить данные после {max_retries} попыток из-за сетевой ошибки")
