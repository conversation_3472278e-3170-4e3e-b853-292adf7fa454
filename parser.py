import io
import json
import os
from collections import OrderedDict
from datetime import datetime
from typing import Dict, OrderedDict

import aiohttp
import pandas as pd
from dotenv import load_dotenv
from flask import Blueprint, render_template, jsonify, request, redirect, url_for
from flask_jwt_extended import jwt_required
from flask_jwt_extended.exceptions import NoAuthorizationError
from loguru import logger
from pytz import timezone

from services.config_info import ParserInfo, SharedDirectoryHostInfo
from services.sets import week_days

load_dotenv()
moscow_tz = timezone("Europe/Moscow")
app_key = os.getenv("CLIENT_APP_KEY_ADMIN")
PORT_SCHED = os.getenv("PORT_SCHED")
parser_bp = Blueprint("parser", __name__)


@parser_bp.errorhandler(NoAuthorizationError)
def internal_error(error):
    logger.warning(f"Ошибка авторизации: {error}. Перенаправление на страницу входа.")
    return redirect(url_for("auth.login"))


@parser_bp.route("/parser", methods=["GET"])
@jwt_required()
async def parser():
    return render_template("parser/parser.html")


@parser_bp.route("/parser_sheduller", methods=["GET"])
@jwt_required()
async def parser_scheduller():
    return render_template("parser/parser_scheduller.html")


@parser_bp.route("/best_price", methods=["GET"])
@jwt_required()
async def best_price():
    return render_template("parser/best_price.html")


@parser_bp.route("/upload_file_scheduller", methods=["POST"])
@jwt_required()
async def upload_file_scheduller():
    logger.info("Загрузка файла для планировщика парсера.")
    try:
        shared_directory_server = SharedDirectoryHostInfo().host
        file = request.files.get("excelFile")
        if not file or not hasattr(file, "read"):
            logger.warning("Файл excelFile не передан или передан неверно.")
            return jsonify({"error": "Файл excelFile не передан или передан неверно"}), 400

        if file.filename == "":
            logger.warning("Файл не выбран.")
            return jsonify({"error": "Файл не выбран"}), 400

        file_content = file.read()
        platforms = request.form.get("platforms")
        filters = request.form.get("filters")
        schedule_type = request.form.get("schedule_type")

        try:
            platforms = json.loads(platforms) if platforms else []
            filters = json.loads(filters) if filters else {}
            schedule_type = json.loads(schedule_type) if schedule_type else []
        except json.JSONDecodeError as e:
            logger.error(f"Ошибка декодирования JSON: {e}")
            return jsonify({"error": f"Ошибка декодирования JSON: {str(e)}"}), 400

        if not platforms:
            logger.warning("Платформы не указаны.")
            return jsonify({"error": "Не указаны платформы (platforms)"}), 400

        form_data = aiohttp.FormData()
        form_data.add_field(
            "file",
            io.BytesIO(file_content),
            filename=file.filename,
            content_type=file.content_type or "application/octet-stream",
        )
        form_data.add_field(
            "settings",
            json.dumps({"platforms": platforms, "filters": filters, "filename": file.filename, "auto": True}),
            content_type="application/json",
        )
        form_data.add_field("type", json.dumps(schedule_type) if schedule_type else json.dumps([]))
        form_data.add_field("created", datetime.now(moscow_tz).strftime("%Y-%m-%d %H:%M:%S"))

        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.post(
                f"{shared_directory_server}:{PORT_SCHED}/save_parser", data=form_data, headers=headers
            ) as response:
                response_json = await response.json()
                if response.status >= 300:
                    logger.error(f"Ошибка при сохранении парсера: {response_json.get('error', 'Неизвестная ошибка')}")
                    return jsonify({"error": response_json.get("error", "Неизвестная ошибка")}), response.status
                logger.success("Файл для планировщика успешно загружен.")
                return jsonify(response_json), 200

    except Exception as e:
        logger.error(f"Произошла ошибка: {e}")
        return jsonify({"error": f"Произошла ошибка: {str(e)}"}), 500


@parser_bp.route("/schedulle_tasks_parser", methods=["GET"], endpoint="schedulle_tasks_parser")
@jwt_required()
async def view_tasks():
    logger.info("Просмотр задач парсера.")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(f"{shared_directory_server}:{PORT_SCHED}/tasks_parser", headers=headers) as response:
                tasks_info = await response.json()
                tasks_info = [dict(task) for task in tasks_info]
        for task in tasks_info:
            types = []
            for type_ in task["type"]:
                if not "everyweek" in type_:
                    types.append(type_)
                else:
                    types.append(week_days[int(type_.split("|")[-1])])
            task["type"] = ", ".join(types)

        if request.args.get("format") == "json":
            return jsonify(tasks_info)

        return render_template("schedulle_tasks_parser.html", tasks_info=tasks_info)
    except Exception as e:
        logger.error(f"Ошибка при просмотре задач: {e}")
        return jsonify({"error": str(e)}), 400


@parser_bp.route("/update_task_parser/<int:task_id>", methods=["GET"])
@jwt_required()
async def update_task(task_id):
    logger.info(f"Обновление задачи парсера: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/tasks_parser/{task_id}", headers=headers
            ) as response:
                task_info = await response.json()
                task_info = dict(task_info)

        if request.args.get("format") == "json":
            return jsonify(task_info)

        return render_template("update_parser.html", task_info=task_info)
    except Exception as e:
        logger.error(f"Ошибка при обновлении задачи: {e}")
        return jsonify({"error": str(e)}), 400


@parser_bp.route("/update_task_parser/<int:task_id>", methods=["POST"])
@jwt_required()
async def update_task_post(task_id):
    logger.info(f"Отправка обновленной задачи парсера: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    platforms = request.form.get("platforms")
    schedule_type = request.form.get("schedule_type")
    file = request.files.get("file")

    async with aiohttp.ClientSession() as session:
        headers = {"app-key": app_key}
        async with session.get(
            f"{shared_directory_server}:{PORT_SCHED}/tasks_parser/{task_id}", headers=headers
        ) as response:
            task_info = await response.json()
            task_info = dict(task_info)

    updated_settings = task_info["settings"]

    try:
        if type(json.loads(platforms)) == list:
            updated_settings["platforms"] = json.loads(platforms) if platforms else []
        else:
            updated_settings["platforms"] = [json.loads(platforms)] if platforms else []
        schedule_type = json.loads(schedule_type) if schedule_type else []
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка декодирования JSON: {e}")
        return jsonify({"error": f"Ошибка декодирования JSON: {str(e)}"}), 400

    if not updated_settings["platforms"]:
        logger.warning("Платформы не указаны.")
        return jsonify({"error": "Не указаны платформы (platforms)"}), 400

    try:
        form_data = aiohttp.FormData()
        form_data.add_field("id", str(task_id))
        form_data.add_field("settings", json.dumps(updated_settings))
        form_data.add_field("schedule_type", json.dumps(schedule_type))

        if file:
            form_data.add_field(
                "file",
                file.stream,
                filename=file.filename,
                content_type=file.content_type or "application/octet-stream",
            )

        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.post(
                f"{shared_directory_server}:{PORT_SCHED}/update_parser", data=form_data, headers=headers
            ) as response:
                response_json = await response.json()
                if response.status >= 300:
                    logger.error(f"Ошибка при обновлении задачи: {response_json.get('error', 'Неизвестная ошибка')}")
                    return jsonify({"error": response_json.get("error", "Неизвестная ошибка")}), response.status
                logger.success("Задача успешно обновлена.")
                return jsonify(response_json), 200
    except Exception as e:
        logger.error(f"Ошибка при обновлении задачи: {e}")
        return jsonify({"error": str(e)}), 500


@parser_bp.route("/schedulle_tasks_parser/<int:task_id>", methods=["DELETE"])
@jwt_required()
async def delete_task(task_id):
    logger.info(f"Удаление задачи парсера: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.delete(
                f"{shared_directory_server}:{PORT_SCHED}/tasks_parser/{task_id}", headers=headers
            ) as response:
                result = await response.json()

        if response.status == 200:
            logger.success(f"Задача {task_id} успешно удалена.")
            return jsonify({"message": "Task deleted successfully"}), 200
        logger.error(f"Ошибка при удалении задачи {task_id}: {result.get('error', 'Неизвестная ошибка')}")
        return jsonify({"error": result.get("error", "Unknown error occurred")}), response.status
    except Exception as e:
        logger.error(f"Ошибка при удалении задачи {task_id}: {e}")
        return jsonify({"error": str(e)}), 400


@parser_bp.route("/start_task_now_parser/<int:task_id>", methods=["GET"])
@jwt_required()
async def start_task_now(task_id):
    logger.info(f"Запуск задачи парсера: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/start_task_parser/{task_id}", headers=headers
            ) as response:
                result = await response.json()

        if response.status == 200:
            logger.success(f"Задача {task_id} успешно запущена.")
            return jsonify({"message": "Task star successfully"}), 200
        logger.error(f"Ошибка при запуске задачи {task_id}: {result.get('error', 'Неизвестная ошибка')}")
        return jsonify({"error": result.get("error", "Unknown error occurred")}), response.status
    except Exception as e:
        logger.error(f"Ошибка при запуске задачи {task_id}: {e}")
        return jsonify({"error": str(e)}), 400


@parser_bp.route("/task_history_parser/<int:task_id>", methods=["GET"])
@jwt_required()
async def task_history(task_id):
    logger.info(f"Получение истории задачи парсера: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/task_filename_parser/{task_id}", headers=headers
            ) as response:
                filename = (await response.json())["filename"]
        if response.status == 200:
            parser = ParserInfo().host
            flower_url = f"{parser}/tasks?auto=true"
            logger.debug(f"URL Flower: {flower_url}")
            async with aiohttp.ClientSession() as session:
                headers = {"app-key": app_key}
                async with session.get(flower_url, headers=headers) as response:
                    tasks_info = await response.json()
                filtered_tasks = {task_id: info for task_id, info in tasks_info.items() if info["filename"] == filename}
                sorted_task_ids = sorted(filtered_tasks, key=lambda k: filtered_tasks[k]["id"], reverse=True)
                sorted_tasks = OrderedDict((task_id, filtered_tasks[task_id]) for task_id in sorted_task_ids)
            return render_template(
                "task_status_parser_id_for_scheduller.html", tasks_info=sorted_tasks, task_id=task_id
            )
        else:
            logger.error("Неизвестная ошибка при получении истории задачи.")
            return jsonify({"error": "Unknown error occurred"}), 400
    except Exception as e:
        logger.error(f"Ошибка при получении истории задачи: {e}")
        return jsonify({"error": str(e)}), 400


async def process_upload_file(endpoint):
    logger.info(f"Обработка загруженного файла для эндпоинта: {endpoint}")
    parser = ParserInfo().host

    if "excelFile" not in request.files:
        logger.warning("Файл не найден в запросе.")
        return jsonify({"error": "No file part"}), 400

    file = request.files["excelFile"]
    if file.filename == "":
        logger.warning("Файл не выбран.")
        return jsonify({"error": "No selected file"}), 400

    platforms = json.loads(request.form.getlist("platforms")[0])
    filters = json.loads(request.form.get("filters"))
    if not platforms:
        logger.warning("Платформы не указаны.")
        return jsonify({"error": "No platforms specified"}), 400

    try:
        file_content = file.read()
        if file.filename.endswith((".xlsx", ".xls")):
            df = pd.read_excel(io.BytesIO(file_content))
        elif file.filename.endswith(".csv"):
            df = pd.read_csv(io.BytesIO(file_content), sep=";", dtype=str)

        if len(df) > 1000 and "Part-kom_OPT" in platforms:
            logger.warning("Превышен лимит строк для Part-kom_OPT.")
            return (
                jsonify({"error": f"Для платформы Part-kom_OPT стоит лимит в 1000 строк, в файле {len(df)} строк"}),
                400,
            )

        df = df.fillna("").astype(str)
        df_json = json.dumps(
            {"data": df.to_dict(), "platforms": platforms, "filename": file.filename, "filters": filters, "auto": False}
        )

        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.post(f"{parser}/{endpoint}", json=df_json, headers=headers) as response:
                await response.text()
                logger.success("Файл успешно обработан и отправлен.")
                return jsonify({"error": False})
    except Exception as e:
        logger.error(f"Ошибка при обработке файла: {e}")
        return jsonify({"error": str(e)}), 400


@parser_bp.route("/upload_file", methods=["POST"])
@jwt_required()
async def upload_file():
    return await process_upload_file("start_parser")


@parser_bp.route("/upload_file_history", methods=["POST"])
@jwt_required()
async def upload_file_history():
    return await process_upload_file("start_parser_history")


async def fetch_and_sort_tasks(auto: str, endpoint: str) -> OrderedDict:
    logger.info(f"Получение и сортировка задач для эндпоинта: {endpoint}, auto: {auto}")
    parser = ParserInfo().host
    flower_url = f"{parser}/{endpoint}?auto={auto}"
    logger.info(f"URL Flower: {flower_url}")
    async with aiohttp.ClientSession() as session:
        headers = {"app-key": app_key}

        async with session.get(flower_url, headers=headers) as response:
            if response.status != 200:
                try:
                    error_data = await response.json()
                    logger.error(f"Ошибка от API flower: {response.status} - {error_data}")
                except json.JSONDecodeError:
                    error_data = await response.text()
                    logger.error(f"Ошибка от API flower: {response.status} - {error_data}")
                return OrderedDict()

            tasks_info: Dict[str, Dict] = await response.json()
            logger.info(f"Получен ответ по tasks_info")

            if not isinstance(tasks_info, dict) or not all(isinstance(v, dict) for v in tasks_info.values()):
                logger.warning(f"Получен неожиданный ответ от flower")
                return OrderedDict()

            sorted_task_ids = sorted(
                tasks_info,
                key=lambda k: (
                    datetime.strptime(tasks_info[k].get("received", ""), "%a, %d %b %Y %H:%M:%S GMT")
                    if tasks_info[k].get("received")
                    else datetime.min
                ),
                reverse=True,
            )
            logger.success(f"Найдено и отсортировано {len(sorted_task_ids)} задач.")
            return OrderedDict((task_id, tasks_info[task_id]) for task_id in sorted_task_ids)


async def render_task_status(auto: str, endpoint: str, template: str):
    logger.info(f"Отрисовка статуса задачи для эндпоинта: {endpoint}, auto: {auto}")
    sorted_tasks = await fetch_and_sort_tasks(auto, endpoint)

    if request.args.get("format") == "json":
        return jsonify(sorted_tasks)

    return render_template(template, tasks_info=sorted_tasks)


@parser_bp.route("/task_status_parser", methods=["GET"])
@jwt_required()
async def task_status_parser():
    auto = request.args.get("auto")
    return await render_task_status(auto, "tasks", "tasks_parser_status.html")


@parser_bp.route("/task_status_parser_history", methods=["GET"])
@jwt_required()
async def task_status_parser_history():
    auto = request.args.get("auto")
    return await render_task_status(auto, "tasks_history", "tasks_parser_history_status.html")


@parser_bp.route("/get_excluded_providers", methods=["GET"])
@jwt_required()
async def get_excluded_providers():
    logger.info("Получение списка исключенных провайдеров.")
    try:
        parser_service = ParserInfo().host
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(f"{parser_service}/api/excluded_providers", headers=headers) as response:
                if response.status != 200:
                    logger.error("Не удалось получить список исключенных провайдеров.")
                    return jsonify({"error": "Failed to fetch excluded providers"}), response.status

                data = await response.json()
                logger.success("Список исключенных провайдеров успешно получен.")
                return jsonify(data)
    except Exception as e:
        logger.error(f"Ошибка при получении списка исключенных провайдеров: {e}")
        return jsonify({"error": str(e)}), 500


@parser_bp.route("/update_excluded_providers/<parser_name>", methods=["POST"])
@jwt_required()
async def update_excluded_providers(parser_name):
    logger.info(f"Обновление списка исключенных провайдеров для: {parser_name}")
    try:
        data = request.get_json()
        providers = data.get("providers", [])

        parser_service = ParserInfo().host
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.put(
                f"{parser_service}/api/excluded_providers/{parser_name}", json={"providers": providers}, headers=headers
            ) as response:
                if response.status != 200:
                    error_data = await response.json()
                    logger.error(f"Ошибка при обновлении списка исключенных провайдеров: {error_data.get('error', 'Неизвестная ошибка')}")
                    return jsonify({"error": error_data.get("error", "Unknown error")}), response.status

                result = await response.json()
                logger.success("Список исключенных провайдеров успешно обновлен.")
                return jsonify(result)
    except Exception as e:
        logger.error(f"Ошибка при обновлении списка исключенных провайдеров: {e}")
        return jsonify({"error": str(e)}), 500