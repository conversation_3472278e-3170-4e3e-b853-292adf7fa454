import os

from dotenv import load_dotenv

load_dotenv()
PORT_WEB = os.getenv('PORT_WEB')
PORT_WEB_MIRROR = os.getenv('PORT_WEB_MIRROR')


class ClickInfo:
    host: str = '**************'
    host_test: str = '************'
    settings: dict = {'use_numpy': True, 'user': 'torgzap_vlastelin', 'password': '5483her!areA@'}
    settings_test: dict = {'use_numpy': True, 'user': 'torgzap_vlastelin', 'password': '5483'}
    settings_no_np: dict = {'user': 'torgzap_vlastelin', 'password': '5483her!areA@'}


class InputFileConfig:
    INPUT_BRAND_ALIASES: list[str] = ["Бренд", "Брэнд", "brand", "brend", "производитель", "Номенклатура.Производитель", "vendor", "producer", "supplier"]
    INPUT_SKU_ALIASES: list[str] = [
        "Артикул", "sku", "номер", "artikul", "article", "partnumber", "part number", "part_number", "код товара", "код", "Номенклатура.Артикул", "number", "supply number", "sup number", "supnumber",
    ]


class HostInfo:
    host: str = '************'


class ParserInfo:
    host: str = f'http://**************:{PORT_WEB}'
    # host: str = f'http://**************:{PORT_WEB_MIRROR}'  # use this if ************** is down


class SharedDirectoryHostInfo:
    host: str = 'http://**************'
