global_set = {
 'brands_filter_table': '' 
}

#Настройки для отчетов
reports = {
 'dinamic_price_per_day': { 'database': 'sup_stat','table': 'dif_step_1', 'name': 'Динамика цен в прайсах поставщика по дням', 'field_value': 'p', 'filters': None},
 'dimanic_sales_per_day': { 'database': 'dif','table': 'dif_step_3', 'name': 'Детализация продаж у поставщика по дням', 'field_value': 'quantity', 'filters': None},
 'dinamic_quantity_per_day': { 'database': 'sup_stat', 'table': 'dif_step_1', 'name': 'Динамика остатков в прайсе поставщика по дням', 'field_value': 'q', 'filters': None}
}

week_days = ['Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота', 'Воскресенье']

#Настройки для полей на странице выбора фильтров (перевод названий из БД в понятный пользователю вид)
transform_dict = {'supid': 'ID Поставщика', 
                  'a': 'Артикул', 
                  'b': 'Бренд', 
                  'dateupd': 'Дата', 
                  'p': 'Цена', 
                  'q': 'Остатки в шт', 
                  'quantity': 'Количество проданных шт', 
                  'cost_price': 'Суммарная стоимость проданных штук',
                  'f0': 'Количество остатков на первый день меньше количества остатков на второй день',
                  'f1': 'Количество остатков на первый день или равно количеству остатков на пятый день, или равно количеству остатков на четвертый день, или равно количеству остатков на третий день',
                  'f2': 'f2',
                  'f3': 'Количество остатков на первый день больше 30 шт. и разница между остатками на первый и на второй день больше 70%',
                  'f4': 'Количество остатков на первый день нулевое',
                  'f5': 'Количество остатков на второй день нулевое',
                  'f6': 'Количество остатков на первый день или равно остаткам на вчера, или равно остаткам на позавчера, или равно остаткам на 2 дня назад.',
                  'f7': 'Cумма остатков на уровне конкурента за завтра(q1) больше или меньше сегодня(q0) более чем на 30%, то исключаются продажи и остатки для p0',
                  'f8': 'Количество sku на уровне конкурента за завтра(q1) больше или меньше сегодня(q0) более чем на 30%',
                  'f9': 'Количество sku на уровне конкурент-бренд за завтра меньше более чем на 10% чем сегодня, то продажи на уровне конкурент-бренд для p0 исключаются. При этом a0 должно быть больше 50, а a1 больше 0'
                  }

#Словарь подмены дат по типу планировщика (числовое значение дня недели от 0 до 6)
type_scheduller_cross = {0: 'Понедельник', 1: 'Вторник', 2: 'Среда', 3: 'Четверг', 4: 'Пятница', 5: 'Суббота', 6: 'Воскресенье', 'everyday': 'Ежедневная'}