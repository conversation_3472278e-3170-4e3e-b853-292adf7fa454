import pandas as pd
import requests
from loguru import logger


def make_request(url):
    try:
        logger.info(f"Выполнение API-запроса к: {url}")
        response = requests.get(url)
        response.raise_for_status()  # Проверка наличия ошибок в ответе
        data = response.json()
        logger.success(f"API-запрос к {url} успешно выполнен.")
        if isinstance(data, list):
            simplified_response_data = [
                {
                    "id": item.get("id"),
                    "supid": item.get("supid"),
                    "mail_from": item.get("mail_from"),
                    "theme_msg": item.get("theme_msg"),
                    "inside_name": item.get("inside_name"),
                }
                for item in data
            ]
            first_item = simplified_response_data[0] if simplified_response_data else None
            logger.debug(f"Ответ API (первый из {len(simplified_response_data)}): {first_item}")
        else:
            logger.debug(f"Ответ API не является списком: {data}")
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Ошибка при выполнении запроса к {url}: {e}")
        return None


def get_list_inactive_supid() -> list:
    """Get list of inactive supid from API"""
    logger.info("Получение списка неактивных supid из API.")
    response = pd.DataFrame(make_request(f"http://87.249.37.86/api/v1/spr_sup_dif?protocol=Email&active=false"))
    list_from_api = response["supid"].drop_duplicates().values.tolist()
    logger.success(f"Успешно получено {len(list_from_api)} неактивных supid.")
    logger.debug(f"list_active_supid = {list_from_api}")
    return list_from_api
