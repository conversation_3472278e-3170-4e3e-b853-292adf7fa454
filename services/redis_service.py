import json
import os
import time
from typing import Dict, Any, Optional

import redis
from loguru import logger

# Get Redis configuration from environment variables or use defaults
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

# Initialize Redis client
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        decode_responses=True,  # Automatically decode responses to strings
        socket_timeout=5,  # 5 second timeout
        socket_connect_timeout=5,
    )
    # Test connection
    redis_client.ping()
    logger.info(f"Успешное подключение к Redis: {REDIS_HOST}:{REDIS_PORT}")
except redis.ConnectionError as e:
    logger.error(f"Не удалось подключиться к Redis: {str(e)}")
    # Initialize to None, will attempt to reconnect on operations
    redis_client = None

def get_redis_client() -> redis.Redis:
    """Get the Redis client, attempting to reconnect if necessary."""
    global redis_client

    if redis_client is None:
        try:
            redis_client = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                db=REDIS_DB,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
            )
            redis_client.ping()
            logger.success(f"Успешное переподключение к Redis: {REDIS_HOST}:{REDIS_PORT}")
        except redis.ConnectionError as e:
            logger.error(f"Не удалось переподключиться к Redis: {str(e)}")
            raise

    return redis_client

def get_default_progress() -> Dict[str, Any]:
    """Return the default progress state."""
    return {
        "status": "idle",
        "total_suppliers": 0,
        "processed_suppliers": 0,
        "current_supplier": "",
        "completed_suppliers": [],
        "empty_suppliers": [],
        "error_suppliers": [],
        "start_time": None,
        "file_path": None,
        "message": "",
    }

def get_export_progress_by_job(job_id: str) -> Optional[Dict[str, Any]]:
    """Get the export progress for a specific job from Redis."""
    max_retries = 3
    retry_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            client = get_redis_client()
            progress_json = client.get(f'export_progress:{job_id}')

            if progress_json:
                logger.success(f"Успешно получены данные о прогрессе для задачи {job_id}")
                return json.loads(progress_json)
            else:
                if attempt == max_retries - 1:
                    logger.warning(f"Данные о прогрессе для задачи {job_id} не найдены после {max_retries} попыток")
                    return None
                logger.info(f"Данные о прогрессе для задачи {job_id} не найдены, повторная попытка ({attempt+1}/{max_retries})")
        except Exception as e:
            if attempt == max_retries - 1:
                logger.critical(f"Ошибка при получении прогресса экспорта из Redis для задачи {job_id} после {max_retries} попыток: {str(e)}")
                return None
            logger.warning(f"Ошибка при получении прогресса экспорта из Redis для задачи {job_id}, повторная попытка ({attempt+1}/{max_retries}): {str(e)}")

        time.sleep(retry_delay)

def set_export_progress_by_job(job_id: str, progress: Dict[str, Any]) -> bool:
    """Set the export progress for a specific job in Redis.
    Returns True if successful, False otherwise.
    """
    max_retries = 3
    retry_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            client = get_redis_client()
            client.set(f'export_progress:{job_id}', json.dumps(progress))
            client.expire(f'export_progress:{job_id}', 86400)  # 24 hours in seconds
            logger.success(f"Успешно установлен прогресс для задачи {job_id}")
            return True
        except Exception as e:
            if attempt == max_retries - 1:
                logger.critical(f"Ошибка при установке прогресса экспорта в Redis для задачи {job_id} после {max_retries} попыток: {str(e)}")
                return False
            logger.warning(f"Ошибка при установке прогресса экспорта в Redis для задачи {job_id}, повторная попытка ({attempt+1}/{max_retries}): {str(e)}")

        time.sleep(retry_delay)

    return False

def update_export_progress_by_job(job_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Update specific fields in the export progress for a specific job.
    Returns the updated progress dictionary or None if the job doesn't exist or update fails.
    """
    max_retries = 3
    retry_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            progress = get_export_progress_by_job(job_id)

            if progress is None:
                if 'status' not in updates:
                    logger.warning(f"Невозможно обновить несуществующую задачу: {job_id}")
                    return None
                progress = get_default_progress()
                logger.info(f"Создание новой задачи {job_id} со значениями по умолчанию")

            progress.update(updates)

            success = set_export_progress_by_job(job_id, progress)
            if success:
                logger.success(f"Успешно обновлен прогресс для задачи {job_id}")
                return progress

            if attempt == max_retries - 1:
                logger.error(f"Не удалось сохранить прогресс для задачи {job_id} после {max_retries} попыток")
                return None

            logger.warning(f"Не удалось сохранить прогресс для задачи {job_id}, повторная попытка ({attempt+1}/{max_retries})")

        except Exception as e:
            if attempt == max_retries - 1:
                logger.critical(f"Ошибка при обновлении прогресса экспорта в Redis для задачи {job_id} после {max_retries} попыток: {str(e)}")
                return None
            logger.warning(f"Ошибка при обновлении прогресса экспорта в Redis для задачи {job_id}, повторная попытка ({attempt+1}/{max_retries}): {str(e)}")

        time.sleep(retry_delay)

    return None
