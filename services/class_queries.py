import datetime
import os
import re
import uuid
from datetime import timedelta

import numpy as np
import openpyxl
import pandas as pd
from clickhouse_driver import Client
from loguru import logger
from openpyxl import Workbook
from openpyxl.styles import Alignment
from openpyxl.styles import NamedStyle
from openpyxl.styles import numbers
from openpyxl.utils.dataframe import dataframe_to_rows

from custom_exceptions import QueryUsageLimit
from services.config_info import ClickInfo

MAX_EXCEL_ROWS = 1_000_000


class ClicData:
    def _normalize_brands(self, brands: list[str]) -> tuple[str, ...]:
        """
        Normalizes a list of brands by querying `dif.zap_brand_cross` and `dif.unique_brands` tables.
        """
        normalized_brands = []
        for brand in brands:
            normalized_brand_upper = str.upper(brand).replace(" ", "")
            query = f"SELECT parent_id FROM dif.zap_brand_cross WHERE UPPER(REPLACE(brand, ' ', '')) = '{normalized_brand_upper}'"
            parent_id_rows = self.client.execute(query)
            if parent_id_rows:
                parent_id = parent_id_rows[0][0]
                query = f"SELECT brand FROM dif.unique_brands WHERE id = '{parent_id}'"
                alias_brands = [row[0] for row in self.client.execute(query)]
                normalized_brands.extend([str.upper(b) for b in alias_brands])
            else:
                normalized_brands.append(str.upper(brand))
        return tuple(normalized_brands)

    def __init__(self, client: Client, filters: set, settings: dict):
        """
        :param client: Clickhouse client for connect
        :param filters: filters to apply
        :param settings: settings dictionary containing database and table information
        """
        self.client = client
        self.filters = filters
        self.settings = settings

    def fetch_distinct_values(self, column):
        # Query distinct values for the specified column
        if column == "supid":
            query = f"""
            SELECT name, dif_id
            FROM sup_stat.sup_list
        """
            df = self.client.query_dataframe(query)
            values = []
            cols = ["name", "dif_id"]
            for i in cols:
                values.append(df[f"{i}"].dropna().tolist())
            values = list(zip(values[0], values[1]))

        else:
            query = f"""
                SELECT DISTINCT {column}
                FROM {self.settings['database']}.{self.settings['table']}
            """
            df = self.client.query_dataframe(query)
            values = df[column].dropna().unique().tolist()

        return values

    def get_fields(self):
        # Retrieve all column names from the specified table
        query = f"""
            SELECT name
            FROM system.columns 
            WHERE database = '{self.settings['database']}' 
            AND table = '{self.settings['table']}'
        """
        columns = self.client.query_dataframe(query)
        columns = [value for value in columns.to_dict()["name"].values()]
        col_val = {}

        # Sequentially fetch distinct values for each column
        for column in columns:
            if column == "supid":
                col_val[column] = self.fetch_distinct_values(column)
            else:
                col_val[column] = []
                continue
        return col_val

    def generate_sql_query(self):
        # Основная часть запроса
        report_type = self.settings.get("report_type", "")
        base_query = f"""
            SELECT DISTINCT 
                concat(concat(ub.brand, '|'), a) AS sku,  -- Выводим нормализованный бренд из unique_brands
                df.supid, 
                sl.name, 
                df.dateupd, 
                {self.settings['field_value']} 
            FROM {self.settings['database']}.{self.settings['table']} df 
            INNER JOIN sup_stat.sup_list sl ON df.supid = sl.dif_id  
            -- Соединяем с zap_brand_cross для получения parent_id 
            LEFT JOIN dif.zap_brand_cross zbc ON UPPER(REPLACE(df.b, ' ', '')) = UPPER(REPLACE(zbc.brand, ' ', ''))
            -- Соединяем с unique_brands для получения нормализованного бренда
            LEFT JOIN dif.unique_brands ub ON zbc.parent_id = ub.id
            WHERE 1=1 
            AND NOT match(df.b, '[^\\x00-\\x7F]')
            """

        total_query = base_query
        total_query_count = f"""
             SELECT count(*) 
             FROM {self.settings['database']}.{self.settings['table']} df 
             INNER JOIN sup_stat.sup_list sl ON df.supid = sl.dif_id  
             LEFT JOIN dif.zap_brand_cross zbc ON UPPER(REPLACE(df.b, ' ', '')) = UPPER(REPLACE(zbc.brand, ' ', ''))
             LEFT JOIN dif.unique_brands ub ON zbc.parent_id = ub.id
             WHERE 1=1
         """
        """фильтр-триггер количества дней в итовой выборке до execute - число строк, 
        генерируемое запросом делится на количество дней, если больше 1048576, 
        выборка не проходит, выдаётся exception. Если фильтра даты нет, то по умолчанию 1000, так триггер 
        не сработает - тут difference как заглушка логики"""
        difference = 1000
        # Добавление фильтров
        filter_clauses = []
        for field, condition in self.settings["filters"].items():
            field = field.split(", ")[0]
            filter_type = condition.get("filter", "")
            selected_value = condition.get("selected_value", "")
            # Игнорирование фильтров с пустым значением
            if selected_value in ["", " AND "]:
                continue

            if filter_type == "between":
                if field == "dateupd":
                    filter_clauses.append(
                        f"{field} BETWEEN '{selected_value.split(' AND ')[0]}' AND '{selected_value.split(' AND ')[1]}'"
                    )
                    date1_str, date2_str = selected_value.split(" AND ")
                    # Преобразуем строки в объекты datetime
                    date1 = datetime.datetime.strptime(date1_str, "%Y-%m-%d")
                    date2 = datetime.datetime.strptime(date2_str, "%Y-%m-%d")
                    # Находим разницу между двумя датами
                    difference = abs((date2 - date1).days) + 1
                else:
                    filter_clauses.append(f"{field} BETWEEN {selected_value}")
            elif filter_type == "in" and isinstance(selected_value, list):
                # Дату оборачиваем в кавычки
                if field == "dateupd":
                    values = ", ".join(map(lambda x: f"'{x}'", selected_value))
                else:
                    # Остальные фильтры без кавычек
                    values = ", ".join(map(str, selected_value))
                    print(values)
                filter_clauses.append(f"{field} IN ({values})")
            elif filter_type in ("=", ">", "<", "like"):
                if field == "dateupd":
                    filter_clauses.append(f"{field} {filter_type} '{selected_value}'")
                elif field == "a":
                    filter_clauses.append(f"lcase({field}) {filter_type} '{str.lower(selected_value)}'")
                elif field == "b":
                    normalized_brands = self._normalize_brands([selected_value])
                    alias_brands_lower = [f"'{brand}'" for brand in normalized_brands]
                    filter_clauses.append(f"lower({field}) IN ({', '.join(alias_brands_lower)})")
                else:
                    filter_clauses.append(f"{field} {filter_type} {selected_value}")

            # Добавить другие типы фильтров при необходимости

        # Объединение всех частей запроса
        if filter_clauses:
            total_query += " AND " + " AND ".join(filter_clauses)
            total_query_count += " AND " + " AND ".join(filter_clauses)
        print(total_query)
        return total_query, total_query_count, difference

    def execute_and_save_to_excel(self):
        # Генерация SQL-запроса
        query, count_query, difference_days = self.generate_sql_query()
        query_show_usage_query = int(self.client.execute(count_query)[0][0])
        print(query_show_usage_query)
        # #Запрос на получение информации о нагрузке базового query
        # query_show_usage_base = int(self.client.execute(f'EXPLAIN ESTIMATE {base_query}')[1][3])
        # #Запрос на получение информации о нагрузке базового query с фильтрами
        # query_show_usage_query = int(self.client.execute(f'EXPLAIN ESTIMATE {query}')[1][3])
        # print(query_show_usage_base)
        # print(query_show_usage_query)
        # Считаем коеффициент k - % нагрузки от общего количества данных в таблице
        # k_ = query_show_usage_query/query_show_usage_base*100
        # print(k_)
        if (query_show_usage_query < 20000000) and ((query_show_usage_query / difference_days) < 1048576):

            # Выполнение запроса
            df = self.client.query_dataframe(query)
            print(df)

            # Генерация уникального имени файла
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.settings['report_type']}_{timestamp}.xlsx"
            filename_save = f"static/{self.settings['report_type']}_{timestamp}.xlsx"

            # Создание сводной таблицы
            field_value = self.settings.get("field_value", "")
            pd.set_option("future.no_silent_downcasting", True)
            if "dateupd" in df.columns and field_value and field_value in df.columns:
                pivot_table = pd.pivot_table(
                    df,
                    values=field_value,
                    index=df.columns.difference(["dateupd", field_value]).tolist(),
                    columns="dateupd",
                    aggfunc="sum",
                    fill_value=0,
                )
                # Сохранение сводной таблицы в Excel
                pivot_table.to_excel(filename_save)
            else:
                # Сохранение результата в Excel без сводной таблицы
                df.to_excel(filename_save, index=False)

            return filename
        else:
            raise QueryUsageLimit("Слишком большая выборка. Добавьте фильтры", 200)

    def get_sup_lists(self):
        query = """
        SELECT arrayJoin(lists) AS value
        FROM sup_stat.sup_list
        """

        # Выполнение запроса и получение результатов
        result = self.client.execute(query)

        # Извлечение уникальных значений
        unique_values = set(row[0] for row in result)

        return unique_values

    def del_sup_list(self):
        # Получаем имя для удаления
        name_to_delete = self.settings["name"]
        print(name_to_delete)

        # SQL-запрос для удаления значения из массива в ClickHouse
        query = f"""
        ALTER TABLE sup_stat.sup_list UPDATE 
        lists = arrayFilter(x -> x != '{name_to_delete}', lists)
        WHERE arrayExists(x -> x == '{name_to_delete}', lists)
        """

        try:
            # Выполнение запроса
            self.client.execute(query)
            return True  # В случае успешного выполнения
        except Exception as e:
            # Обработка ошибок, например, вывод в лог или возвращение False
            print(f"Error deleting from sup_list: {e}")
            return False

    def get_list_sups_group(self):
        # SQL-запрос для получения списка поставщиков, у которых есть name в lists
        query = f"""
        SELECT DISTINCT dif_id, name
        FROM sup_stat.sup_list
        WHERE has(lists, '{self.settings['name']}')
        """

        # Выполнение запроса и получение результатов
        result = self.client.execute(query)

        # Извлечение уникальных значений поставщиков в виде (dif_if, name)
        suppliers = [i for i in set((row[0], row[1]) for row in result)]
        print(suppliers)
        return suppliers

    def del_sup_from_list(self):
        # Получаем имя для удаления
        name_to_delete = self.settings["name"]
        dif_id = self.settings["dif_id"]
        print(name_to_delete)

        # SQL-запрос для удаления значения из массива в ClickHouse
        query = f"""
        ALTER TABLE sup_stat.sup_list UPDATE 
        lists = arrayFilter(x -> x != '{name_to_delete}', lists)
        WHERE dif_id={dif_id}
        """

        try:
            # Выполнение запроса
            self.client.execute(query)
            return True  # В случае успешного выполнения
        except Exception as e:
            # Обработка ошибок, например, вывод в лог или возвращение False
            print(f"Error deleting from sup_list: {e}")
            return False

    # Получение списка поставщиков с именами и id
    def get_all_sups_names_ids(self):
        # SQL-запрос для получения списка поставщиков, у которых есть name в lists
        query = f"""
        SELECT DISTINCT dif_id, name
        FROM sup_stat.sup_list
        """
        # Выполнение запроса и получение результатов
        result = self.client.execute(query)

        # Извлечение уникальных значений поставщиков в виде (dif_if, name)
        suppliers = [i for i in set((row[0], row[1]) for row in result)]
        print(suppliers)
        return suppliers

    # Добавление поставщика в указанный список
    def set_sup_in_list(self):
        for i in self.settings:
            name = i["name"]
            dif_id = i["dif_id"]

            # SQL-запрос для удаления значения из массива в ClickHouse
            query = f"""
            ALTER TABLE sup_stat.sup_list
            UPDATE lists = arrayConcat(lists, ['{name}'])
                WHERE dif_id = {str(int(dif_id))} AND NOT has(lists, '{name}')
        """
            try:
                # Выполнение запроса
                self.client.execute(query)
            except Exception as e:
                # Обработка ошибок, например, вывод в лог или возвращение False
                print(f"Error deleting from sup_list: {e}")
                return False

    def generate_sql_query_percentage(self):
        # Определяем формат файла и читаем данные
        file_extension = os.path.splitext(self.settings["file"])[1].lower()

        if file_extension == ".xlsx":
            wb = openpyxl.load_workbook(self.settings["file"])
            sheet = wb.active
            rows = sheet.iter_rows(min_row=2, max_col=7, values_only=True)
        elif file_extension == ".csv":
            try:
                df = pd.read_csv(self.settings["file"], delimiter=";")
                print(df)
                rows = df.itertuples(index=False, name=None)
            except Exception as e:
                print(f"Ошибка при чтении CSV файла: {e}")
                return None
        else:
            print("Unsupported file format")
            return None

        # Создание нового .xlsx файла для записи результата
        result_wb = openpyxl.Workbook()
        result_sheet = result_wb.active

        # Определяем заголовки для новых столбцов
        headers = [
            "SKU",
            "Бренд",
            "Артикул",
            "Наименование",
            "Лучшая цена 1",
            "Количество 1",
            "Название поставщика 1",
            "Лучшая цена 2",
            "Количество 2",
            "Название поставщика 2",
            "Лучшая цена 3",
            "Количество 3",
            "Название поставщика 3",
        ]

        # Добавляем заголовки
        for col_num, header in enumerate(headers, start=1):
            result_sheet.cell(row=1, column=col_num, value=header)

        # Создаем пустой список для хранения SQL-запросов
        sql_queries = []
        sups = ", ".join(map(str, self.settings["sups"]))
        logger.info(f"SUPS: {sups}")

        # Итерируемся по строкам в данных
        for row_num, row in enumerate(rows, start=2):
            print(f"Processing row {row_num}: {row}")
            logger.info(f"Processing row {row_num}: {row}")
            brand = str(row[0])
            article = str(row[1])
            name = row[2]  # Сохраняем наименование из входного файла
            print(f"Processing row {row_num}: {row}")
            logger.info(f"Processing row {row_num}: {row}")

            # Устанавливаем дату для запроса
            dateupd = datetime.datetime.now().strftime("%Y-%m-%d")

            # Создание SKU
            sku = f"{brand}|{article}"

            # Запись базовых данных в результат .xlsx
            result_sheet.cell(row=row_num, column=1, value=sku)
            result_sheet.cell(row=row_num, column=2, value=brand)
            result_sheet.cell(row=row_num, column=3, value=article)
            result_sheet.cell(row=row_num, column=4, value=name)

            brand_condition = (
                "('hyundai/kia', 'hyundai/kia/mobis')"
                if brand.lower() == "hyundai/kia/mobis" or brand.lower() == "hyundai/kia"
                else f"('{brand.lower()}')"
            )

            # Формирование SQL-запроса
            query = f"""
    WITH recent_prices AS (
        SELECT DISTINCT
            df.p,
            df.q,
            sl.name,
            df.dateupd,
            ROW_NUMBER() OVER (PARTITION BY sl.name ORDER BY df.dateupd DESC) AS rn
        FROM
            sup_stat.dif_step_1 AS df
        INNER JOIN
            sup_stat.sup_list AS sl
        ON
            df.supid = sl.dif_id
        WHERE
            lower(df.a) = '{article.lower()}'
            AND lower(df.b) IN {brand_condition}
            AND df.dateupd >= now() - interval 2 day
            AND df.supid IN ({sups})
            AND df.q > 0
    ),
    ranked_suppliers AS (
        SELECT DISTINCT p, q,
            name,
            ROW_NUMBER() OVER (PARTITION BY name ORDER BY p ASC) AS rank
        FROM recent_prices
        WHERE rn = 1
    )
    SELECT
        p,
        q,
        name
    FROM
        ranked_suppliers
    WHERE
        rank = 1
    ORDER BY
        p ASC
    LIMIT 3;
    """
            print(f"SQL Query: {article}")
            logger.info(f"SQL Query: {article}")

            # Выполнение SQL-запроса и обработка результатов
            res = self.client.execute(query)

            # Заполнение данных результатами запроса
            for i, result in enumerate(res):
                price = result[0] if len(result) > 0 else ""
                quantity = result[1] if len(result) > 1 else ""
                supplier = result[2] if len(result) > 2 else ""

                result_sheet.cell(row=row_num, column=5 + i * 3, value=float(price) if price != "" else "")
                result_sheet.cell(row=row_num, column=6 + i * 3, value=int(quantity) if quantity != "" else "")
                result_sheet.cell(row=row_num, column=7 + i * 3, value=str(supplier))

                print(f"Filled cells for supplier {i + 1}: Price={price}, Quantity={quantity}, Supplier={supplier}")
                logger.info(
                    f"Filled cells for supplier {i + 1}: Price={price}, Quantity={quantity}, Supplier={supplier}"
                )

            # Если данных меньше 3, заполняем оставшиеся ячейки пустыми значениями
            for i in range(len(res), 3):
                result_sheet.cell(row=row_num, column=5 + i * 3, value="")
                result_sheet.cell(row=row_num, column=6 + i * 3, value="")
                result_sheet.cell(row=row_num, column=7 + i * 3, value="")

                print(f"Filled empty cells for supplier {i + 1}")
                logger.info(f"Filled empty cells for supplier {i + 1}")

            sql_queries.append(res)  # Добавляем результат запроса в список

        # Сохранение изменений в новый .xlsx файл
        unique_filename = f"results_{uuid.uuid4()}.xlsx"
        unique_directory = "static"
        os.makedirs(unique_directory, exist_ok=True)
        unique_filepath = os.path.join(unique_directory, unique_filename)
        result_wb.save(unique_filepath)

        return unique_filepath  # Возвращаем путь к файлу

    def load_existing_data_in_chunks(self, chunk_size=100000):
        """
        Загружает существующие данные из таблицы emex_dif в чанках.

        :param client: клиент для подключения к ClickHouse
        :param chunk_size: размер чанка
        :return: генератор, который возвращает чанк данных
        """
        offset = 0
        while True:
            query = f"SELECT * FROM emex_dif LIMIT {chunk_size} OFFSET {offset}"
            df_chunk = pd.read_sql(self, query)
            if df_chunk.empty:
                break
            yield df_chunk
            offset += chunk_size

    def fetch_chunk(self, client, query, offset, chunk_size):
        """
        Fetch a chunk of data from the database.

        :param client: Database client
        :param query: SQL query template
        :param offset: Offset for the SQL query
        :param chunk_size: Chunk size for the SQL query
        :return: DataFrame with the fetched data
        """
        chunk_query = query.format(offset=offset, chunk_size=chunk_size)
        df_chunk = client.query_dataframe(chunk_query)
        client.disconnect()
        return df_chunk

    def check_discrepancies(self, df_new, start_date, end_date):
        """
        Проверяет количество расхождений между существующими и новыми данными.

        :param df_new: DataFrame с новыми данными
        :param start_date: начальная дата для фильтрации данных
        :param end_date: конечная дата для фильтрации данных
        :return: DataFrame с новыми данными, которых нет в существующих,
                общее количество расхождений за период,
                количество расхождений за последний месяц
        """
        logger.info("Начало проверки расхождений в данных Emex.")
        logger.info(f"Проверка данных за период с {start_date} по {end_date}.")
        # Преобразование данных нового датафрейма
        new_records = df_new.copy()
        new_records.columns = new_records.columns.astype(str)
        new_records["Дата"] = pd.to_datetime(new_records["Дата"]).dt.strftime("%Y-%m-%d")
        # Ensure TIN columns are strings to prevent merge errors
        if "ИННПоставщика" in new_records.columns:
            new_records["ИННПоставщика"] = new_records["ИННПоставщика"].astype(str)
        if "ИННКлиента" in new_records.columns:
            new_records["ИННКлиента"] = new_records["ИННКлиента"].astype(str)
        new_records["ЦенаПокупки"] = pd.to_numeric(new_records["ЦенаПокупки"].astype(str).str.replace(",", ".")).astype("float64")
        new_records["СуммаПокупки"] = pd.to_numeric(new_records["СуммаПокупки"].astype(str).str.replace(",", ".")).astype("float64")
        new_records["ЦенаПродажи"] = pd.to_numeric(new_records["ЦенаПродажи"].astype(str).str.replace(",", ".")).astype("float64")
        new_records["СуммаПродажи"] = pd.to_numeric(new_records["СуммаПродажи"].astype(str).str.replace(",", ".")).astype("float64")

        # SQL-шаблон для выборки существующих данных по частям
        query_template = """
            SELECT `Дата`, `Арт`, `Бренд`, `ЛогоПоставщика`, `ИННПоставщика`, `НаименованиПоставщика`, `Количество`,
                `ЦенаПокупки`, `СуммаПокупки`, `ЦенаПродажи`, `СуммаПродажи`, `Склад`, `ЛогоКлиента`, `ИННКлиента`, 
                `НазваниеКлиента`, `ЛогоПрайса`
            FROM sup_stat.emex_dif 
            WHERE `Дата` >= '{start_date}' AND `Дата` <= '{end_date}' ORDER BY `Дата` DESC
            LIMIT {chunk_size} OFFSET {offset}
        """

        # Получение общего количества строк в таблице
        count_rows = self.client.execute(
            f"""
                                        SELECT count(*)
                                        FROM sup_stat.emex_dif
                                        WHERE `Дата` >= '{start_date}' AND `Дата` <= '{end_date}'
                                        """
        )[0][0]
        logger.info(f"Всего строк для проверки в базе данных: {count_rows}")
        # Определение размера чанка
        chunk_size = 500000
        query_template = query_template.format(
            start_date=start_date, end_date=end_date, chunk_size="{chunk_size}", offset="{offset}"
        )
        total_chunks = (count_rows + chunk_size - 1) // chunk_size if count_rows > 0 else 1
        # Итерирование соответствия входных данных чанкам из БД
        for i, offset in enumerate(range(0, count_rows, chunk_size)):
            chunk_num = i + 1
            logger.info(f"Обработка чанка {chunk_num} из {total_chunks}. Получение данных из БД...")
            df_existing_chunk = self.fetch_chunk(
                Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"]),
                query_template,
                offset,
                chunk_size,
            )
            logger.info(f"Получено {df_existing_chunk.shape[0]} строк из БД.")

            if df_existing_chunk.empty:
                continue

            # Преобразование столбца "Дата" в строку
            df_existing_chunk["Дата"] = pd.to_datetime(df_existing_chunk["Дата"]).dt.strftime("%Y-%m-%d")

            # Сравнение с текущим чанком данных
            merged = new_records.merge(df_existing_chunk, how="left", on=new_records.columns.tolist(), indicator=True)
            new_records = merged[merged["_merge"] == "left_only"].drop(columns="_merge")
            logger.info(f"Найдено {new_records.shape[0]} новых записей после сравнения с чанком {chunk_num}.")

            if new_records.shape[0] == 0:
                logger.info("Все новые записи найдены, дальнейшая проверка не требуется.")
                break
                # Преобразование столбца "Дата" к типу datetime.datetime
        new_records["Дата"] = pd.to_datetime(new_records["Дата"]).dt.date

        # Вычисление расхождений за последний месяц
        last_month_start_date = end_date.replace(day=1)
        last_month_discrepancies = new_records[new_records["Дата"] >= last_month_start_date]
        logger.info(f"Проверка расхождений завершена. Всего найдено {new_records.shape[0]} новых записей.")
        logger.info(f"Из них {last_month_discrepancies.shape[0]} записей за последний месяц.")
        return new_records, last_month_discrepancies

    def load_existing_data_in_chunks(self, chunk_size=100000):
        """
        Загружает существующие данные из таблицы emex_dif в чанках.

        :param client: клиент для подключения к ClickHouse
        :param chunk_size: размер чанка
        :return: генератор, который возвращает чанк данных
        """
        offset = 0
        while True:
            query = f"SELECT * FROM emex_dif LIMIT {chunk_size} OFFSET {offset}"
            df_chunk = pd.read_sql(query, self.client.connection)
            if df_chunk.empty:
                break
            yield df_chunk
            offset += chunk_size

    def clean_numeric_columns(self, df, columns):
        """
        Преобразует указанные числовые колонки из строкового формата с запятой в числовой формат с точкой.

        :param df: DataFrame, содержащий данные
        :param columns: список колонок, которые нужно преобразовать
        :return: DataFrame с преобразованными значениями
        """
        df = df.copy()  # Избегаем SettingWithCopyWarning

        for col in columns:
            # Проверяем, если колонка уже float64, то пропускаем
            if df[col].dtype != "float64":
                df.loc[:, col] = (
                    df[col]
                    .astype(str)  # Преобразуем в строку
                    .str.replace(",", ".", regex=False)  # Заменяем запятые на точки
                    .str.replace(" ", "", regex=False)  # Удаляем пробелы
                    .str.strip()  # Удаляем лишние пробелы
                )

                # Преобразуем в float64 с контролем ошибок
                df.loc[:, col] = pd.to_numeric(df[col], errors="coerce")

            # Принудительно задаём тип float64
            df.loc[:, col] = df[col].astype("float64")

        return df

    def insert_new_data(self, df_new):
        """
        Вставляет новые данные в таблицу emex_dif.

        :param df_new: DataFrame с новыми данными
        """
        columns_to_decode = [
            "Арт",
            "Бренд",
            "ЛогоПоставщика",
            "ИННПоставщика",
            "НаименованиПоставщика",
            "Количество",
            "Склад",
            "ЛогоКлиента",
            "ИННКлиента",
            "НазваниеКлиента",
            "ЛогоПрайса",
        ]
        # Define batch size
        batch_size = 5000

        # Total number of rows
        total_rows = len(df_new)
        if total_rows == 0:
            logger.info("Нет новых данных для вставки.")
            return {"error": False, "result": "Новых данных для записи нет."}

        logger.info(f"Начало вставки {total_rows} новых записей в emex_dif.")
        total_batches = (total_rows + batch_size - 1) // batch_size
        # Decode necessary columns
        for col in columns_to_decode:
            df_new[col] = df_new[col].apply(lambda x: x.decode("cp1251") if isinstance(x, bytes) else x)

        client = Client(
            ClickInfo.host,
            user=ClickInfo.settings["user"],
            password=ClickInfo.settings["password"],
            connect_timeout=999999,
            send_receive_timeout=999999,
        )

        for i, start_idx in enumerate(range(0, total_rows, batch_size)):
            batch_num = i + 1
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df_new.iloc[start_idx:end_idx]
            logger.info(f"Вставка пакета {batch_num} из {total_batches} ({len(batch_df)} записей)...")
            numeric_columns = ["ЦенаПокупки", "СуммаПокупки", "ЦенаПродажи", "СуммаПродажи"]
            batch_df = self.clean_numeric_columns(batch_df, numeric_columns)

            insert_values = []
            for _, row in batch_df.iterrows():
                date_obj = pd.to_datetime(row["Дата"])
                formatted_date = date_obj.strftime("%Y-%m-%d")
                values = (
                    formatted_date,
                    row["Арт"],
                    row["Бренд"],
                    row["ЛогоПоставщика"],
                    row["ИННПоставщика"],
                    row["НаименованиПоставщика"],
                    row["Количество"],
                    row["ЦенаПокупки"],
                    row["СуммаПокупки"],
                    row["ЦенаПродажи"],
                    row["СуммаПродажи"],
                    row["Склад"],
                    row["ЛогоКлиента"],
                    row["ИННКлиента"],
                    row["НазваниеКлиента"],
                    row["ЛогоПрайса"],
                )

                formatted_values = tuple(
                    f"'{str(v).replace('"', '""')}'" if isinstance(v, str) else str(v) for v in values
                )
                insert_values.append(f"({', '.join(formatted_values)})")

            insert_query = f"""
                INSERT INTO sup_stat.emex_dif (`Дата`, `Арт`, `Бренд`, `ЛогоПоставщика`, `ИННПоставщика`, 
                `НаименованиПоставщика`, `Количество`, `ЦенаПокупки`, `СуммаПокупки`, `ЦенаПродажи`, 
                `СуммаПродажи`, `Склад`, `ЛогоКлиента`, `ИННКлиента`, `НазваниеКлиента`, `ЛогоПрайса`)
                VALUES {', '.join(insert_values)}
            """
            try:
                client.execute(insert_query)
            except Exception as e:
                logger.error(f"Ошибка при обновлении файла emex txt: {e}")
        client.disconnect()
        logger.info("Вставка новых данных в emex_dif завершена.")
        return {"error": False, "result": "Данные записаны в БД"}

    async def update_emex_dif(self, df_new):
        logger.info("Начало процесса обновления emex_dif.")
        current_year = datetime.datetime.today().year
        current_month = datetime.datetime.today().month
        client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
        # Определение диапазона дат
        df_new["Дата"] = pd.to_datetime(df_new["Дата"], errors="coerce")
        start_date = df_new["Дата"].min().date()
        end_date = df_new["Дата"].max().date()
        logger.info(f"Диапазон дат в новом файле: с {start_date} по {end_date}.")

        # Проверка на расхождения и получение новых записей
        new_records, last_month_discrepancies = self.check_discrepancies(df_new, start_date, end_date)
        # Запись новых данных в ClickHouse
        self.insert_new_data(new_records)
        result = {}
        result["Новых записей"] = new_records.shape[0]
        result["Новых записей c начала предыдущего месяца"] = last_month_discrepancies.shape[0]
        # Обновление статуса загрузки
        logger.info(f"Обновление upload_tracker за {current_year}-{current_month}.")
        update_query = f"""
            INSERT INTO sup_stat.upload_tracker (year, month, upload)
            VALUES ({current_year}, {current_month}, 1)
        """
        client.execute(update_query)
        logger.info(f"Процесс обновления emex_dif завершен. Итог: {result}")
        return result

    def get_actualization(self):
        query = f"""
         WITH
        -- Подсчет пустых колонок для каждого поставщика на последнюю дату актуализации
        latest_record_data AS (
            SELECT DISTINCT
                supid,
                MAX(dateupd) AS max_dateupd
            FROM sup_stat.dif_step_1
            GROUP BY supid
        ),
        
        -- Расчеты показателей только за последнюю актуальную дату
        calculated_data AS (
            SELECT DISTINCT
                lrd.supid as supid,
                lrd.max_dateupd as max_dateupd,
                COUNT(*) AS count_price,
                SUM(b IS NULL OR b = '') AS empty_b,
                SUM(a IS NULL OR a = '') AS empty_a,
                SUM(p IS NULL) AS empty_p,
                SUM(q IS NULL) AS empty_q,
                COUNT(*) AS count_rows_price,
                -- Сумма значений с более чем 3 цифрами в начале
                SUM(CASE WHEN b IS NOT NULL AND b != '' AND match(b, '^[0-9]{4,}') THEN 1 ELSE 0 END) AS b_more_than_3_digits,
                -- Сумма пустых строк в столбце `b`
                SUM(b IS NULL OR b = '') AS empty_b_count,
                SUM(a IS NULL OR a = '') AS empty_a_count
            FROM sup_stat.dif_step_1 d 
            INNER JOIN latest_record_data lrd ON d.supid = lrd.supid AND d.dateupd = lrd.max_dateupd
            GROUP BY lrd.supid, lrd.max_dateupd 
        )
        
        -- Итоговый запрос с дополнительными колонками
        SELECT DISTINCT
            sl.dif_id AS supid, 
            sl.name AS name, 
            COALESCE(cd.max_dateupd, toDate('1970-01-01')) AS dateupd,
            COALESCE(cd.count_price, 0) AS count_price,
            arrayJoin([
                if(cd.count_rows_price = cd.empty_b AND cd.empty_b > 0, 'Бренд', NULL),
                if(cd.count_rows_price = cd.empty_a AND cd.empty_a > 0, 'Артикул', NULL),
                if(cd.count_rows_price = cd.empty_p AND cd.empty_p > 0, 'Цена', NULL),
                if(cd.count_rows_price = cd.empty_q AND cd.empty_q > 0, 'Количество', NULL)
            ]) AS `Пустые колонки`,
            cd.b_more_than_3_digits AS `Бренд с более чем 3мя цифрами`,
            cd.empty_b_count AS `Пустые строки в бренде`,
            cd.empty_a_count AS `Пустые строки в артикуле`
        FROM sup_stat.sup_list sl
        LEFT JOIN calculated_data cd
        ON sl.dif_id = cd.supid
        ORDER BY `Пустые строки в артикуле` DESC, `Пустые строки в бренде` DESC, `Бренд с более чем 3мя цифрами` DESC, `Пустые колонки` DESC;
            """

        result = self.client.execute(query)
        actualization_sups = [
            {
                "supid": row[0],
                "name": row[1],
                "dateupd": datetime.datetime.strptime(str(row[2]), "%Y-%m-%d"),
                "count_price": row[3],
                "empty_columns": row[4],
                "above_3_digits_brand": row[5],  # Бренд с более чем 3мя цифрами
                "empty_b_count": row[6],
                "empty_a_count": row[7],  # Количество пустых строк в бренде
            }
            for row in result
        ]
        return actualization_sups

    def get_demand_emex_brands(self, brands) -> dict:
        """
        Получает данные о потребности по брендам из источника EMEX.

        Метод анализирует данные продаж за последний год, вычисляет показатели стабильности,
        рекомендуемые объемы закупок и оптимальные цены для товаров указанных брендов.
        Результаты сохраняются в Excel-файл.

        См. подробную документацию о методе в "Документация по методу get_demand_emex_brands"
        в docs/services/class_queries_get_demand_emex.md

        Args:
            brands (list): Список брендов для анализа потребности

        Returns:
            dict: Словарь с результатами:
                - error (bool): False в случае успеха, иначе содержит сообщение об ошибке
                - url (str): Имя сгенерированного файла Excel или None в случае ошибки
        """
        normalized_brands_parent_ids = []
        # Нормализуем все бренды и получаем их parent_id
        for brand in brands:
            parent_id = self._get_parent_id_by_brand(brand)
            normalized_brands_parent_ids.append(str(parent_id))
        try:
            query = f"""WITH sales_data AS (
            SELECT DISTINCT
                `Дата`,
                toMonth(`Дата`) AS month,
                toYear(`Дата`) AS year,
                `Арт`,
                `Бренд`,
                `Количество`,
                `СуммаПокупки`,
                `ЦенаПродажи`,
                `СуммаПродажи`
            FROM sup_stat.emex_dif
            WHERE `Дата` >= toStartOfMonth(addYears(today(), -1)) AND `Дата` < toStartOfMonth(today())
        ),
        monthly_sales AS (
            SELECT
                year,
                month,
                `Арт`,
                `Бренд`,
                SUM(`Количество`) AS total_quantity,
                SUM(`СуммаПокупки`) AS total_purchase_amount,
                SUM(`СуммаПродажи`) AS total_sales_amount
            FROM sales_data
            GROUP BY year, month, `Арт`, `Бренд`
        ),
        stability_data AS (
            SELECT
                `Арт`,
                `Бренд`,
                COUNTIf(total_quantity > 0) AS stability,
                COUNTIf(total_quantity > 0 AND month >= toMonth(today()) - 4) AS recent_sales_count,
                SUM(total_quantity) AS annual_total_quantity,
                MAX(total_quantity) AS max_month_quantity,
                MINIf(total_quantity, total_quantity > 0) AS min_month_quantity,
                AVG(total_sales_amount / NULLIF(total_quantity, 0)) AS avg_sales_price_12m,
                AVGIf(total_sales_amount / NULLIF(total_quantity, 0), month >= toMonth(today()) - 4) AS avg_sales_price_4m
            FROM monthly_sales
            GROUP BY `Арт`, `Бренд`
        ),
        stability_calculated AS (
            SELECT
                `Арт`,
                `Бренд`,
                IF(recent_sales_count = 4, GREATEST(CAST(stability AS Float64), 7.5), CAST(stability AS Float64)) AS final_stability,
                annual_total_quantity,
                max_month_quantity,
                min_month_quantity,
                avg_sales_price_12m,
                avg_sales_price_4m
            FROM stability_data
        )
        SELECT
            `Арт`,
            `Бренд`,
            final_stability,
            IF(final_stability BETWEEN 6 AND 9,
                (annual_total_quantity - max_month_quantity) / 11,
                IF(final_stability >= 10,
                    (annual_total_quantity - max_month_quantity) / 11 * 2,
                    0)) AS demand_quantity,
            IF(avg_sales_price_4m < 3,
                avg_sales_price_4m,
                IF(avg_sales_price_12m < 3,
                    avg_sales_price_12m,
                    IF(avg_sales_price_12m BETWEEN 3 AND 10,
                        LEAST(avg_sales_price_4m, avg_sales_price_12m),
                        avg_sales_price_12m
                    )
                )
            ) AS demand_price
        FROM stability_calculated
        LEFT JOIN dif.zap_brand_cross zp on upper(stability_calculated.`Бренд`) = upper(zp.brand)
        WHERE final_stability >= 6 AND zp.parent_id in ({', '.join(normalized_brands_parent_ids)})  order by demand_quantity asc;
        """
            result_query = self.client.execute(query)
            # Explicitly define the columns for the DataFrame
            columns = ["Арт", "Бренд", "final_stability", "demand_quantity", "demand_price"]
            df = pd.DataFrame(result_query, columns=columns)

            # Replace NaN with None
            df = df.replace({np.nan: None})

            # Convert columns to appropriate data types
            df["Арт"] = df["Арт"].astype(str)
            df["Бренд"] = df["Бреnd"].astype(str)
            df["final_stability"] = df["final_stability"].astype(float)
            df["demand_quantity"] = df["demand_quantity"].astype(float)
            df["demand_price"] = df["demand_price"].astype(float)

            # Create a new workbook and worksheet
            wb = Workbook()
            ws = wb.active

            # Append the column names to the first row
            ws.append(columns)
            # Write data row by row and cell by cell with explicit types
            for row_idx, row in enumerate(df.itertuples(index=False, name=None), start=2):
                for col_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    if col_idx in [1, 2]:  # Columns "Арт" and "Бренд"
                        cell.number_format = numbers.FORMAT_TEXT
                    else:  # Columns "final_stability", "demand_quantity", "demand_price"
                        cell.number_format = "0.00"
            # Путь к файлу
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"demand_emex_brands_{timestamp}.xlsx"
            filename_save = f"static/demand_emex_brands_{timestamp}.xlsx"
            wb.save(filename_save)

            return {"error": False, "url": filename}

        except Exception as e:
            # Логгируем ошибку, если нужно
            print(f"Error: {e}")
            return {"error": "Указанного бренда нет в базе. Измените название в поиске", "url": None}

    def get_interval_to_original_months_emex(self, type):
        try:
            # Определяем условие для брендов в зависимости от типа
            if type == "original":
                brand_condition = "AND upper(`Бренд`) IN (SELECT DISTINCT upper(brand_emex) FROM sup_stat.region_brand)"
            elif type == "not_original":
                brand_condition = """
                AND upper(`Бренд`) IN (
                    'CTR', 'GMB', 'ILJIN', 'MANDO', 'SANGSIN BRAKE', 'VALEO', 'PHC', 'BREMBO', 'GATES', 
                    'LEMFORDER', 'MANN', 'SACHS', 'TRW', 'DEPO', 'FEBEST', 'INA', 'LUK', 'MAHLE / KNECHT', 
                    'NTY', 'NGK', 'OSRAM', 'ZIMMERMANN', 'PHILIPS', 'BOSCH', 'DAYCO', 'DENSO', 'FEBI', 
                    'KAYABA', 'VICTOR REINZ'
                )
                """
            else:
                raise ValueError("Invalid type specified. Use 'original' or 'not_original'.")

            # Формируем SQL-запрос
            query = f"""
            WITH sales_data AS (
                SELECT
                    `Дата`,
                    toMonth(`Дата`) AS month,
                    toYear(`Дата`) AS year,
                    `Арт`,
                    `Бренд`,
                    `Количество`,
                    `СуммаПокупки`
                FROM sup_stat.emex_dif
                WHERE `Дата` >= toStartOfMonth(addMonths(today(), -3))  -- (3 месяца назад)
                  AND `Дата` < toStartOfMonth(today())                 -- (исключаем текущий месяц)
                  {brand_condition}
                  AND `Количество` > 0  -- Только строки с количеством больше 0
            ),
            aggregated_data AS (
                SELECT
                    `Бренд`,
                    `Арт`,
                    year,
                    month,
                    SUM(`Количество`) AS total_quantity,
                    SUM(`СуммаПокупки`) AS total_purchase_amount
                FROM sales_data
                GROUP BY `Бренд`, `Арт`, year, month
            )
            SELECT
                `Бренд`,
                `Арт`,
                -- Количество и сумма покупок за 3 месяца назад
                SUMIf(total_quantity, year = toYear(addMonths(today(), -3)) AND month = toMonth(addMonths(today(), -3))) AS quantity_3,
                SUMIf(total_purchase_amount, year = toYear(addMonths(today(), -3)) AND month = toMonth(addMonths(today(), -3))) AS purchase_amount_3,
                -- Количество и сумма покупок за 2 месяца назад
                SUMIf(total_quantity, year = toYear(addMonths(today(), -2)) AND month = toMonth(addMonths(today(), -2))) AS quantity_2,
                SUMIf(total_purchase_amount, year = toYear(addMonths(today(), -2)) AND month = toMonth(addMonths(today(), -2))) AS purchase_amount_2,
                -- Количество и сумма покупок за 1 месяц назад
                SUMIf(total_quantity, year = toYear(addMonths(today(), -1)) AND month = toMonth(addMonths(today(), -1))) AS quantity_1,
                SUMIf(total_purchase_amount, year = toYear(addMonths(today(), -1)) AND month = toMonth(addMonths(today(), -1))) AS purchase_amount_1
            FROM aggregated_data
            GROUP BY `Бренд`, `Арт`;
            """

            # Получение данных из ClickHouse
            data = self.client.execute(query)

            # Преобразование данных в DataFrame
            df = pd.DataFrame(
                data,
                columns=[
                    "Бренд",
                    "Арт",
                    "quantity_1",
                    "purchase_amount_1",
                    "quantity_2",
                    "purchase_amount_2",
                    "quantity_3",
                    "purchase_amount_3",
                ],
            )

            # Динамическое определение названий месяцев
            today = datetime.datetime.today()
            month_names = [
                (today - timedelta(days=30)).strftime("%B"),  # 3 месяца назад
                (today - timedelta(days=60)).strftime("%B"),  # 2 месяца назад
                (today - timedelta(days=90)).strftime("%B"),  # 1 месяц назад
            ]

            wb = Workbook()
            ws = wb.active

            # Добавление строк с названиями месяцев
            ws.append(["", ""] + [month_names[2]] * 2 + [month_names[1]] * 2 + [month_names[0]] * 2)
            ws.append(
                [
                    "Бренд",
                    "Арт",
                    "Количество",
                    "Сумма Покупки",
                    "Количество",
                    "Сумма Покупки",
                    "Количество",
                    "Сумма Покупки",
                ]
            )

            # Добавление данных из DataFrame
            for row in dataframe_to_rows(df, index=False, header=False):
                ws.append(row)

            # Объединение ячеек для названий месяцев
            ws.merge_cells(start_row=1, start_column=3, end_row=1, end_column=4)  # Месяц 1
            ws.merge_cells(start_row=1, start_column=5, end_row=1, end_column=6)  # Месяц 2
            ws.merge_cells(start_row=1, start_column=7, end_row=1, end_column=8)  # Месяц 3

            # Выравнивание текста по центру
            for row in ws.iter_rows(min_row=1, max_row=1):
                for cell in row:
                    cell.alignment = Alignment(horizontal="center")

            # Сохранение файла
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"interval_to_original_emex_{timestamp}.xlsx"
            filename_save = f"static/interval_to_original_emex_{timestamp}.xlsx"
            wb.save(filename_save)

            return {"error": False, "url": filename}

        except Exception as e:
            # Логгируем ошибку, если нужно
            print(f"Error: {e}")
            return {"error": f"{e}", "url": None}

    def _normalize_brand(self, brand):
        """Нормализует название бренда, убирая лишние символы."""
        brand = "HYUNDAI/KIA/MOBIS" if brand.lower() in ["hyundai/kia", "hyundai/kia/mobis", "hyundai / kia"] else brand
        return re.sub(r"[^a-zA-Z0-9а-яА-ЯёЁ]", "", str(brand)).upper()  # Учитываем русские буквы

    def _get_parent_id_by_brand(self, brand):
        """Получает parent_id по нормализованному бренду."""
        normalized_brand = self._normalize_brand(brand)
        query = f"SELECT parent_id FROM dif.zap_brand_cross WHERE UPPER(REPLACE(brand, ' ', '')) = UPPER(REPLACE('{normalized_brand}', ' ', ''))"
        result = self.client.execute(query)
        if result:
            return result[0][0]
        return None

    def _get_unique_brand_by_parent_id(self, parent_id):
        """Получает название бренда по parent_id."""
        query = f"SELECT brand FROM dif.unique_brands WHERE id = {parent_id}"
        result = self.client.execute(query)
        if result:
            return result[0][0]
        return None

    def get_demand_dif_brands(self, brands):
        """
        Получает данные о потребности по брендам.
        См подробную документацию о методе в "Процесс генерации отчета о потребности" в docs/services/class_queries_get_demand_dif.md
        """
        try:
            normalized_brands = []

            # Нормализуем все бренды и получаем их parent_id
            for brand in brands:
                parent_id = self._get_parent_id_by_brand(brand)
                if parent_id is not None:
                    unique_brand = self._get_unique_brand_by_parent_id(parent_id)
                    if unique_brand:
                        normalized_brands.append(unique_brand)

            # Форматируем нормализованные бренды для SQL
            normalized_brands = [f"'{brand}'" for brand in normalized_brands]
            query = f"""
            WITH unique_data AS (
                SELECT DISTINCT
                    step_4.sku AS sku,
                    step_4.supid AS supid,
                    step_4.d AS d,
                    sl.name AS name,
                    step_4.q_day AS q_day,
                    step_4.q_sale AS q_sale,
                    step_4.quantity AS quantity,
                    multiIf(
                        (NOT isNaN(step_4.price)) AND (step_4.quantity > 0),
                        step_4.price,
                        NULL
                    ) AS price
                FROM
                    dif.dif_step_4_id_full AS step_4
                INNER JOIN
                    sup_stat.sup_list AS sl ON sl.dif_id = step_4.supid
                LEFT JOIN
                    dif.zap_brand_cross AS zbc ON UPPER(replaceRegexpAll(splitByChar('|', step_4.sku)[1], '[^a-zA-Z0-9а-яА-ЯёЁ]', '')) = UPPER(replaceRegexpAll(zbc.brand, '[^a-zA-Z0-9а-яА-ЯёЁ]', ''))
                LEFT JOIN
                    dif.unique_brands AS ub ON ub.id = zbc.parent_id
                WHERE
                	sl.post BETWEEN 24 AND 96
                    AND ub.brand IN ({', '.join(normalized_brands)})
                    AND UPPER(step_4.sku) NOT IN (
                                    SELECT SKU FROM dif.full_sku_black_list
                                    UNION ALL
                                    SELECT UPPER(SKU) FROM dif.full_sku_replace_list)
            ),

            aggregated_data AS (
                SELECT
                    sku,
                    supid,
                    name,
                    SUM(q_day) AS q_day_sum,   -- Количество дней с остатком
                    SUM(q_sale) AS q_sale_sum, -- Количество дней с продажами
                    SUM(quantity) AS total_quantity, -- Количество проданных штук суммарно за все время
                    (SUM(price * quantity) / NULLIF(SUM(quantity), 0)) AS price, -- Средняя цена за 1 штуку
                    (SUM(q_sale) * 30 / NULLIF(SUM(q_day), 0)) AS k_, -- Коэффициент продаж
                    (SUM(quantity) / NULLIF(SUM(q_day), 0) * 30) AS quantity_m -- Среднее количество проданных штук в месяц
                FROM
                    unique_data
                GROUP BY
                    sku, supid, name
                HAVING 
                    q_day_sum > 14 AND q_sale_sum > 0
            )
            SELECT
                sku,
                supid,
                name,
                MAX(q_day_sum) AS avg_q_day_sum,          -- Максимальное количество дней с остатком
                MAX(q_sale_sum) AS avg_q_sale_sum,        -- Максимальное количество дней с продажами
                MAX(k_) AS avg_k_,                        -- Максимальный коэффициент продаж
                MAX(total_quantity) AS avg_total_quantity, -- Максимальное количество проданных штук
                MAX(quantity_m) AS avg_quantity_m,        -- Максимальное среднее количество проданных штук в месяц
                MAX(price) AS avg_price                   -- Максимальная средняя цена за 1 штуку
            FROM
                aggregated_data
            GROUP BY
                sku, supid, name;
            """

            result_query = self.client.execute(query)
            # Define the DataFrame columns
            columns = ["sku", "supid", "name", "q_day_sum", "q_sale_sum", "k_", "total_quantity", "quantity_m", "price"]
            # Create DataFrame from the result
            df = pd.DataFrame(result_query, columns=columns)
            df["supid"] = df["supid"].astype(int)
            df["q_day_sum"] = df["q_day_sum"].astype(float)
            df["q_sale_sum"] = df["q_sale_sum"].astype(float)
            df["k_"] = df["k_"].astype(float)
            df["total_quantity"] = df["total_quantity"].astype(float)
            df["quantity_m"] = df["quantity_m"].astype(float)
            df["price"] = df["price"].astype(float)
            # Clean the DataFrame
            df = df.replace({np.nan: None})
            df.replace([np.inf, -np.inf], np.nan, inplace=True)
            df.fillna("", inplace=True)

            # Create a new workbook and worksheet
            wb = Workbook()
            ws = wb.active

            # Define styles for different data types
            str_style = NamedStyle(name="str_style", number_format="@")  # Text format
            float_style = NamedStyle(name="float_style", number_format="0.00")  # Two decimal float format

            # Append the column names to the first row
            ws.append(columns)

            # Write data row by row and cell by cell with explicit types
            for row_idx, row in enumerate(df.itertuples(index=False, name=None), start=2):
                for col_idx, value in enumerate(row, start=1):
                    if col_idx == 1:
                        value = (
                            "HYUNDAI/KIA/MOBIS"
                            if value.lower() in ["hyundai/kia", "hyundai/kia/mobis", "hyundai / kia"]
                            else value
                        )
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    if col_idx in [1, 2, 3]:  # Columns "sku" and "name"
                        cell.number_format = numbers.FORMAT_TEXT
                    else:  # Columns "q_day_sum", "q_sale_sum", "k_", "total_quantity", "quantity_m", "price"
                        cell.number_format = "0.00"
            # Путь к файлу
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"demand_dif_brands_{timestamp}.xlsx"
            filename_save = f"static/demand_dif_brands_{timestamp}.xlsx"
            wb.save(filename_save)
            return {"error": False, "url": filename}

        except Exception as e:
            # Логгируем ошибку, если нужно
            print(f"Error: {e}")
            return {"error": str(e), "url": None}

    def get_demand_dif_not_filtered(self):
        try:
            query = """
            WITH unique_data AS (
                SELECT DISTINCT
                    step_4.sku AS sku,
                    step_4.supid AS supid,
                    step_4.d,
                    sl.name AS name,
                    step_4.q_day,
                    step_4.q_sale,
                    step_4.quantity,
                    CASE WHEN NOT isNaN(price) AND step_4.quantity > 0 THEN price ELSE NULL END AS price, -- Цена за 1 штуку
                    sc.k AS k__,
                    ub.brand AS brand
                FROM
                    dif.dif_step_4_id_full AS step_4
                INNER JOIN
                    sup_stat.sup_list AS sl ON sl.dif_id = step_4.supid
                LEFT JOIN
                    dif.zap_brand_cross zbc ON UPPER(replaceRegexpAll(splitByChar('|', step_4.sku)[1], '[^a-zA-Z0-9а-яА-ЯёЁ]', '')) = UPPER(replaceRegexpAll(zbc.brand, '[^a-zA-Z0-9а-яА-ЯёЁ]', ''))
                LEFT JOIN
                    dif.unique_brands ub ON ub.id = zbc.parent_id
                INNER JOIN
                    sup_stat.region_brand AS rb ON ub.brand = rb.brand
                INNER JOIN
                    sup_stat.sups_coef AS sc ON step_4.supid = sc.sup_id AND sc.country = rb.region
                WHERE
                    UPPER(step_4.sku) NOT IN (
                        SELECT SKU FROM dif.full_sku_black_list
                        UNION ALL
                        SELECT UPPER(SKU) FROM dif.full_sku_replace_list
                    )
            ),

            aggregated_data AS (
                SELECT
                    sku,
                    supid,
                    brand,
                    name,
                    SUM(q_day) AS q_day_sum,   -- Количество дней с остатком
                    SUM(q_sale) AS q_sale_sum, -- Количество дней с продажами
                    SUM(quantity) AS total_quantity, -- Количество проданных штук суммарно за все время
                    (SUM(price * quantity) / NULLIF(SUM(quantity), 0)) AS price, -- Средняя цена за 1 штуку
                    (SUM(q_sale) * 30 / NULLIF(SUM(q_day), 0)) AS k_, -- Коэффициент продаж
                    k__ -- Коэффициент из sup_stat.sups_coef
                FROM
                    unique_data
                GROUP BY
                    sku, brand, name, supid, k__
                HAVING 
                    q_day_sum > 14 
                    AND k_ > 2
            )

            SELECT
                CONCAT(brand, '|', SUBSTRING_INDEX(sku, '|', -1)) AS sku,
                supid,
                name,
                MAX(q_day_sum) AS avg_q_day_sum,
                MAX(q_sale_sum) AS avg_q_sale_sum,
                MAX(k_) AS avg_k_,
                MAX(total_quantity) AS avg_total_quantity,
                MAX((total_quantity / NULLIF(q_day_sum, 0)) * 30 * k__) AS avg_quantity_m,
                (SUM(price * total_quantity) / NULLIF(SUM(total_quantity), 0)) AS avg_price
            FROM
                aggregated_data
            GROUP BY
                sku, supid, brand, name;
            """
            # Execute the query and get the result
            result_query = self.client.execute(query)

            # Define the DataFrame columns
            columns = ["sku", "supid", "name", "q_day_sum", "q_sale_sum", "k_", "total_quantity", "quantity_m", "price"]

            # Create DataFrame from the result
            df = pd.DataFrame(result_query, columns=columns)
            df["supid"] = df["supid"].astype(int)
            df["q_day_sum"] = df["q_day_sum"].astype(float)
            df["q_sale_sum"] = df["q_sale_sum"].astype(float)
            df["k_"] = df["k_"].astype(float)
            df["total_quantity"] = df["total_quantity"].astype(float)
            df["quantity_m"] = df["quantity_m"].astype(float)
            df["price"] = df["price"].astype(float)
            # Clean the DataFrame
            df = df.replace({np.nan: None})
            df.replace([np.inf, -np.inf], np.nan, inplace=True)
            df.fillna("", inplace=True)

            # Create a new workbook and worksheet
            wb = Workbook()
            ws = wb.active

            # Define styles for different data types
            str_style = NamedStyle(name="str_style", number_format="@")  # Text format
            float_style = NamedStyle(name="float_style", number_format="0.00")  # Two decimal float format

            # Append the column names to the first row
            ws.append(columns)

            # Write data row by row and cell by cell with explicit types
            for row_idx, row in enumerate(df.itertuples(index=False, name=None), start=2):
                for col_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    if col_idx in [1]:  # Columns "sku" and "name"
                        cell.number_format = numbers.FORMAT_TEXT
                    else:  # Columns "q_day_sum", "q_sale_sum", "k_", "total_quantity", "quantity_w", "price"
                        cell.number_format = "0.00"

            # Save the workbook to a file
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"demand_dif_not_filtered_{timestamp}.xlsx"
            filename_save = f"static/demand_dif_not_filtered_{timestamp}.xlsx"
            wb.save(filename_save)

            return {"error": False, "url": filename}

        except Exception as e:
            # Log the error if needed
            print(f"Error: {e}")
            return {"error": True, "url": None}

    def get_demand_emex_not_filtered(self):
        try:
            query = f"""WITH sales_data AS (
                SELECT DISTINCT
                    `Дата`,
                    toMonth(`Дата`) AS month,
                    toYear(`Дата`) AS year,
                    `Арт`,
                    `Бренд`,
                    `Количество`,
                    `ЦенаПокупки`,
                    `СуммаПокупки`,
                    `ЦенаПродажи`,
                    `СуммаПродажи`
                FROM sup_stat.emex_dif
                WHERE `Дата` >= toStartOfMonth(addYears(today(), -1)) AND `Дата` < toStartOfMonth(today())
            ), 
            monthly_sales AS (
                SELECT
                    year,
                    month,
                    `Арт`,
                    `Бренд`,
                    SUM(`Количество`) AS total_quantity,
                    SUM(`СуммаПокупки`) AS total_purchase_amount,
                    SUM(`СуммаПродажи`) AS total_sales_amount
                FROM sales_data
                GROUP BY year, month, `Арт`, `Бренд`
            ),
            stability_data AS (
                SELECT
                    `Арт`,
                    `Бренд`,
                    COUNTIf(total_quantity > 0) AS stability,
                    COUNTIf(total_quantity > 0 AND month >= toMonth(today()) - 4) AS recent_sales_count,
                    SUM(total_quantity) AS annual_total_quantity,
                    MAX(total_quantity) AS max_month_quantity,
                    MINIf(total_quantity, total_quantity > 0) AS min_month_quantity,
                    AVG(total_sales_amount / NULLIF(total_quantity, 0)) AS avg_sales_price_12m,
                    AVGIf(total_sales_amount / NULLIF(total_quantity, 0), month >= toMonth(today()) - 4) AS avg_sales_price_4m
                FROM monthly_sales
                GROUP BY `Арт`, `Бренд`
            ),
            stability_calculated AS (
                SELECT
                    `Арт`,
                    `Бренд`,
                    IF(recent_sales_count = 4, GREATEST(CAST(stability AS Float64), 7.5), CAST(stability AS Float64)) AS final_stability,
                    annual_total_quantity,
                    max_month_quantity,
                    min_month_quantity,
                    avg_sales_price_12m,
                    avg_sales_price_4m
                FROM stability_data
            )
            SELECT
                `Арт`,
                `Бренд`,
                final_stability,
                IF(final_stability BETWEEN 6 AND 9, 
                    (annual_total_quantity - max_month_quantity) / 11,
                    IF(final_stability >= 10,
                        (annual_total_quantity - max_month_quantity) / 11 * 2,
                        0)) AS demand_quantity,
                IF(avg_sales_price_4m < 3,
                    avg_sales_price_4m,
                    IF(avg_sales_price_12m < 3,
                        avg_sales_price_12m,
                        IF(avg_sales_price_12m BETWEEN 3 AND 10,
                            LEAST(avg_sales_price_4m, avg_sales_price_12m),
                            avg_sales_price_12m
                        )
                    )
                ) AS demand_price
            FROM stability_calculated
            WHERE final_stability >= 6 AND upper(`Бренд`) IN (SELECT DISTINCT upper(brand_emex) FROM sup_stat.region_brand) 
            ORDER BY demand_quantity ASC;
            """

            # Execute the query and get the result
            result_query = self.client.execute(query)

            # Explicitly define the columns for the DataFrame
            columns = ["Арт", "Бренд", "final_stability", "demand_quantity", "demand_price"]
            df = pd.DataFrame(result_query, columns=columns)

            # Replace NaN with None
            df = df.replace({np.nan: None})

            # Convert columns to appropriate data types
            df["Арт"] = df["Арт"].astype(str)
            df["Бренд"] = df["Бренд"].astype(str)
            df["final_stability"] = df["final_stability"].astype(float)
            df["demand_quantity"] = df["demand_quantity"].astype(float)
            df["demand_price"] = df["demand_price"].astype(float)

            # Create a new workbook and worksheet
            wb = Workbook()
            ws = wb.active

            # Append the column names to the first row
            ws.append(columns)

            # Define styles for different data types
            str_style = NamedStyle(name="str_style", number_format="@")  # Text format
            float_style = NamedStyle(name="float_style", number_format="0.00")  # Two decimal float format

            # Write data row by row and cell by cell with explicit types
            for row_idx, row in enumerate(df.itertuples(index=False, name=None), start=2):
                for col_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    if col_idx in [1, 2]:  # Columns "Арт" and "Бренд"
                        cell.number_format = numbers.FORMAT_TEXT
                    else:  # Columns "final_stability", "demand_quantity", "demand_price"
                        cell.number_format = "0.00"

            # Save the workbook to a file
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"demand_emex_not_filtered_{timestamp}.xlsx"
            filename_save = f"static/demand_emex_not_filtered_{timestamp}.xlsx"
            wb.save(filename_save)

            return {"error": False, "url": filename}

        except Exception as e:
            # Log the error if needed
            print(f"Error: {e}")
            return {"error": True, "url": None}

    def download_summary_excel(self):
        supplier_ids = self.get_uniq_suppliers()
        results = []

        for sup_id in supplier_ids:
            result = self.query_supplier_data(sup_id, self.settings["brands"], self.settings["OriginalBrandsOnly"])
            results.append(result)
        final_df = pd.concat(results, ignore_index=True)
        final_df = final_df.sort_values(by=["Поставщик", "Бренд", "Месяц", "Регион", "Бренд"])

        # Создание уникального имени файла с временной меткой
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"supplier_summary_{timestamp}.xlsx"
        filepath = os.path.join("static", filename)

        # Разделение данных на части для записи в Excel
        max_rows = 1048576
        print(len(final_df))
        num_sheets = (len(final_df) // max_rows) + 1

        try:
            with pd.ExcelWriter(filepath, engine="xlsxwriter") as writer:
                for i in range(num_sheets):
                    start_row = i * max_rows
                    end_row = min((i + 1) * max_rows, len(final_df))
                    df_part = final_df.iloc[start_row:end_row]
                    sheet_name = f"Sheet_{i + 1}"
                    df_part.to_excel(writer, sheet_name=sheet_name, index=False)

                    # Настройка форматов
                    workbook = writer.book
                    worksheet = writer.sheets[sheet_name]
                    header_format = workbook.add_format({"bold": True, "bg_color": "#F4CCCC"})
                    for col_num, value in enumerate(df_part.columns):
                        worksheet.write(0, col_num, value, header_format)
                    data_format = workbook.add_format({"num_format": "#,##0.00"})
                    for row_num in range(1, len(df_part) + 1):
                        for col_num in range(len(df_part.columns)):
                            # print(f'строка {row_num} - яейка {col_num}')
                            worksheet.write(row_num, col_num, df_part.iloc[row_num - 1, col_num], data_format)

            return {"error": False, "url": filename}
        except Exception as e:
            print(f"Error while saving Excel file: {e}")
            return {"error": True, "url": ""}

    def get_uniq_suppliers(self):
        query = "SELECT DISTINCT dif_id FROM sup_stat.sup_list"
        result = self.client.execute(query)
        print([row[0] for row in result])
        return [row[0] for row in result]

    def query_supplier_data(self, supplier_id, brands, only_origs=False):
        print(supplier_id)
        if brands:
            query = f"""
            WITH MonthlySalesData AS (
            SELECT
                ds.supid,
                formatDateTime(toStartOfMonth(ds.dateupd), '%Y-%m') AS month_year,
                ds.b AS brand,
                SUM(ds.quantity) AS avg_sales_per_month,
                IFNULL(SUM(ds.f0), 0) AS avg_f0,
                IFNULL(SUM(ds.f1), 0) AS avg_f1,
                IFNULL(SUM(ds.f2), 0) AS avg_f2,
                IFNULL(SUM(ds.f3), 0) AS avg_f3,
                IFNULL(SUM(ds.f4), 0) AS avg_f4,
                IFNULL(SUM(ds.f5), 0) AS avg_f5,
                IFNULL(SUM(ds.f6), 0) AS avg_f6,
                IFNULL(SUM(ds.f7), 0) AS avg_f7,
                IFNULL(SUM(ds.f8), 0) AS avg_f8
            FROM
                dif.dif_step_3 ds
            WHERE
                ds.supid = {supplier_id}
                AND b in ({''.join(["'" + brand + "'" for brand in brands])})
            GROUP BY
                ds.supid, month_year, brand
        ),
        SupplierInfo AS (
            SELECT
                sl.dif_id AS supid,
                sl.name AS supplier_name,
                sc.country AS supplier_country,
                sc.k AS coefficient
            FROM
                sup_stat.sup_list sl
            LEFT JOIN
                sup_stat.sups_coef sc ON sl.dif_id = sc.sup_id
            WHERE
                sl.dif_id = {supplier_id}
        ),
        SupplierRegion AS (
            SELECT
                si.supid,
                si.supplier_name,
                IFNULL(rb.region, '') AS region,
                si.coefficient AS coefficient,
                rb.brand
            FROM
                SupplierInfo si
            LEFT JOIN
                sup_stat.region_brand rb ON rb.brand = si.supplier_country
        )
        SELECT
            si.supplier_name AS `Поставщик`,
            msd.brand AS `Бренд`,
            msd.month_year AS `Месяц`,
            IFNULL(msd.avg_sales_per_month, 0)  `Среднее количество продаж за месяц`,
            CASE
                WHEN sr.region != '' THEN sr.coefficient
                ELSE 0
            END AS `k по региону`,
            IFNULL(sr.region, '') AS `Регион`,
            msd.avg_f0 AS `Сумма f0`,
            msd.avg_f1 AS `Сумма f1`,
            msd.avg_f2 AS `Сумма f2`,
            msd.avg_f3 AS `Сумма f3`,
            msd.avg_f4 AS `Сумма f4`,
            msd.avg_f5 AS `Сумма f5`,
            msd.avg_f6 AS `Сумма f6`,
            msd.avg_f7 AS `Сумма f7`,
            msd.avg_f8 AS `Сумма f8`
        FROM
            MonthlySalesData msd
        LEFT JOIN
            SupplierRegion sr ON sr.supid = msd.supid AND sr.brand = msd.brand
        LEFT JOIN
            SupplierInfo si ON si.supid = msd.supid
        ORDER BY
            si.supplier_name, msd.month_year, sr.region, msd.brand
            """
        elif not brands and only_origs:
            query = f"""
            WITH MonthlySalesData AS (
            SELECT
                ds.supid,
                formatDateTime(toStartOfMonth(ds.dateupd), '%Y-%m') AS month_year,
                ds.b AS brand,
                SUM(ds.quantity) AS avg_sales_per_month,
                IFNULL(SUM(ds.f0), 0) AS avg_f0,
                IFNULL(SUM(ds.f1), 0) AS avg_f1,
                IFNULL(SUM(ds.f2), 0) AS avg_f2,
                IFNULL(SUM(ds.f3), 0) AS avg_f3,
                IFNULL(SUM(ds.f4), 0) AS avg_f4,
                IFNULL(SUM(ds.f5), 0) AS avg_f5,
                IFNULL(SUM(ds.f6), 0) AS avg_f6,
                IFNULL(SUM(ds.f7), 0) AS avg_f7,
                IFNULL(SUM(ds.f8), 0) AS avg_f8
            FROM
                dif.dif_step_3 ds
            WHERE
                ds.supid = {supplier_id}
                AND ds.b in (SELECT DISTINCT brand from sup_stat.region_brand)
            GROUP BY
                ds.supid, month_year, brand

        ),
        SupplierInfo AS (
            SELECT
                sl.dif_id AS supid,
                sl.name AS supplier_name,
                sc.country AS supplier_country,
                sc.k AS coefficient
            FROM
                sup_stat.sup_list sl
            LEFT JOIN
                sup_stat.sups_coef sc ON sl.dif_id = sc.sup_id
            WHERE
                sl.dif_id = {supplier_id}
        ),
        SupplierRegion AS (
            SELECT
                si.supid,
                si.supplier_name,
                IFNULL(rb.region, '') AS region,
                si.coefficient AS coefficient,
                rb.brand
            FROM
                SupplierInfo si
            LEFT JOIN
                sup_stat.region_brand rb ON rb.brand = si.supplier_country
        )
        SELECT
            si.supplier_name AS `Поставщик`,
            msd.brand AS `Бренд`,
            msd.month_year AS `Месяц`,
            IFNULL(msd.avg_sales_per_month, 0)  `Среднее количество продаж за месяц`,
            CASE
                WHEN sr.region != '' THEN sr.coefficient
                ELSE 0
            END AS `k по региону`,
            IFNULL(sr.region, '') AS `Регион`,
            msd.avg_f0 AS `Сумма f0`,
            msd.avg_f1 AS `Сумма f1`,
            msd.avg_f2 AS `Сумма f2`,
            msd.avg_f3 AS `Сумма f3`,
            msd.avg_f4 AS `Сумма f4`,
            msd.avg_f5 AS `Сумма f5`,
            msd.avg_f6 AS `Сумма f6`,
            msd.avg_f7 AS `Сумма f7`,
            msd.avg_f8 AS `Сумма f8`
        FROM
            MonthlySalesData msd
        LEFT JOIN
            SupplierRegion sr ON sr.supid = msd.supid AND sr.brand = msd.brand
        LEFT JOIN
            SupplierInfo si ON si.supid = msd.supid
        ORDER BY
            si.supplier_name, msd.month_year, sr.region, msd.brand
            """
        result = self.client.execute(query)
        columns = [
            "Поставщик",
            "Бренд",
            "Месяц",
            "Продаж в штуках за месяц",
            "k по региону",
            "Регион",
            "Сумма f0",
            "Сумма f1",
            "Сумма f2",
            "Сумма f3",
            "Сумма f4",
            "Сумма f5",
            "Сумма f6",
            "Сумма f7",
            "Сумма f8",
        ]
        df = pd.DataFrame(result, columns=columns)
        return df

    def get_recommended_purchase_quantities(self, sku_list, input_df):
        # Initialize an empty DataFrame to store individual results
        final_df = pd.DataFrame(
            columns=[
                "sku",
                "demand",
                "stock",
                "saturation_ratio",
                "saturation_level",
                "trend",
                "sales_change",
                "percentage_change",
                "purchase_advice",
                "recommended_purchase_quantity",
            ]
        )

        for sku in sku_list:
            # Prepare individual SQL query for each SKU
            query = f"""
           WITH
    -- Обработка данных, извлечение года и недели
    parsed_data AS (
        SELECT
            sku AS sku,
            toUInt32(substring(`d`, 1, 2)) AS year, 
            toUInt32(substring(`d`, 4, 2)) AS week
        FROM 
            dif.dif_step_4_id_full
        WHERE
            length(`d`) >= 5
            AND sku='{sku}'
    ),

    -- Расчет даты начала недели
    week_dates AS (
        SELECT
            sku,
            year,
            week,
            toDate(
                concat(
                    if(year < toYear(today()), -- Упростил условие
                        toString(year + 2000),
                        toString(year)
                    ),
                    '-01-01'
                ), 'YYYY-MM-DD'
            ) AS year_start_date,
            toStartOfWeek(toDate(
                concat(
                    if(year < toYear(today()), -- Упростил условие
                        toString(year + 2000),
                        toString(year)
                    ),
                    '-01-01'
                ), 'YYYY-MM-DD'
            ) + INTERVAL (week - 1) WEEK) AS week_start_date
        FROM 
            parsed_data
    ),

    -- Расчет спроса за последние 12 месяцев
    demand_data AS (
        SELECT
            wd.sku,
            toStartOfMonth(wd.week_start_date) AS month_start_date,
            SUM(toFloat64(df.quantity)) AS total_quantity_sold
        FROM 
            week_dates wd
        JOIN 
            dif.dif_step_4_id_full df ON df.sku = wd.sku
        WHERE 
            wd.week_start_date BETWEEN toStartOfMonth(now()) - INTERVAL 12 MONTH AND toStartOfMonth(now())
        GROUP BY 
            wd.sku, month_start_date
    ),

    -- Создание временной таблицы для хранения данных по месяцам
    monthly_sales AS (
        SELECT
            sku,
            month_start_date,
            total_quantity_sold
        FROM 
            demand_data
    ),

    -- Расчет изменений в продажах
    sales_trend AS (
        SELECT
            ms1.sku,
            ms1.month_start_date,
            ms1.total_quantity_sold,
            (ms1.total_quantity_sold - COALESCE(ms2.total_quantity_sold, ms1.total_quantity_sold)) AS sales_change
        FROM 
            monthly_sales ms1
        LEFT JOIN 
            monthly_sales ms2 
        ON 
            ms1.sku = ms2.sku 
            AND ms2.month_start_date = toStartOfMonth(ms1.month_start_date - INTERVAL 1 MONTH)
    ),

    -- Прогнозирование продаж на следующие 6 месяцев по месяцам
    forecast_sales AS (
        SELECT
            sku,
            month_start_date,
            total_quantity_sold + sales_change AS forecasted_sales
        FROM 
            sales_trend
        WHERE
            month_start_date >= toStartOfMonth(now()) - INTERVAL 12 MONTH
    ),

    -- Прогноз на следующие 6 месяцев
    forecast_6_months AS (
        SELECT
            sku,
            month_start_date,
            forecasted_sales,
            ROW_NUMBER() OVER (PARTITION BY sku ORDER BY month_start_date) AS month_number
        FROM 
            forecast_sales
        WHERE
            month_start_date BETWEEN toStartOfMonth(now()) AND toStartOfMonth(now()) + INTERVAL 6 MONTH
    ),

    -- Получение текущих запасов
    current_stock AS (
        SELECT
            CONCAT(`b`, '-', `a`) AS sku,
            SUM(toFloat64(`q`)) AS total_stock
        FROM 
            sup_stat.dif_step_1
        WHERE 
            dateupd >= today() - INTERVAL 4 DAY
            AND CONCAT(`b`, '-', `a`)='{sku}'
        GROUP BY 
            CONCAT(`b`, '-', `a`)
    ),

    -- Определение тренда на основе изменения продаж
    trend_analysis AS (
        SELECT
            fs.sku,
            fs.month_start_date,
            fs.forecasted_sales,
            COALESCE(cs.total_stock, 0) AS current_stock,
            (fs.forecasted_sales - COALESCE(cs.total_stock, 0)) AS recommended_purchase_quantity,
            CASE
                WHEN (fs.forecasted_sales - COALESCE(cs.total_stock, 0)) > 0 THEN 'Increase Purchase'
                ELSE 'Maintain Purchase'
            END AS purchase_advice
        FROM 
            forecast_6_months fs
        LEFT JOIN 
            current_stock cs ON fs.sku = cs.sku
    ),

    -- Прогноз продаж и рекомендации по закупке на основе тренда
    final_recommendation AS (
        SELECT
            ta.sku,
            ta.month_start_date,
            ta.forecasted_sales,
            ta.current_stock,
            ta.recommended_purchase_quantity,
            ta.purchase_advice
        FROM 
            trend_analysis ta
    )

SELECT 
    *
FROM 
    final_recommendation
ORDER BY 
    sku, month_start_date;

            """

            # Execute the query and get the result for the current SKU
            result = self.client.execute(query)

            # Create a temporary DataFrame for this SKU result
            temp_df = pd.DataFrame(
                result,
                columns=[
                    "sku",
                    "month_start_date",
                    "forecasted_sales",
                    "current_stock",
                    "recommended_purchase_quantity",
                    "purchase_advice",
                ],
            )

            # Append the result to the final DataFrame
            final_df = pd.concat([final_df, temp_df], ignore_index=True)

        # Merge the final DataFrame with input_df based on 'sku'
        result_df = input_df.merge(final_df, on="sku", how="left")

        # Generate a timestamp for the filename
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"demand_quantity_{timestamp}.xlsx"
        file_path = os.path.join("/static", filename)

        # Save the result to an Excel file
        result_df.to_excel(file_path, index=False)

        # Return the file path to be used for downloading
        return f"static/{filename}"

    # Управление списком поставщиков
    def get_sup_records(self):
        try:
            """Получить все поля из таблицы."""
            query = f"SELECT dif_id, skl, name, post, lists FROM sup_stat.sup_list"
            return [field for field in self.client.execute(query)]
        except Exception as e:
            return {"error": True, "message": str(e)}

    def get_brand_records(self):
        """Получает названия брендов и разбивает их по источникам для таблицы."""

        def escape_like(query):
            """Экранирует опасные символы для SQL-запросов с LIKE."""
            return re.sub(r"([%_\\'])", r"\\\1", query)

        query = """
                WITH 
                    brand_groups AS (
                        SELECT 
                            parent_id AS id,
                            groupArray(brand) AS zap_brands 
                        FROM 
                            dif.zap_brand_cross 
                        GROUP BY 
                            parent_id
                    )
                SELECT  
                    ub.id,
                    ub.brand,
                    ab.brand AS autopiter_brand,
                    eb.brand AS emex_brand,
                    pb.brand AS partkom_brand,
                    bg.zap_brands
                FROM 
                    dif.unique_brands ub
                LEFT JOIN dif.autopiter_brands ab ON ub.id = ab.parent_id
                LEFT JOIN dif.emex_brands eb ON ub.id = eb.parent_id
                LEFT JOIN dif.partkom_brands pb ON ub.id = pb.parent_id
                LEFT JOIN brand_groups bg ON ub.id = bg.id
                """
        # Проверка наличия фильтра
        if self.filters.get("search_brand"):
            search_brand = escape_like(self.filters["search_brand"])
            query += f" WHERE LOWER(ub.brand) LIKE LOWER('%{search_brand}%')"

        records = self.client.execute(query)

        formatted_records = []
        for id, brand, autopiter_brand, emex_brand, partkom_brand, zap_brands in records:
            formatted_records.append(
                {
                    "id": id,
                    "brand": brand,
                    "autopiter_brand": autopiter_brand,
                    "emex_brand": emex_brand,
                    "partkom_brand": partkom_brand,
                    "zap_brands": zap_brands or [],
                }
            )

        return formatted_records

    def delete_brand(self, platform, parent_id):
        # Удаляет запись, если она существует
        delete_statement = f"""
            ALTER TABLE dif.{platform} DELETE 
            WHERE parent_id = {parent_id} 
            AND EXISTS (SELECT 1 FROM dif.{platform} WHERE parent_id = {parent_id})
        """
        self.client.execute(delete_statement)

    def add_brand(self, platform, parent_id, brand):
        # Добавляет запись, если она ещё не существует
        insert_statement = f"""
            INSERT INTO dif.{platform} (brand, parent_id) 
            SELECT '{brand}', {parent_id}
            WHERE NOT EXISTS (
                SELECT 1 
                FROM dif.{platform} 
                WHERE parent_id = {parent_id} AND brand = '{brand}'
            )
        """
        self.client.execute(insert_statement)

    def delete_alias(self, parent_id, alias):
        # Удаляет запись, если она существует
        delete_statement = f"""
            ALTER TABLE dif.zap_brand_cross DELETE 
            WHERE parent_id = {parent_id} AND brand = '{alias}'
            AND EXISTS (SELECT 1 FROM dif.zap_brand_cross WHERE parent_id = {parent_id} AND brand = '{alias}')
        """
        self.client.execute(delete_statement)

    def add_alias(self, parent_id, alias):
        # Добавляет запись, если она ещё не существует
        insert_statement = f"""
            INSERT INTO dif.zap_brand_cross (brand, parent_id) 
            SELECT '{alias}', {parent_id}
            WHERE NOT EXISTS (
                SELECT 1 
                FROM dif.zap_brand_cross
                WHERE parent_id = {parent_id} AND brand = '{alias}'
            )
        """
        self.client.execute(insert_statement)

    def add_sup_record(self, data: dict):
        try:
            """Добавить новую запись."""
            # Fetch the maximum dif_id from the database
            max_id_query = f"SELECT MAX(dif_id) FROM {self.settings['database']}.{self.settings['table']}"
            max_dif_id = self.client.execute(max_id_query)[0][0]  # Assuming it returns a list of tuples

            # Increment dif_id by 1
            new_dif_id = (max_dif_id + 1) if max_dif_id is not None else 1

            # Add dif_id to data dictionary
            data["dif_id"] = new_dif_id

            # Prepare columns and values for the insert query
            columns = ", ".join(data.keys())
            values = ", ".join([f"'{value}'" if isinstance(value, str) else str(value) for value in data.values()])

            # Create the insert query
            query = f"INSERT INTO {self.settings['database']}.{self.settings['table']} ({columns}) VALUES ({values})"
            self.client.execute(query)
            return {"error": False}
        except Exception as e:
            return {"error": True, "message": str(e)}

    def update_sup_record(self, dif_id: int, data: dict):
        try:
            """Обновить существующую запись."""
            set_clause = ", ".join(
                [f"{key} = '{value}'" if isinstance(value, str) else f"{key} = {value}" for key, value in data.items()]
            )
            query = f"ALTER TABLE {self.settings['database']}.{self.settings['table']} UPDATE {set_clause} WHERE dif_id = {dif_id}"
            self.client.execute(query)
            return {"error": False}
        except Exception as e:
            return {"error": True, "message": str(e)}

    def del_sup_record(self, dif_id: int):
        try:
            """Удалить запись по dif_id."""
            query = f"ALTER TABLE {self.settings['database']}.{self.settings['table']} DELETE WHERE dif_id = {dif_id}"
            self.client.execute(query)
            return {"error": False}
        except Exception as e:
            return {"error": True, "message": str(e)}

    @staticmethod
    def get_week_number_from_iso(date: "datetime.date") -> str:
        """
        Convert a date to custom week string format (e.g., 25Y04W for 2025, week 4).
        :param date: datetime.date
        :return: str in format 'YYYnW', e.g. '25Y04W'
        """
        year = date.strftime("%y")
        week = date.strftime("%V")
        return f"{year}Y{week}W"

    def get_consolidated_demand_with_filters(
        self,
        brands: list[str],
        k_filter: float,
        start_date: str | None = None,
        end_date: str | None = None,
        selected_supids: list[int] | None = None,
    ) -> dict[str, str | None]:
        """
        Generates the consolidated demand report with filters for brands, k_, date range,
        and selected suppliers, combining DIF and EMEX data.
        Saves as .xlsx if <= 1M rows, .csv if > 1M rows.
        Returns dict with url, error, file_type, num_rows, and file_size.
        """
        logger_consolidated.info(
            f"[ConsolidatedReport] Generating report for brands: {len(brands)}, k_filter: {k_filter}, dates: {start_date}-{end_date}, selected_supids: {selected_supids}"
        )

        if selected_supids is not None and not selected_supids:
            logger_consolidated.warning("[ConsolidatedReport] Selected supplier list is empty, returning empty result.")
            return {"url": None, "error": "Не выбрано ни одного поставщика."}

        try:
            today = datetime.date.today()
            # Parse dates from strings to datetime.date objects
            end_dt = datetime.datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else today
            start_dt = (
                datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
                if start_date
                else end_dt - datetime.timedelta(weeks=52)
            )  # Default to 1 year ago

            # Convert dates to week strings for DIF query date filter ('d' column format)
            start_week_dif = self.get_week_number_from_iso(start_dt)
            end_week_dif = self.get_week_number_from_iso(end_dt)
            logger_consolidated.info(
                f"[ConsolidatedReport] DIF date range (weeks, filter by 'd'): {start_week_dif}-{end_week_dif}"
            )
            logger_consolidated.info(
                f"[ConsolidatedReport] EMEX date range (YYYY-MM-DD, filter by 'Дата'): {start_dt.strftime('%Y-%m-%d')}-{end_dt.strftime('%Y-%m-%d')}"
            )

            client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
            clic_data_instance = ClicData(client=client, settings={}, filters={})
            # Normalize brands for the DIF query (and EMEX brand check)
            normalized_brands_dif = []
            # We need parent_ids for EMEX filtering, let's normalize brands here and store parent_ids
            normalized_brands_parent_ids: list[int] = []
            normalized_brands_dif_sql: tuple = clic_data_instance._normalize_brands(brands)
            logger.info(f"Normalized brands for DIF query: {normalized_brands_dif_sql=}\n")
            for brand in brands:
                # Use _normalize_brands logic to find parent_id and unique brands
                parent_id = self._get_parent_id_by_brand(brand)
                if parent_id is not None:
                    normalized_brands_parent_ids.append(parent_id)
                    logger.info(f"Parent_id for brand {brand}: {parent_id}")
                    unique_brand = self._get_unique_brand_by_parent_id(parent_id)
                    if unique_brand:
                        normalized_brands_dif.append(unique_brand)
            # normalized_brands_dif_sql: list[str] = [f"'{brand}'" for brand in normalized_brands_dif]

            params_dif: dict = {}
            if not normalized_brands_dif_sql:
                logger_consolidated.warning("[ConsolidatedReport] No valid normalized brands for DIF query.")
                df_dif = pd.DataFrame()
            else:
                normalized_brands = []

                for brand in brands:
                    parent_id = self._get_parent_id_by_brand(brand)
                    if parent_id is not None:
                        unique_brand = self._get_unique_brand_by_parent_id(parent_id)
                        if unique_brand:
                            normalized_brands.append(unique_brand)

                # Форматируем нормализованные бренды для SQL
                normalized_brands = [f"'{brand}'" for brand in normalized_brands]

                params_dif["normalized_brands_dif_sql"] = normalized_brands_dif_sql  # Pass as tuple for IN clause
                params_dif["start_week_dif"] = start_week_dif
                params_dif["end_week_dif"] = end_week_dif
                params_dif["k_filter"] = k_filter
                params_dif["selected_supids"] = tuple(selected_supids)
                logger_consolidated.info(f"\nAll filters so far:\n"
                                         f"{start_week_dif=}\n"
                                         f"{end_week_dif=}\n"
                                         f"k_filter>={k_filter}\n"
                                         f"{normalized_brands=}\n"
                                         f"selected_supids ({len(selected_supids)}): {selected_supids}\n")

                query_dif = f"""
                WITH unique_data AS (
                SELECT DISTINCT
                    step_4.sku AS sku,
                    step_4.supid AS supid,
                    step_4.d,
                    sl.name AS name,
                    step_4.q_day,
                    step_4.q_sale,
                    step_4.quantity,
                    multiIf(
                        (NOT isNaN(step_4.price)) AND (step_4.quantity > 0),
                        step_4.price,
                        NULL
                    ) AS price,
                    ub.brand AS brand
                FROM
                    dif.dif_step_4_id_full AS step_4
                INNER JOIN
                    sup_stat.sup_list AS sl ON sl.dif_id = step_4.supid
                LEFT JOIN
                    dif.zap_brand_cross zbc ON UPPER(replaceRegexpAll(splitByChar('|', step_4.sku)[1], '[^a-zA-Z0-9а-яА-ЯёЁ]', '')) = UPPER(replaceRegexpAll(zbc.brand, '[^a-zA-Z0-9а-яА-ЯёЁ]', ''))
                LEFT JOIN
                    dif.unique_brands ub ON ub.id = zbc.parent_id
                WHERE
                    sl.post BETWEEN 24 AND 96
                    AND ub.brand IN ({', '.join(normalized_brands)})
                    AND step_4.supid IN %(selected_supids)s
                    AND step_4.d between %(start_week_dif)s and %(end_week_dif)s
                    AND UPPER(step_4.sku) NOT IN (
                        SELECT SKU FROM dif.full_sku_black_list
                        UNION ALL
                        SELECT UPPER(SKU) FROM dif.full_sku_replace_list
                    )
            ),

            aggregated_data AS (
                SELECT
                    sku,
                    supid,
                    brand,
                    name,
                    SUM(q_day) AS q_day_sum,   -- Количество дней с остатком
                    SUM(q_sale) AS q_sale_sum, -- Количество дней с продажами
                    SUM(quantity) AS total_quantity, -- Количество проданных штук суммарно за все время
                    (SUM(price * quantity) / NULLIF(SUM(quantity), 0)) AS price, -- Средняя цена за 1 штуку
                    (SUM(q_sale) * 30 / NULLIF(SUM(q_day), 0)) AS k_, -- Коэффициент продаж
                    (SUM(quantity) / NULLIF(SUM(q_day), 0) * 30) AS quantity_m -- Среднее количество проданных штук в месяц
                FROM
                    unique_data
                GROUP BY
                    sku, brand, name, supid
                HAVING 
                    q_day_sum > 14
                    AND q_sale_sum > 0
                    AND k_ >= %(k_filter)s
            )

            SELECT
                CONCAT(brand, '|', SUBSTRING_INDEX(sku, '|', -1)) AS sku,
                supid,
                name,
                MAX(q_day_sum) AS avg_q_day_sum,
                MAX(q_sale_sum) AS avg_q_sale_sum,
                MAX(k_) AS avg_k_,
                MAX(total_quantity) AS avg_total_quantity,
                MAX(quantity_m) AS avg_quantity_m,
                MAX(price) AS avg_price
            FROM
                aggregated_data
            GROUP BY
                sku, supid, brand, name;
                """

                # Execute the DIF query
                result_query_dif = self.client.execute(query_dif, params=params_dif)

                # Define DIF columns explicitly to ensure correct DataFrame creation
                columns_dif = [
                    "sku",
                    "supid",
                    "name",
                    # "normalized_brand",
                    "q_day_sum",
                    "q_sale_sum",
                    "k_",
                    "total_quantity",
                    "quantity_m",
                    "price",
                ]
                df_dif = pd.DataFrame(result_query_dif, columns=columns_dif)

                logger_consolidated.info(f"[ConsolidatedReport] Fetched {len(df_dif)} rows from DIF.")

                # Add 'Арт' and 'Бренд' columns to df_dif, extracted from sku
                if not df_dif.empty:
                    # Extract 'Арт' from SKU
                    df_dif["sku"] = df_dif["sku"].astype(str)
                    sku_parts = df_dif["sku"].str.split("|", n=1, expand=True)
                    df_dif["Арт"] = sku_parts[1].fillna("")  # Только 'Арт', без 'Бренд'
                    df_dif["Бренд"] = sku_parts[0].fillna("")  # Только 'Бренд', без 'Арт'

                    print("\n🔍 Проверка первых строк после нормализации бренда:")
                    print(df_dif[["sku", "Арт", "Бренд"]].head(10))
                else:
                    # Ensure these columns exist even if df_dif is empty
                    df_dif["Арт"] = pd.Series(dtype="object")
                    df_dif["Бренд"] = pd.Series(dtype="object")

            # --- Call the updated _get_emex_demand_data method ---
            # Pass parent_ids and date objects to the EMEX method
            # We only pass parent_ids that were successfully normalized, matching DIF
            df_emex = self._get_emex_demand_data(normalized_brands_parent_ids, start_dt, end_dt, k_filter)

            # --- Combine DIF and EMEX data ---
            combined_df: pd.DataFrame
            if df_emex is not None and not df_emex.empty:
                # Ensure both dataframes have the same columns before concatenating
                target_columns = [
                    "sku",
                    "supid",
                    "name",
                    "q_day_sum",
                    "q_sale_sum",
                    "k_",
                    "total_quantity",
                    "quantity_m",
                    "price",
                    "Арт",
                    "Бренд",
                ]
                # Ensure column order is consistent
                if not df_dif.empty:
                    df_dif = df_dif[target_columns]
                # Ensure column order is consistent for EMEX data
                if not df_emex.empty:
                    df_emex = df_emex[target_columns]

                combined_df = pd.concat([df_dif, df_emex], ignore_index=True)
                logger_consolidated.info(
                    f"[ConsolidatedReport] Combined DIF ({len(df_dif)} rows) and EMEX ({len(df_emex) if df_emex is not None else 0} rows). Total: {len(combined_df)} rows."
                )
            else:
                combined_df = df_dif.copy()
                logger_consolidated.info(
                    f"[ConsolidatedReport] No EMEX data found or error fetching. Using only DIF data ({len(combined_df)} rows)."
                )

            # Apply k_filter
            if not combined_df.empty:
                # Ensure k_ column is numeric before filtering
                combined_df["k_"] = pd.to_numeric(combined_df["k_"], errors="coerce").fillna(0)
                filter_mask = combined_df["k_"] >= k_filter
                combined_df = combined_df[filter_mask]
                logger_consolidated.info(
                    f"[ConsolidatedReport] Applied k_filter ({k_filter}). Remaining rows: {len(combined_df)}."
                )
            else:
                logger_consolidated.info("[ConsolidatedReport] Combined DataFrame is empty, no k_filter applied.")

            # Ensure numeric columns are float64 and fill NaN/Inf
            numeric_cols_to_clean = ["q_day_sum", "q_sale_sum", "k_", "total_quantity", "quantity_m", "price"]
            for col in numeric_cols_to_clean:
                if col in combined_df.columns:
                    # Use to_numeric with errors='coerce' and fillna for robust conversion
                    combined_df[col] = pd.to_numeric(combined_df[col], errors="coerce").fillna(0.0)

            # Handle potential NaN/Inf values after calculations/conversions (fillna with empty string at the end)
            # Fill None/NaN in object/string columns
            for col in ["sku", "name", "Арт", "Бренд"]:
                if col in combined_df.columns:
                    combined_df[col] = combined_df[col].fillna("")

            combined_df.replace([np.inf, -np.inf], np.nan, inplace=True)  # Replace inf with NaN
            combined_df.fillna("", inplace=True)  # Fill remaining NaN/None with empty string for Excel/CSV

            if combined_df.empty:
                logger_consolidated.warning("[ConsolidatedReport] No data remaining after filtering.")
                return {"url": None, "error": "Нет данных для выбранных критериев после объединения и фильтрации."}

            # Ensure final column order before saving
            final_columns_ordered = [
                "sku",
                "supid",
                "name",
                "q_day_sum",
                "q_sale_sum",
                "k_",
                "total_quantity",
                "quantity_m",
                "price",
                "Арт",
                "Бренд",
            ]
            # Reindex the DataFrame to ensure the specified column order
            combined_df = combined_df.reindex(columns=final_columns_ordered)

            # --- Save based on row count ---
            num_rows = len(combined_df)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"consolidated_demand_report_{timestamp}"
            filename: str
            filename_save: str
            result_url: str
            file_type: str
            file_size: int

            if num_rows > MAX_EXCEL_ROWS:
                # Save as CSV
                filename = f"{base_filename}.csv"
                filename_save = f"static/{filename}"
                file_type = "CSV"
                try:
                    # Use utf-8 encoding and ';' delimiter for consistency if needed, or default to ','
                    combined_df.to_csv(filename_save, index=False, encoding="utf-8")
                    file_size = os.path.getsize(filename_save)  # Get file size
                    logger_consolidated.info(
                        f"[ConsolidatedReport] Report generated successfully as CSV: {filename_save} ({num_rows} rows, {file_size} bytes)."
                    )
                    result_url = filename
                    # Return additional info
                    return {
                        "error": False,
                        "url": result_url,
                        "file_type": file_type,
                        "num_rows": num_rows,
                        "file_size": file_size,
                    }
                except Exception as csv_err:
                    logger_consolidated.exception(f"[ConsolidatedReport] Error saving CSV file: {str(csv_err)}")
                    return {"error": f"Ошибка при сохранении файла CSV: {str(csv_err)}", "url": None}

            else:
                # Save as Excel
                filename = f"{base_filename}.xlsx"
                filename_save = f"static/{filename}"
                file_type = "Excel"  # Use "Excel" for display
                try:
                    with pd.ExcelWriter(filename_save, engine="xlsxwriter") as writer:
                        combined_df.to_excel(writer, sheet_name="Consolidated Demand", index=False)

                        # Get the xlsxwriter workbook and worksheet objects
                        workbook = writer.book
                        worksheet = writer.sheets["Consolidated Demand"]

                        # Define formats
                        # Ensure '@' format for text columns to prevent scientific notation for numbers
                        text_format = workbook.add_format({"num_format": "@"})
                        float_format = workbook.add_format({"num_format": "0.00"})
                        integer_format = workbook.add_format({"num_format": "0"})  # Format for supid

                        # Apply formats to columns based on their final position and type
                        # 0=sku, 1=supid, 2=name, 3=q_day_sum, 4=q_sale_sum, 5=k_, 6=total_quantity, 7=quantity_m, 8=price, 9=Арт, 10=Бренд
                        worksheet.set_column("A:A", None, text_format)  # sku
                        worksheet.set_column("B:B", None, integer_format)  # supid
                        worksheet.set_column("C:C", None, text_format)  # name
                        worksheet.set_column("D:I", None, float_format)  # q_day_sum to price
                        worksheet.set_column("J:J", None, text_format)  # Арт
                        worksheet.set_column("K:K", None, text_format)  # Бренд

                    file_size = os.path.getsize(filename_save)  # Get file size
                    logger_consolidated.info(
                        f"[ConsolidatedReport] Report generated successfully as XLSX: {filename_save} ({num_rows} rows, {file_size} bytes)."
                    )
                    result_url = filename
                    # Return additional info
                    return {
                        "error": False,
                        "url": result_url,
                        "file_type": file_type,
                        "num_rows": num_rows,
                        "file_size": file_size,
                    }

                except Exception as excel_err:
                    logger_consolidated.exception(f"[ConsolidatedReport] Error saving Excel file: {str(excel_err)}")
                    return {"error": f"Ошибка при сохранении файла Excel: {str(excel_err)}", "url": None}

        except Exception as e:
            logger_consolidated.exception(f"[ConsolidatedReport] Error generating consolidated demand report: {str(e)}")
            return {"error": str(e), "url": None}

    def _get_emex_demand_data(self,
                              parent_ids: list[int],
                              start_date: datetime.date | None = None,
                              end_date: datetime.date | None = None,
                              k_filter: float | None = None) -> pd.DataFrame | None:
        """
        Fetches and transforms EMEX demand data for consolidation.
        Calculates demand metrics based on sales within the specified date range.

        Args:
            parent_ids: List of parent_ids from normalized brands to filter by.
            start_date: Start date (datetime.date object) for the date range.
            end_date: End date (datetime.date object) for the date range.

        Returns:
            A pandas DataFrame with EMEX data transformed into the consolidated
            report structure, or None if no data is found or an error occurs.
        """
        logger_emex.info(
            f"Fetching EMEX data for parent_ids: {len(parent_ids)}, date range: {start_date} to {end_date}, k_filter: {k_filter}"
        )

        if not parent_ids:
            logger_emex.warning("No valid parent_ids provided for EMEX query.")
            return None

        try:
            # SQL query to calculate demand metrics and k_ coefficient
            query = """
            WITH 
            -- Get sales data for the specified date range with normalized brand names
            filtered_sales AS (
                SELECT
                    ed.`Дата`,
                    ed.`Арт`,
                    ub.brand AS `Бренд`,  -- Using normalized brand name
                    ed.`Количество`,
                    ed.`СуммаПродажи`
                FROM sup_stat.emex_dif ed
                JOIN dif.zap_brand_cross zbc ON UPPER(REPLACE(ed.`Бренд`, ' ', '')) = UPPER(REPLACE(zbc.brand, ' ', ''))
                JOIN dif.unique_brands ub ON zbc.parent_id = ub.id
                WHERE ed.`Дата` >= %(start_date)s 
                  AND ed.`Дата` <= %(end_date)s
                  AND ed.`Количество` > 0
                  AND zbc.parent_id IN %(parent_ids)s
            ),
            
            -- Calculate total quantities, sales amounts, and days with sales
            sales_aggregated AS (
                SELECT
                    `Арт`,
                    `Бренд`,
                    SUM(`Количество`) AS total_quantity,
                    SUM(`СуммаПродажи`) AS total_sales_amount,
                    COUNT(DISTINCT `Дата`) AS days_with_sales,
                    dateDiff('day', toDate(%(start_date)s), toDate(%(end_date)s)) + 1 AS days_in_period
                FROM filtered_sales
                GROUP BY `Арт`, `Бренд`
                HAVING total_quantity > 0  -- Only include items with sales
            )
            
            -- Final calculation of demand metrics and k_ coefficient
            SELECT
                `Арт`,
                `Бренд`,
                -- Calculate monthly demand
                (total_quantity / NULLIF(days_with_sales, 0)) AS demand_quantity,
                -- Calculate average price
                (total_sales_amount / NULLIF(total_quantity, 0)) AS demand_price,
                -- Calculate k_ coefficient
                CASE 
                    WHEN days_in_period > 0 THEN (days_with_sales::Float64 / days_in_period) * 60
                    ELSE 0 
                END AS k_
            FROM sales_aggregated
            HAVING k_ >= %(k_filter)s
            """

            # Parameters for the query
            params = {"parent_ids": tuple(parent_ids), "start_date": start_date, "end_date": end_date, "k_filter": k_filter}

            logger_emex.debug(f"EMEX Query: {query}")
            logger_emex.debug(f"EMEX Params: {params}")

            result_query = self.client.execute(query, params=params)

            # Explicitly define the columns returned by the query
            query_columns = ["Арт", "Бренд", "demand_quantity", "demand_price", "k_"]
            df_emex = pd.DataFrame(result_query, columns=query_columns)

            if df_emex.empty:
                logger_emex.info("No EMEX data returned from query.")
                return None

            logger_emex.info(f"Fetched {len(df_emex)} rows from EMEX.")

            # --- Transform the DataFrame to match the consolidated format ---

            # Create sku column
            df_emex["Арт"] = df_emex["Арт"].astype(str).fillna("")
            df_emex["Бренд"] = df_emex["Бренд"].astype(str).fillna("")
            df_emex["sku"] = df_emex["Бренд"] + "|" + df_emex["Арт"]

            # Add required columns with specific values or placeholders
            df_emex["supid"] = 1
            df_emex["name"] = "EMEX"

            # Map calculated demand metrics to consolidated columns
            df_emex["quantity_m"] = pd.to_numeric(df_emex["demand_quantity"], errors="coerce").fillna(0.0)
            df_emex["price"] = pd.to_numeric(df_emex["demand_price"], errors="coerce").fillna(0.0)

            # Add other DIF-specific columns as placeholders
            df_emex["q_day_sum"] = 0.0
            df_emex["q_sale_sum"] = 0.0
            df_emex["total_quantity"] = 0.0

            # Ensure correct columns and order for combining
            final_columns = [
                "sku",
                "supid",
                "name",
                "q_day_sum",
                "q_sale_sum",
                "k_",
                "total_quantity",
                "quantity_m",
                "price",
                "Арт",  # Keep the original EMEX Art and Brand columns
                "Бренд",
            ]
            df_emex = df_emex[final_columns].copy()

            logger_emex.info("EMEX data transformed successfully.")
            return df_emex

        except Exception as e:
            logger_emex.exception(f"Error fetching or transforming EMEX data: {str(e)}")
            return None
