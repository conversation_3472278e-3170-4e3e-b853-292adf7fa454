"""
Logging configuration for the mail processing system.

1. Console output (stdout):
   - Level: INFO and above
   - Colored output for better readability
   - Shows timestamp, level, source location, and message

2. File output (/src/logs/mail_YYYY-MM-DD.log):
   - Level: DEBUG and above
   - Daily rotation of log files
   - 30-day retention policy
   - Compress logs older than 3 days
   - Detailed formatting with timestamp and source information

Format includes:
    - Timestamp: YYYY-MM-DD HH:mm:ss
    - Log Level: DEBUG/INFO/WARNING/ERROR/CRITICAL
    - Source: filename:function:line
    - Message: The actual log message
"""

import logging
import sys

from loguru import logger

# Removing default logger to add custom handler for better formatting anf file handling
logger.remove()

class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            if frame.f_back:
                frame = frame.f_back
                depth += 1
            else:
                break

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# Simplified and generalized formatter
log_format = (
    "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
    "<level>{level: <8}</level> | "
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>\n"
)

logger.add(
    sys.stdout,
    format=log_format,
    level="INFO",
    colorize=True,
    backtrace=True,
    diagnose=True,
    enqueue=True
)

# Generalized log file names
logger.add(
    "/app/logs/debug_{time:YYYY-MM-DD}.log",
    format=log_format,
    rotation="1 day",
    retention="30 days",
    compression="zip",
    level="DEBUG",
    backtrace=True,
    diagnose=True,
    enqueue=True
)

logger.add(
    "/app/logs/errors_{time:YYYY-MM-DD}.log",
    format=log_format,
    level="ERROR",
    rotation="1 week",
    retention="3 months",
    compression="zip",
    backtrace=True,
    diagnose=True
)


def setup_logging():
    """
    Initializes the logging configuration and intercepts standard logging.
    """
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    logger.info("Logging initialized and standard library logging intercepted.")
