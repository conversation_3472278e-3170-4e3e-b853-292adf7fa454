"""
This script reads a large tab-separated value (TSV) file in chunks, processes the data, 
and uploads it to the `dif.item_catalog_stage_legacy` table in a ClickHouse database.

It is designed to handle large files efficiently by processing them in smaller pieces, 
and it displays a progress bar to monitor the upload process.

The script performs the following steps:
1.  Reads data from a specified TSV file in chunks.
2.  Renames the columns from Russian to English based on a predefined mapping.
3.  Fills any missing (NA) values with empty strings.
4.  Reorders the columns to match the target table schema.
5.  Connects to ClickHouse and inserts the processed data chunk by chunk.

Example of data transformation:

Input from TSV file (`out2.txt`):
-------------------------------------------------------------------
Арт\tБренд\tВес\tДлина\tВысота\tШирина
MMR190876\tMitsubishi\t0.316\t\t\t
-------------------------------------------------------------------

Resulting row inserted into ClickHouse table (`dif.item_catalog_stage_legacy`):
-------------------------------------------------------------------
- part_number: 'MMR190876'
- brand: 'Mitsubishi'
- weight_kg_raw: '0.316'
- length_mm_raw: '' (empty string)
- height_mm_raw: '' (empty string)
- width_mm_raw: '' (empty string)
-------------------------------------------------------------------
"""
import os
import sys

import clickhouse_connect
import pandas as pd
from loguru import logger
from tqdm import tqdm

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from services.config_info import ClickInfo

# Configure logger
log_file_path = os.path.join(os.path.dirname(__file__), 'upload_vgh_legacy_debug.log')
logger.add(log_file_path, rotation="10 MB", level="DEBUG")

# Define the column mapping
COLUMN_MAPPING = {
    "Арт": "part_number",
    "Бренд": "brand",
    "Вес": "weight_kg_raw",
    "Длина": "length_mm_raw",
    "Высота": "height_mm_raw",
    "Ширина": "width_mm_raw",
}

# Define the file path
FILE_PATH = "out2.txt"
CHUNK_SIZE = 100000  # Process 100,000 rows at a time

def upload_data():
    """
    Reads data from the TSV file in chunks, processes it, and uploads it to ClickHouse.
    """
    logger.info("Starting data upload process for legacy table.")
    try:
        logger.info("Connecting to ClickHouse...")
        client = clickhouse_connect.get_client(
            host=ClickInfo.host_test,
            user=ClickInfo.settings_test['user'],
            password=ClickInfo.settings_test['password'],
            connect_timeout=120,
            send_receive_timeout=99999,
        )
        logger.info("Successfully connected to ClickHouse.")

        # Get total number of lines for tqdm progress bar
        with open(FILE_PATH, 'r', encoding='utf-8') as f:
            total_lines = sum(1 for line in f)

        logger.info(f"Reading TSV file from: {FILE_PATH} in chunks of {CHUNK_SIZE}.")
        
        with pd.read_csv(FILE_PATH, sep='\t', dtype=str, engine='python', chunksize=CHUNK_SIZE) as reader:
            with tqdm(total=total_lines, unit='lines', desc="Uploading to ClickHouse") as pbar:
                for chunk in reader:
                    # Fill NA values with empty strings before insertion
                    chunk.fillna('', inplace=True)

                    # Select and rename columns
                    df_to_upload = chunk[list(COLUMN_MAPPING.keys())].copy()
                    df_to_upload.rename(columns=COLUMN_MAPPING, inplace=True)

                    # Reorder columns
                    df_to_upload = df_to_upload[[
                        "part_number",
                        "brand",
                        "weight_kg_raw",
                        "length_mm_raw",
                        "height_mm_raw",
                        "width_mm_raw",
                    ]]

                    # Insert chunk into ClickHouse
                    client.insert_df("dif.item_catalog_stage_legacy", df_to_upload)
                    pbar.update(len(chunk))

        logger.success("Successfully uploaded all data to dif.item_catalog_stage_legacy.")
        print("Successfully uploaded all data to dif.item_catalog_stage_legacy.")

    except pd.errors.ParserError as pe:
        logger.error(f"Pandas parsing error: {pe}")
        print(f"A parsing error occurred: {pe}")
    except Exception as e:
        logger.exception("An unexpected error occurred during the upload process.")
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    upload_data()