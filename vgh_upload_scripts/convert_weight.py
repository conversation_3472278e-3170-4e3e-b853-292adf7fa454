
"""
This script converts the weight column (the 3rd column) in a tab-separated file 
from grams to kilograms. It reads from an input file, processes the data, and 
writes the result to a new output file.

The script assumes the weight is in the third column (index 2). If the value in 
that column is a valid integer, it is divided by 1000 to convert it from 
grams to kilograms. The new value is then written back to the file as a string.

Example of conversion:

Input file (`input.txt`):
-------------------------------------------------------------------
Арт\tБренд\tВес\tДлина\tВысота\tШирина
MMR190876\tMitsubishi\t316\t\t\t
MMR190706\tMitsubishi\t27\t\t\t
MMR190808\tMitsubishi\t2686\t\t\t
-------------------------------------------------------------------

Output file (`output.txt`):
-------------------------------------------------------------------
Арт\tБренд\tВес\tДлина\tВысота\tШирина
MMR190876\tMitsubishi\t0.316\t\t\t
MMR190706\tMitsubishi\t0.027\t\t\t
MMR190808\tMitsubishi\t2.686\t\t\t
-------------------------------------------------------------------
"""
import csv

input_file = 'input.txt'
output_file = 'output.txt'

with open(input_file, 'r', encoding='utf-8') as infile, open(output_file, 'w', encoding='utf-8', newline='') as outfile:
    reader = csv.reader(infile, delimiter='\t')
    writer = csv.writer(outfile, delimiter='\t')

    header = next(reader)
    writer.writerow(header)

    for row in reader:
        if len(row) > 2 and row[2].strip().isdigit():
            grams = int(row[2])
            kilograms = grams / 1000
            row[2] = str(kilograms)
        writer.writerow(row)
