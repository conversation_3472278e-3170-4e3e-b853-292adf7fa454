"""
The upload_data() script reads a CSV file containing product information, processes the data,
and uploads it to the `dif.item_catalog_stage_full` table in a ClickHouse database.

The script performs the following steps:
1.  Reads data from a specified CSV file.
2.  Renames the columns from Russian to English names based on a predefined mapping.
3.  Fills any missing (NA) values with empty strings.
4.  Adds a new column `is_bulky_raw` and sets its value to `None` (which becomes NULL in the database).
5.  Reorders the columns to match the target table schema.
6.  Connects to ClickHouse and inserts the processed data in a single batch.

Example of data transformation:

Input from CSV file (`ВГХ Китай2 27.08.2025.csv`):
---------------------------------------------------------------------------------------------------------------------
Б<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> кг,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,"Объем, куб.cм"
AISIN,AST021,ДАТЧИК ДОРОЖНОГО ПРОСВЕТА,,224,66,178,
---------------------------------------------------------------------------------------------------------------------

Resulting row inserted into ClickHouse table (`dif.item_catalog_stage_full`):
---------------------------------------------------------------------------------------------------------------------
- brand: 'AISIN'
- part_number: 'AST021'
- nomenclature: 'ДАТЧИК ДОРОЖНОГО ПРОСВЕТА'
- is_bulky_raw: NULL
- weight_kg_raw: '' (empty string)
- length_mm_raw: '224'
- height_mm_raw: '66'
- width_mm_raw: '178'
- volume_cm3_raw: '' (empty string)
---------------------------------------------------------------------------------------------------------------------
"""

import os
import sys

import clickhouse_connect
import pandas as pd
from loguru import logger

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from services.config_info import ClickInfo

# Configure logger
log_file_path = os.path.join(os.path.dirname(__file__), 'upload_vgh_debug.log')
logger.add(log_file_path, rotation="10 MB", level="DEBUG")

# Define the column mapping
COLUMN_MAPPING = {
    "Бренд": "brand",
    "Артикул": "part_number",
    "Номенклатура": "nomenclature",
    "Вес кг": "weight_kg_raw",
    "ДлинаММ": "length_mm_raw",
    "ВысотаММ": "height_mm_raw",
    "ШиринаММ": "width_mm_raw",
    "Объем, куб.cм": "volume_cm3_raw",
}

# Define the file path
FILE_PATH = "ВГХ Китай2 27.08.2025.csv"

def upload_data():
    logger.info("Starting data upload process.")
    try:
        logger.info(f"Reading CSV file from: {FILE_PATH}")
        df = pd.read_csv(FILE_PATH, sep=',', dtype=str, engine='python')
        logger.info(f"Successfully read CSV. DataFrame shape: {df.shape}")

        # Fill NA values with empty strings before insertion
        df.fillna('', inplace=True)
        logger.info("Filled NA values with empty strings.")

        logger.info("Selecting and renaming columns.")
        df_to_upload = df[list(COLUMN_MAPPING.keys())].copy()
        df_to_upload.rename(columns=COLUMN_MAPPING, inplace=True)
        logger.info("Columns selected and renamed.")

        logger.info("Adding 'is_bulky_raw' column.")
        df_to_upload["is_bulky_raw"] = None

        logger.info("Reordering columns.")
        df_to_upload = df_to_upload[[
            "brand",
            "part_number",
            "nomenclature",
            "is_bulky_raw",
            "weight_kg_raw",
            "length_mm_raw",
            "height_mm_raw",
            "width_mm_raw",
            "volume_cm3_raw",
        ]]
        logger.info("Columns reordered.")


        logger.info("Connecting to ClickHouse...")
        client = clickhouse_connect.get_client(
            host=ClickInfo.host_test,
            user=ClickInfo.settings_test['user'],
            password=ClickInfo.settings_test['password'],
            connect_timeout=120,  # Таймаут на подключение (секунды)
            send_receive_timeout=99999,  # Таймаут на отправку/получение данных (секунды)
        )
        logger.info("Successfully connected to ClickHouse.")

        logger.info(f"Inserting {len(df_to_upload)} rows into the table using insert_df()...")
        client.insert_df("dif.item_catalog_stage_full", df_to_upload)

        logger.success(f"Successfully uploaded {len(df_to_upload)} rows to dif.item_catalog_stage_full.")
        print(f"Successfully uploaded {len(df_to_upload)} rows to dif.item_catalog_stage_full.")

    except pd.errors.ParserError as pe:
        logger.error(f"Pandas parsing error: {pe}")
        print(f"A parsing error occurred: {pe}")
    except Exception as e:
        logger.exception("An unexpected error occurred during the upload process.")
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    upload_data()