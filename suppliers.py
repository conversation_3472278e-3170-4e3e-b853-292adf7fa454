import os
import threading
import time
import uuid
import zipfile
from datetime import datetime
from io import BytesIO
from typing import Dict, Any

import pandas as pd
import psutil
from clickhouse_driver import Client
from dotenv import load_dotenv
from flask import (
    Blueprint,
    render_template,
    jsonify,
    request,
    redirect,
    url_for,
    send_file,
    Response,
)
from flask_jwt_extended import jwt_required
from loguru import logger
from pytz import timezone

from services.config_info import ClickInfo, InputFileConfig
from services.redis_service import (
    get_export_progress_by_job, set_export_progress_by_job, update_export_progress_by_job
)

load_dotenv()
moscow_tz = timezone("Europe/Moscow")
app_key = os.getenv("CLIENT_APP_KEY_ADMIN")
PORT_SCHED = os.getenv("PORT_SCHED")
supplier_bp = Blueprint("supplier", __name__)

# Directory for storing temporary export files
EXPORT_FILES_DIR = os.path.join(os.getcwd(), "pricelists")
os.makedirs(EXPORT_FILES_DIR, exist_ok=True)


def _find_column_name(columns: list[str], aliases: list[str]) -> str | None:
    """Finds the first matching column name from a list of aliases, case-insensitively."""
    column_map: dict[str, str] = {col.lower(): col for col in columns}
    for alias in aliases:
        if alias.lower() in column_map:
            return column_map[alias.lower()]
    return None


def process_all_brands_export(form_data: Dict[str, Any], job_id: str) -> None:
    """
    Обрабатывает экспорт всех брендов в фоновом потоке.

    Создает ZIP-архив с данными по всем поставщикам и обновляет прогресс в Redis.

    Args:
        form_data: Данные формы с параметрами экспорта (даты начала и окончания)
        job_id: Уникальный идентификатор задания экспорта
    """
    try:
        logger.info(f"Запуск фоновой обработки для задачи ID: {job_id}")
        client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])

        start_date = form_data.get("start_date", "")
        end_date = form_data.get("end_date", "")
        date_range = f"{start_date}_to_{end_date}"

        suppliers_query = """
            SELECT id, name
            FROM sup_stat.sup_partners
        """
        suppliers = client.execute(suppliers_query)
        total_suppliers = len(suppliers)
        logger.info(f"Найдено {total_suppliers} поставщиков для задачи {job_id}")

        # Update progress in Redis
        update_export_progress_by_job(job_id, {
            "total_suppliers": total_suppliers,
            "message": f"Найдено {total_suppliers} поставщиков для обработки"
        })

        if suppliers:
            logger.info(f"Пример поставщиков для задачи {job_id}: {suppliers[:3]}")

        zip_filename = f"all_brands_{date_range}.zip"
        zip_path = os.path.join(EXPORT_FILES_DIR, zip_filename)

        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zip_file:
            for index, (sup_id, sup_name) in enumerate(suppliers):
                # Check for cancellation
                current_progress = get_export_progress_by_job(job_id)
                if current_progress is None:
                    logger.warning(f"Не удалось получить прогресс для задачи {job_id}, предполагается, что задача должна продолжаться")
                elif current_progress.get("status") == "cancelled":
                    logger.info(f"Процесс экспорта для задачи {job_id} был отменен, обработка остановлена")
                    break

                try:
                    update_export_progress_by_job(job_id, {
                        "current_supplier": f"{sup_name} (ID: {sup_id})",
                        "message": f'Обработка поставщика {index + 1} из {total_suppliers}: "{sup_name}"'
                    })

                    logger.info(f"Обработка поставщика: {sup_name} (ID: {sup_id}) для задачи {job_id}")

                    # Check if there's any data for this supplier
                    check_query = """
                        SELECT COUNT(*)
                        FROM sup_stat.dif_partners
                        WHERE sup_id = %(sup_id)s AND dateupd BETWEEN %(start)s AND %(end)s
                    """
                    check_params = {"sup_id": sup_id, "start": start_date, "end": end_date}
                    count_result = client.execute(check_query, check_params)
                    row_count = count_result[0][0] if count_result else 0
                    logger.info(f"Найдено {row_count} строк для поставщика ID {sup_id} в задаче {job_id}")

                    update_export_progress_by_job(job_id, {
                        "message": f"Найдено {row_count} строк. Начинаем обработку..."
                    })

                    if row_count == 0:
                        logger.info(f"Данные для поставщика {sup_name} (ID: {sup_id}) в задаче {job_id} не найдены")

                        # Update empty suppliers list
                        current_progress = get_export_progress_by_job(job_id)
                        empty_suppliers = current_progress.get("empty_suppliers", [])
                        supplier_entry = f"{sup_name} (ID: {sup_id})"

                        if supplier_entry not in empty_suppliers:  # avoid duplicates
                            empty_suppliers.append(supplier_entry)

                        update_export_progress_by_job(job_id, {
                            "message": f"Не найдено данных для поставщика",
                            "empty_suppliers": empty_suppliers,
                            "processed_suppliers": index + 1
                        })
                        continue

                    # Query data for this supplier
                    query = """
                        SELECT
                            d.b as brand,
                            d.a as article,
                            d.s_a as alternative,
                            d.q as quantity,
                            d.p as price,
                            d.c as currency,
                            sp.name as supplier,
                            d.dateupd as date
                        FROM sup_stat.dif_partners d
                        LEFT JOIN sup_stat.sup_partners sp ON d.sup_id = sp.id
                        WHERE d.sup_id = %(sup_id)s AND d.dateupd BETWEEN %(start)s AND %(end)s
                    """
                    params = {"sup_id": sup_id, "start": start_date, "end": end_date}
                    result = client.execute(query, params)

                    logger.info(f"Получено {len(result)} строк для поставщика {sup_name} в задаче {job_id}")

                    # Check for cancellation after retrieving data
                    current_progress = get_export_progress_by_job(job_id)
                    if current_progress is None:
                        logger.warning(
                            f"Не удалось получить прогресс для задачи {job_id} после получения данных, предполагается, что задача должна продолжаться")
                    elif current_progress.get("status") == "cancelled":
                        logger.info(
                            f"Процесс экспорта для задачи {job_id} был отменен после получения данных, обработка остановлена")
                        break

                    df = pd.DataFrame(
                        result,
                        columns=[
                            "brand",
                            "article",
                            "alternative",
                            "quantity",
                            "price",
                            "currency",
                            "supplier",
                            "date",
                        ],
                    )
                    df["alternative"] = df["alternative"].replace([0, None, pd.NA], "")

                    supplier_file = BytesIO()
                    file_extension = ".csv" if len(df) > 1000000 else ".xlsx"
                    filename = f"{sup_id}_{sup_name}_{date_range}{file_extension}"

                    # Check for cancellation before saving file
                    current_progress = get_export_progress_by_job(job_id)
                    if current_progress is None:
                        logger.warning(
                            f"Не удалось получить прогресс для задачи {job_id} перед сохранением файла, предполагается, что задача должна продолжаться")
                    elif current_progress.get("status") == "cancelled":
                        logger.info(
                            f"Процесс экспорта для задачи {job_id} был отменен перед сохранением файла, обработка остановлена")
                        break

                    update_export_progress_by_job(job_id, {
                        "message": f"Сохранение данных в файл {file_extension}"
                    })

                    if file_extension == ".csv":
                        df.to_csv(supplier_file, index=False, encoding="utf-8-sig")
                    else:
                        df.to_excel(supplier_file, index=False, engine="openpyxl")

                    supplier_file.seek(0)
                    zip_file.writestr(filename, supplier_file.getvalue())
                    logger.success(f"Файл {filename} успешно добавлен в ZIP-архив для задачи {job_id}")

                    # Update completed suppliers list
                    current_progress = get_export_progress_by_job(job_id)
                    completed_suppliers = current_progress.get("completed_suppliers", [])
                    supplier_entry = f"{sup_name} (ID: {sup_id})"

                    if supplier_entry not in completed_suppliers:
                        completed_suppliers.append(supplier_entry)

                    update_export_progress_by_job(job_id, {
                        "completed_suppliers": completed_suppliers,
                        "processed_suppliers": index + 1,
                        "message": f"Завершена обработка поставщика"
                    })

                except Exception as e:
                    logger.error(f"Ошибка при обработке поставщика {sup_name} (ID: {sup_id}) для задачи {job_id}: {str(e)}",
                                 exc_info=True)

                    # Update error suppliers list
                    current_progress = get_export_progress_by_job(job_id)
                    error_suppliers = current_progress.get("error_suppliers", [])
                    supplier_entry = f"{sup_name} (ID: {sup_id}): {str(e)}"

                    if supplier_entry not in error_suppliers:
                        error_suppliers.append(supplier_entry)

                    update_export_progress_by_job(job_id, {
                        "error_suppliers": error_suppliers,
                        "processed_suppliers": index + 1,
                        "message": f"Ошибка при обработке поставщика: {str(e)}"
                    })

        # Check final status
        current_progress = get_export_progress_by_job(job_id)
        if current_progress is None:
            logger.warning(f"Не удалось получить финальный прогресс для задачи {job_id}, предполагается, что задача должна завершиться")
        elif current_progress.get("status") == "cancelled":
            logger.info(f"Экспорт для задачи {job_id} был отменен, очистка")
            if os.path.exists(zip_path):
                os.remove(zip_path)
                logger.info(f"Удален отмененный файл экспорта: {zip_path}")
            return

        file_size_bytes = os.path.getsize(zip_path)
        file_size_mb = round(file_size_bytes / (1024 * 1024), 1)
        file_size_str = f"{file_size_mb}MB"

        # Update final status in Redis
        update_export_progress_by_job(job_id, {
            "status": "completed",
            "processed_suppliers": total_suppliers,
            "file_path": zip_path,
            "file_size": file_size_str,
            "message": f"Экспорт успешно завершен. Размер файла: {file_size_str}"
        })

        logger.success(f"Экспорт успешно завершен для задачи {job_id}, файл сохранен по пути: {zip_path}")

    except Exception as e:
        logger.critical(f"Критическая ошибка в фоновой обработке для задачи {job_id}: {str(e)}", exc_info=True)

        update_export_progress_by_job(job_id, {
            "status": "error",
            "message": f"Ошибка: {str(e)}"
        })


def is_file_in_use(file_path: str) -> bool:
    """
    Проверяет, используется ли файл в данный момент другим процессом.

    Args:
        file_path: Путь к проверяемому файлу

    Returns:
        bool: True если файл используется, False если нет
    """
    for proc in psutil.process_iter(["pid", "open_files"]):
        try:
            if any(file.path == file_path for file in proc.info["open_files"] or []):
                return True
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            continue
    return False


def normilize_brand(brand: str) -> str | None:
    """
    Нормализует название бренда, используя таблицу соответствия брендов.

    Поиск происходит без учета регистра символов.

    Args:
        brand: Исходное название бренда

    Returns:
        Нормализованное название бренда или исходное название, если соответствие не найдено
    """
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])

    query = f"""
        SELECT ub.brand
        FROM dif.zap_brand_cross zc
        JOIN dif.unique_brands ub ON zc.parent_id = ub.id
        WHERE lower(zc.brand) = lower('{brand}')
    """

    # Выполняем запрос
    result = client.execute(query)

    # Получаем значение brand
    brand_name = result[0][0] if result else None  # Если результат есть, берем первый элемент
    return brand_name if brand_name is not None else brand


@supplier_bp.route("/suppliers", methods=["GET"])
@jwt_required()
async def suppliers_page() -> Response:
    """
    Отображает страницу с информацией о поставщиках.

    Returns:
        Шаблон страницы поставщиков или перенаправление на страницу авторизации
    """
    access_token_cookie = request.cookies.get("access_token_cookie")
    refresh_token_cookie = request.cookies.get("access_token_cookie")

    if not access_token_cookie or not refresh_token_cookie:
        return redirect(url_for("auth.login"))

    return render_template("sup.html")


@supplier_bp.route("/load_sup_price", methods=["GET"])
@jwt_required()
async def price_page() -> Response:
    """
    Отображает страницу загрузки прайс-листов поставщиков.

    Returns:
        Шаблон страницы загрузки прайс-листов или перенаправление на страницу авторизации
    """
    access_token_cookie = request.cookies.get("access_token_cookie")
    refresh_token_cookie = request.cookies.get("access_token_cookie")

    if not access_token_cookie or not refresh_token_cookie:
        return redirect(url_for("auth.login"))

    return render_template("load_sup_price.html")


@supplier_bp.route("/export_pricelist", methods=["GET"])
@jwt_required()
async def export_pricelist_page() -> Response:
    """
    Отображает страницу экспорта прайс-листов.

    Returns:
        Шаблон страницы экспорта прайс-листов или перенаправление на страницу авторизации
    """
    access_token_cookie = request.cookies.get("access_token_cookie")
    refresh_token_cookie = request.cookies.get("access_token_cookie")
    if not access_token_cookie or not refresh_token_cookie:
        return redirect(url_for("auth.login"))
    return render_template("export_pricelist.html")


@supplier_bp.route("/get_sup_pricelist", methods=["POST"])
@jwt_required()
async def export_pricelist() -> tuple[Response, int] | Response:
    """
    Обрабатывает запрос на экспорт прайс-листов.

    Поддерживает два режима работы:
    1. Экспорт по одному бренду - возвращает файл непосредственно
    2. Экспорт по всем брендам - запускает фоновую задачу и возвращает идентификатор задачи

    Returns:
        Для одного бренда: файл с данными (Excel или CSV)
        Для всех брендов: JSON с идентификатором задачи
    """
    logger.info("Запуск функции export_pricelist")
    try:
        all_brands = request.form.get("all_brands", "False").lower() == "true"
        start_date = request.form.get("start_date", "")
        end_date = request.form.get("end_date", "")

        if all_brands:
            logger.info(f"Экспорт всех брендов за период: {start_date} - {end_date}")

            # Generate a unique job ID for this export
            job_id = str(uuid.uuid4())
            logger.info(f"Сгенерирован ID задачи: {job_id} для экспорта всех брендов")

            # Initialize progress tracking in Redis with the job ID
            set_export_progress_by_job(job_id, {
                "status": "processing",
                "total_suppliers": 0,
                "processed_suppliers": 0,
                "current_supplier": "",
                "completed_suppliers": [],
                "empty_suppliers": [],
                "error_suppliers": [],
                "start_time": datetime.now(timezone('UTC')).isoformat(),
                "message": "Запуск процесса экспорта...",
            })

            # Start the export process in a background thread
            thread = threading.Thread(
                target=process_all_brands_export,
                args=(request.form, job_id),
                daemon=True
            )
            thread.start()

            # Return the job ID immediately
            logger.info(f"Запущен фоновый поток для задачи ID: {job_id}")
            return jsonify({"status": "started", "job_id": job_id}), 200
        else:
            logger.info(f"Экспорт по файлу за период: {start_date} - {end_date}")
            client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])

            if 'pricelist_file' not in request.files:
                logger.error("Файл не найден в запросе")
                return jsonify({"error": "Файл не был предоставлен"}), 400

            file = request.files['pricelist_file']
            if file.filename == '':
                logger.error("Пустое имя файла")
                return jsonify({"error": "Файл не был выбран"}), 400

            try:
                df_from_file = pd.read_excel(file)
                brand_col = _find_column_name(list(df_from_file.columns), InputFileConfig.INPUT_BRAND_ALIASES)
                sku_col = _find_column_name(list(df_from_file.columns), InputFileConfig.INPUT_SKU_ALIASES)

                if not brand_col:
                    brand_aliases = InputFileConfig.INPUT_BRAND_ALIASES
                    logger.error(f"Не удалось найти колонку бренда. Ожидаемые варианты названия: {','.join(brand_aliases)}")
                    return jsonify({"error": f"Не удалось найти колонку бренда. Ожидаемые варианты названия: {', '.join(brand_aliases)}"}), 400
                
                if not sku_col:
                    sku_aliases = InputFileConfig.INPUT_SKU_ALIASES
                    logger.error(f"Не удалось найти колонку артикула. Ожидаемые варианты названия: {','.join(sku_aliases)}")
                    return jsonify({"error": f"Не удалось найти колонку артикула. Ожидаемые варианты названия: {', '.join(sku_aliases)}"}), 400
                
                logger.info(f"Используем колонку '{brand_col}' для бренда и '{sku_col}' для артикула.")

            except Exception as e:
                logger.error(f"Ошибка чтения Excel файла: {e}")
                return jsonify({"error": f"Ошибка при чтении файла: {e}"}), 400

            brand_article_map = {}
            for index, row in df_from_file.iterrows():
                brand = row[brand_col]
                article = row[sku_col]
                normalized_brand = normilize_brand(brand)
                if normalized_brand:
                    if normalized_brand not in brand_article_map:
                        brand_article_map[normalized_brand] = []
                    brand_article_map[normalized_brand].append(article)

            results_df_list = []
            for brand, articles in brand_article_map.items():
                logger.info(f"Выполнение запроса для бренда: {brand}, количество артикулов: {len(articles)}")
                query = """
                    SELECT
                        d.b as brand,
                        d.a as article,
                        d.s_a as alternative,
                        d.q as quantity,
                        d.p as price,
                        d.c as currency,
                        sp.name as supplier,
                        d.dateupd as date
                    FROM sup_stat.dif_partners d
                    LEFT JOIN sup_stat.sup_partners sp ON d.sup_id = sp.id
                    WHERE d.b = %(brand)s AND d.a IN %(articles)s AND d.dateupd BETWEEN %(start)s AND %(end)s
                """
                params = {"brand": brand, "articles": articles, "start": start_date, "end": end_date}
                result = client.execute(query, params)
                logger.info(f"Запрос выполнен. Получено {len(result)} строк для бренда {brand}")
                if result:
                    results_df_list.append(pd.DataFrame(result, columns=["brand", "article", "alternative", "quantity", "price", "currency", "supplier", "date"]))

            if not results_df_list:
                logger.info("Нет данных для экспорта")
                return jsonify({"error": "Нет данных для экспорта по указанным брендам и артикулам"}), 404

            df = pd.concat(results_df_list, ignore_index=True)
            df["alternative"] = df["alternative"].replace([0, None, pd.NA], "")

            logger.info(f"DataFrame создан. Форма: {df.shape}")

            output = BytesIO()
            # Using a generic filename since there are multiple brands
            filename_base = f"pricelist_export_{start_date}_to_{end_date}"

            if len(df) > 1000000:  # Excel limit is 1,048,576 rows
                logger.info("Обнаружен большой набор данных. Сохранение в CSV")
                df.to_csv(output, index=False, encoding="utf-8-sig")
                filename = f"{filename_base}.csv"
                mimetype = "text/csv"
            else:
                logger.info("Набор данных в пределах лимитов Excel. Сохранение в Excel")
                df.to_excel(output, index=False, engine="openpyxl")
                filename = f"{filename_base}.xlsx"
                mimetype = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

            output.seek(0)
            logger.success(f"Файл успешно подготовлен. Имя файла: {filename}")

            return send_file(output, as_attachment=True, download_name=filename, mimetype=mimetype)
    except Exception as e:
        logger.critical(f"Критическая ошибка в export_pricelist: {str(e)}", exc_info=True)

        if all_brands and 'job_id' in locals():
            # Update error status in Redis for the specific job
            update_export_progress_by_job(job_id, {
                "status": "error",
                "message": f"Ошибка: {str(e)}"
            })

        return jsonify({"error": "Произошла ошибка при обработке вашего запроса"}), 500


@supplier_bp.route("/download-export/<job_id>", methods=["GET"])
@jwt_required()
def download_export(job_id: str) -> Response:
    """
    Скачивает файл экспорта для завершенного задания.

    Проверяет, что задание завершено и файл существует, затем отправляет его клиенту.

    Args:
        job_id: Идентификатор задания экспорта

    Returns:
        Файл экспорта или JSON с ошибкой, если файл недоступен
    """
    try:
        logger.info(f"Эндпоинт скачивания вызван для задачи ID: {job_id}")

        # Get progress from Redis using the job-specific function
        from services.redis_service import get_export_progress_by_job
        progress = get_export_progress_by_job(job_id)

        if progress is None:
            logger.warning(f"Прогресс для задачи ID: {job_id} не найден")
            return jsonify({"error": "Задача не найдена"}), 404

        if progress["status"] != "completed":
            logger.warning(f"Задача {job_id} не завершена. Текущий статус: {progress['status']}")
            return jsonify({
                "error": "Экспорт еще не завершен",
                "status": progress["status"],
                "message": progress.get("message", "")
            }), 400

        file_path = progress.get("file_path")
        if not file_path:
            logger.error(f"Путь к файлу для завершенной задачи {job_id} не найден")
            return jsonify({"error": "Файл экспорта не найден"}), 404

        if not os.path.exists(file_path):
            logger.error(f"Файл не найден по пути: {file_path} для задачи {job_id}")
            return jsonify({"error": "Файл экспорта не найден на сервере"}), 404

        filename = os.path.basename(file_path)

        logger.info(f"Отправка файла {filename} для задачи {job_id}")

        # Start a background thread to delete the file after it's no longer in use
        threading.Thread(target=delayed_delete, args=(file_path,)).start()
        logger.info(f"Запланировано отложенное удаление для файла {file_path}")

        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype="application/zip"
        )
    except Exception as e:
        logger.critical(f"Ошибка при скачивании экспорта для задачи {job_id}: {str(e)}", exc_info=True)
        return jsonify({"error": f"Ошибка при скачивании экспорта: {str(e)}"}), 500


@supplier_bp.route("/download/<filename>")
def download_file(filename: str) -> Response:
    """
    Скачивает файл по указанному имени.

    Args:
        filename: Имя файла для скачивания

    Returns:
        Файл для скачивания
    """
    file_path = os.path.join("pricelists", filename)

    # Start a background thread to delete the file after it's no longer in use
    threading.Thread(target=delayed_delete, args=(file_path,)).start()
    logger.info(f"Запланировано отложенное удаление для файла {file_path}")

    return send_file(file_path, as_attachment=True)


def delayed_delete(path: str) -> None:
    """
    Ждет, пока файл не перестанет использоваться, затем удаляет его.

    Сначала ждет 60 секунд, затем проверяет использование файла каждые 10 секунд.

    Args:
        path: Путь к файлу, который нужно удалить
    """
    time.sleep(60)
    while is_file_in_use(path):
        time.sleep(10)
    if os.path.exists(path):
        os.remove(path)
        logger.info(f"Файл {path} удален.")


@supplier_bp.route("/delete_file", methods=["POST"])
@jwt_required()
def delete_file() -> Response:
    """
    Удаляет файл с задержкой в фоновом режиме.

    Получает имя файла из JSON-запроса и запускает фоновый поток для удаления.

    Returns:
        JSON с сообщением о статусе операции
    """
    data = request.get_json()
    filename = data.get("filename")
    file_path = os.path.join("pricelists", filename)
    threading.Thread(target=delayed_delete, args=(file_path,)).start()
    return jsonify({"status": "Файл будет удален через 60 секунд"}), 200


@supplier_bp.route("/export-status/<job_id>", methods=["GET"])
@jwt_required()
def get_export_status(job_id: str) -> Response:
    """
    Получает текущий статус задания экспорта.

    Извлекает информацию о прогрессе из Redis для указанного идентификатора задания.

    Args:
        job_id: Идентификатор задания экспорта

    Returns:
        JSON с информацией о прогрессе или сообщение об ошибке
    """
    try:
        logger.info(f"Эндпоинт статуса вызван для задачи ID: {job_id}")

        # Get progress from Redis using the job-specific function
        from services.redis_service import get_export_progress_by_job
        progress = get_export_progress_by_job(job_id)

        if progress is None:
            logger.warning(f"Прогресс для задачи ID: {job_id} не найден")
            return jsonify({"error": "Задача не найдена"}), 404

        logger.debug(f"Возвращение прогресса для задачи ID {job_id}: {progress}")
        return jsonify(progress), 200
    except Exception as e:
        logger.error(f"Ошибка при получении статуса экспорта для задачи {job_id}: {str(e)}", exc_info=True)
        return jsonify({"error": f"Ошибка при получении статуса задачи: {str(e)}"}), 500


@supplier_bp.route("/cancel-export/<job_id>", methods=["POST"])
@jwt_required()
def cancel_export(job_id: str) -> Response:
    """
    Отменяет процесс экспорта для указанного задания.

    Устанавливает статус задания в "cancelled". Этот статус проверяется в нескольких местах функции экспорта для быстрой отмены.

    Args:
        job_id: Идентификатор задания экспорта для отмены

    Returns:
        JSON с сообщением об успешной отмене или об ошибке
    """
    logger.info(f"Эндпоинт отмены вызван для задачи ID: {job_id}")

    # Get current progress to verify the job exists
    progress = get_export_progress_by_job(job_id)

    if progress is None:
        logger.warning(f"Невозможно отменить несуществующую задачу: {job_id}")
        return jsonify({"error": "Задача не найдена"}), 404

    # Update status in Redis for the specific job
    update_export_progress_by_job(job_id, {
        "status": "cancelled",
        "message": "Экспорт был отменен пользователем"
    })

    logger.info(f"Процесс экспорта для задачи {job_id} отменен пользователем")
    return jsonify({"status": "success", "message": "Экспорт отменен"}), 200