from io import BytesIO
from unittest.mock import MagicMock

import numpy as np
import pandas as pd
import pytest

from app import app


@pytest.fixture
def client():
    app.config["TESTING"] = True
    app.config["WTF_CSRF_ENABLED"] = False
    with app.test_client() as client:
        yield client

@pytest.fixture
def mock_db_client(mocker):
    """Mocks the ClickHouse client."""
    mock_client = MagicMock()
    mocker.patch("item_catalog.get_clickhouse_client", return_value=mock_client)
    return mock_client

def create_test_excel(data):
    """Helper function to create an in-memory Excel file from a dictionary."""
    df = pd.DataFrame(data)
    output = BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return (output, "test.xlsx")

def test_upload_page_loads(client):
    response = client.get("/item_catalog/upload")
    assert response.status_code == 200
    assert "Обновить базу ВГХ".encode("utf-8") in response.data

def test_download_template(client):
    response = client.get("/item_catalog/download_template")
    assert response.status_code == 200
    assert response.headers["Content-Disposition"] == "attachment; filename=item_catalog_template.xlsx"

def test_upload_admin_mode(client, mock_db_client):
    """Test that admin mode directly inserts the prepared data."""
    test_data = {
        "Бренд": ["TEST"],
        "Артикул": ["T001"],
        "Номенклатура": ["Test Part"],
        "Габаритная": ["да"],
        "Номенклатура англ.": ["Test Part ENG"],
        "Вес кг": [1.5],
        "ДлинаММ": [100],
        "ВысотаММ": [50],
        "ШиринаММ": [20],
        "Объем, куб.cм": [1000]
    }
    file = create_test_excel(test_data)
    
    response = client.post(
        "/item_catalog/upload", 
        data={"file": file, "admin_mode": "on"}, 
        content_type="multipart/form-data"
    )

    assert response.status_code == 302
    mock_db_client.insert_df.assert_called_once()
    inserted_df = mock_db_client.insert_df.call_args[0][1]
    
    assert inserted_df.iloc[0]['brand'] == "TEST"
    assert inserted_df.iloc[0]['part_number'] == "T001"
    assert bool(inserted_df.iloc[0]['is_bulky']) is True
    assert inserted_df.iloc[0]['weight_kg'] == 1.5

def test_upload_default_mode(client, mock_db_client):
    """Test that default mode correctly merges data, updating only nulls."""
    # Data from user's uploaded file
    new_data = {
        "Бренд": ["TEST"],
        "Артикул": ["T001"],
        "Номенклатура": ["New Name"], # Should NOT update existing
        "Габаритная": ["да"], # Should update null
        "Номенклатура англ.": ["New English Name"], # Should update null
        "Вес кг": [9.9], # Should NOT update existing
        "ДлинаММ": [None], # Should NOT overwrite existing with null
        "ВысотаММ": [55], # Should update null
        "ШиринаММ": [25], # Should update null
        "Объем, куб.cм": [None] # Should NOT overwrite existing with null
    }
    file = create_test_excel(new_data)

    # Data already in the database
    existing_data = {
        "brand": ["TEST"],
        "part_number": ["T001"],
        "nomenclature": ["Old Name"],
        "is_bulky": [None], # Is Null
        "name_eng": [None], # Is Null
        "weight_kg": [1.5],
        "length_mm": [100],
        "height_mm": [None], # Is Null
        "width_mm": [None], # Is Null
        "volume_cm3": [1000]
    }
    mock_db_client.query_df.return_value = pd.DataFrame(existing_data)

    response = client.post("/item_catalog/upload", data={"file": file}, content_type="multipart/form-data")

    assert response.status_code == 302
    mock_db_client.insert_df.assert_called_once()
    final_df = mock_db_client.insert_df.call_args[0][1]
    
    # Verify the merged logic
    assert len(final_df) == 1
    result = final_df.iloc[0]
    assert result["nomenclature"] == "Old Name"  # Was not null, should not change
    assert result["weight_kg"] == 1.5          # Was not null, should not change
    assert bool(result["is_bulky"]) is True          # Was null, should be updated
    assert result["name_eng"] == "New English Name" # Was null, should be updated
    assert result["height_mm"] == 55.0         # Was null, should be updated
    assert result["length_mm"] == 100.0        # Should remain, as new value was null


def test_upload_excludes_db_managed_columns(client, mock_db_client):
    """Test that db-managed columns like created_at are not included in the insert."""
    test_data = {
        "Бренд": ["TEST"],
        "Артикул": ["T001"],
        "Номенклатура": ["Test Part"],
        "Габаритная": ["да"],
        "Номенклатура англ.": ["Test Part ENG"],
        "Вес кг": [1.5],
        "ДлинаММ": [100],
        "ВысотаММ": [50],
        "ШиринаММ": [20],
        "Объем, куб.cм": [1000]
    }
    file = create_test_excel(test_data)

    # Simulate that the database returns these columns
    existing_data = {
        "brand": ["TEST"],
        "part_number": ["T001"],
        "nomenclature": ["Old Name"],
        "is_bulky": [None],
        "name_eng": [None],
        "weight_kg": [None],
        "length_mm": [None],
        "height_mm": [None],
        "width_mm": [None],
        "volume_cm3": [None],
        "created_at": [pd.to_datetime("2023-01-01")],
        "updated_at": [pd.to_datetime("2023-01-01")]
    }
    mock_db_client.query_df.return_value = pd.DataFrame(existing_data)

    client.post("/item_catalog/upload", data={"file": file}, content_type="multipart/form-data")

    mock_db_client.insert_df.assert_called_once()
    final_df = mock_db_client.insert_df.call_args[0][1]

    # The crucial test: assert that these columns are NOT in the final DataFrame
    assert "created_at" not in final_df.columns
    assert "updated_at" not in final_df.columns


def test_default_mode_handles_db_duplicates(client, mock_db_client):
    """Tests that default mode does not crash if the DB returns duplicate keys."""
    # Data from user's uploaded file
    new_data = {
        "Бренд": ["TEST"],
        "Артикул": ["D001"],
        "Номенклатура": ["New Part"],
        "Габаритная": ["да"],
        "Номенклатура англ.": ["New Part ENG"],
        "Вес кг": [9.9],
        "ДлинаММ": [100],
        "ВысотаММ": [50],
        "ШиринаММ": [20],
        "Объем, куб.cм": [1000]
    }
    file = create_test_excel(new_data)

    # Simulate the database returning duplicate rows for the same part
    existing_data_with_duplicates = {
        "brand": ["TEST", "TEST"],
        "part_number": ["D001", "D001"],
        "nomenclature": ["Old Name", "Old Name"],
        "is_bulky": [None, None],
        "name_eng": [None, None],
        "weight_kg": [1.0, 1.0],
        "length_mm": [None, None],
        "height_mm": [None, None],
        "width_mm": [None, None],
        "volume_cm3": [None, None]
    }
    mock_db_client.query_df.return_value = pd.DataFrame(existing_data_with_duplicates)

    # Act: Perform the upload
    response = client.post("/item_catalog/upload", data={"file": file}, content_type="multipart/form-data")

    # Assert: The main assertion is that the code did not crash and redirected successfully.
    assert response.status_code == 302
    # And that it proceeded to call the insert function.
    mock_db_client.insert_df.assert_called_once()


def test_default_mode_no_updates(client, mock_db_client):
    """Tests that updated_count is 0 if no db fields are updated."""
    # File has full data, identical to what's in the DB
    file_data = {
        "Бренд": ["TEST"],
        "Артикул": ["T001"],
        "Номенклатура": ["Test Part"],
        "Габаритная": [False],
        "Номенклатура англ.": ["Test Part ENG"],
        "Вес кг": [1.5],
        "ДлинаММ": [100],
        "ВысотаММ": [50],
        "ШиринаММ": [20],
        "Объем, куб.cм": [1000]
    }
    file = create_test_excel(file_data)

    # DB also has the exact same data (no nulls to update)
    existing_data = {
        "brand": ["TEST"],
        "part_number": ["T001"],
        "nomenclature": ["Test Part"],
        "is_bulky": [False],
        "name_eng": ["Test Part ENG"],
        "weight_kg": [1.5],
        "length_mm": [100],
        "height_mm": [50],
        "width_mm": [20],
        "volume_cm3": [1000]
    }
    mock_db_client.query_df.return_value = pd.DataFrame(existing_data)

    # Act
    response = client.post("/item_catalog/upload", data={"file": file}, content_type="multipart/form-data")
    # Assert that the post redirects
    assert response.status_code == 302

    # Check the session for the correct flash message
    with client.session_transaction() as session:
        flashed_messages = session.get("_flashes", [])
        assert len(flashed_messages) > 0
        _category, message = flashed_messages[0]
        assert "Обновлено существующих строк: 0" in message


def test_parses_comma_decimals_correctly(client, mock_db_client):
    """Tests that numbers with comma decimals (e.g., '0,01') are parsed correctly."""
    test_data = {
        "Бренд": ["TEST"],
        "Артикул": ["T001"],
        "Номенклатура": ["Test Part"],
        "Габаритная": ["да"],
        "Номенклатура англ.": ["Test Part ENG"],
        "Вес кг": ["0,01"],  # Comma decimal
        "ДлинаММ": [100],
        "ВысотаММ": [50],
        "ШиринаММ": [20],
        "Объем, куб.cм": ["1000,5"]
    }
    file = create_test_excel(test_data)
    
    client.post(
        "/item_catalog/upload", 
        data={"file": file, "admin_mode": "on"}, 
        content_type="multipart/form-data"
    )

    mock_db_client.insert_df.assert_called_once()
    inserted_df = mock_db_client.insert_df.call_args[0][1]
    
    # Assert that the comma was correctly converted to a dot and parsed as a float
    assert inserted_df.iloc[0]['weight_kg'] == 0.01
    assert inserted_df.iloc[0]['volume_cm3'] == 1000.5


def test_treats_zero_as_null(client, mock_db_client):
    """Tests that a 0 in a numeric column is treated as a NULL (NaN) value."""
    test_data = {
        "Бренд": ["TEST"],
        "Артикул": ["T002"],
        "Номенклатура": ["Test Part 2"],
        "Габаритная": ["нет"],
        "Номенклатура англ.": [""],
        "Вес кг": [0],  # Zero value
        "ДлинаММ": [100],
        "ВысотаММ": [50],
        "ШиринаММ": [20],
        "Объем, куб.cм": ["0.0"]
    }
    file = create_test_excel(test_data)
    
    client.post(
        "/item_catalog/upload", 
        data={"file": file, "admin_mode": "on"}, 
        content_type="multipart/form-data"
    )

    mock_db_client.insert_df.assert_called_once()
    inserted_df = mock_db_client.insert_df.call_args[0][1]
    
    # Assert that the 0 values were correctly converted to NaN
    assert np.isnan(inserted_df.iloc[0]['weight_kg'])
    assert np.isnan(inserted_df.iloc[0]['volume_cm3'])