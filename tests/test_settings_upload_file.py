from __future__ import annotations

from flask import Flask


class _MockClient:
    def __init__(self, *args, **kwargs) -> None:
        pass

    def execute(self, query: str):
        # Pretend we have NOT uploaded this month: empty result set
        return []

    def disconnect(self) -> None:
        return None


class _MockClicData:
    def __init__(self, client, settings, filters) -> None:
        self._client = client

    async def update_emex_dif(self, df_new):
        # Return a simple success payload with row count
        rows = int(getattr(df_new, "shape", (0, 0))[0])
        return {"status": "ok", "rows": rows}


class _MockClickInfo:
    host = "localhost"
    settings = {"user": "u", "password": "p"}


def _noop_verify(*args, **kwargs) -> None:
    return None


def test_upload_txt_emex_validate_chunk_happy_path(mocker):
    """Test the chunk validation endpoint with a correctly formatted chunk."""
    import settings as settings_mod

    mocker.patch("flask_jwt_extended.view_decorators.verify_jwt_in_request", _noop_verify)

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.register_blueprint(settings_mod.settings_bp)

    sample_path = "tests/assets/sample_good_emex.txt"

    with app.test_client() as client, open(sample_path, "rb") as f:
        chunk = f.read(4096)
        resp = client.post("/upload_txt_emex_validate", data=chunk, content_type="application/octet-stream")

    assert resp.status_code == 200
    payload = resp.get_json()
    assert payload == {"success": True}


def test_upload_txt_emex_validate_chunk_unhappy_path(mocker):
    """Test the chunk validation endpoint with a badly formatted chunk."""
    import settings as settings_mod

    mocker.patch("flask_jwt_extended.view_decorators.verify_jwt_in_request", _noop_verify)

    app = Flask(__name__)
    app.config["TESTING"] = True
    app.register_blueprint(settings_mod.settings_bp)

    sample_path = "tests/assets/sample_bad_emex.txt"

    with app.test_client() as client, open(sample_path, "rb") as f:
        chunk = f.read(4096)
        resp = client.post("/upload_txt_emex_validate", data=chunk, content_type="application/octet-stream")

    assert resp.status_code == 200
    payload = resp.get_json()
    assert payload["success"] is False
    assert "error" in payload