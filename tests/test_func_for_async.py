import pytest

from func_for_async import validate_emex_chunk

# --- Test Data ---

GOOD_HEADER = "Дата\tАрт\tБренд\tЛогоПоставщика\tИННПоставщика\tНаименованиПоставщика\tКоличество\tЦенаПокупки\tСуммаПокупки\tЦенаПродажи\tСуммаПродажи\tСклад\tЛогоКлиента\tИННКлиента\tНазваниеКлиента\tЛогоПрайса"
GOOD_ROW = "2024-12-03 00:00:26.613\t04E107065BJ\tVAG\tALIK\tALJJ\t3662005006\tООО АЛС-ТУР\t4\t4200\t16800\t4583,4\t18333,6\tСП\tMGOE\t7713476966\tООО Движок"
BAD_ROW_FEW_COLUMNS = "2024-12-03 00:00:26.613\t04E107065BJ\tVAG\tALIK"


@pytest.mark.parametrize(
    "test_name, chunk_str, expected_success, expected_error_part",
    [
        (
            "good_chunk",
            f"{GOOD_HEADER}\n{GOOD_ROW}",
            True,
            None,
        ),
        (
            "bad_header",
            f"{BAD_ROW_FEW_COLUMNS}\n{GOOD_ROW}",
            False,
            "Ошибка в заголовке",
        ),
        (
            "bad_data_row",
            f"{GOOD_HEADER}\n{BAD_ROW_FEW_COLUMNS}",
            False,
            "Ошибка в первой строке данных",
        ),
        (
            "too_few_lines",
            GOOD_HEADER,
            False,
            "не содержит достаточного количества строк",
        ),
        ("empty_string", "", False, "не содержит достаточного количества строк"),
    ], ids=["good_chunk", "bad_header", "bad_data_row", "too_few_lines", "empty_string"]
)
def test_validate_emex_chunk(test_name, chunk_str, expected_success, expected_error_part):
    """Tests the validate_emex_chunk function with various inputs."""
    chunk_bytes = chunk_str.encode("cp1251")
    result = validate_emex_chunk(chunk_bytes)

    assert result["success"] is expected_success
    if expected_error_part:
        assert expected_error_part in result["error"]


def test_validate_emex_chunk_bad_encoding():
    """Tests that the function fails gracefully with non-cp1251 bytes."""
    # A two-line UTF-8 string that is invalid in CP1251.
    chunk_bytes = "Это тестовая строка\nИ еще одна строка".encode("utf-8")
    result = validate_emex_chunk(chunk_bytes)

    assert result["success"] is False
    assert "Ошибка декодирования" in result["error"]

