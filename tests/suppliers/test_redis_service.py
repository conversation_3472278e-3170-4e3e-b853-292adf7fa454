#!/usr/bin/env python3
"""
Test script for Redis service functions.
This script tests the job-specific Redis functions for export progress tracking.
"""

import json
import os
import sys
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))


# Mock Redis for testing without a Redis server
class MockRedis:
    def __init__(self):
        self.data = {}

    def set(self, key, value):
        self.data[key] = value
        return True

    def get(self, key):
        return self.data.get(key)

    def expire(self, key, seconds):
        # Just pretend we set an expiration
        return True

    def ping(self):
        return True


# Create a mock Redis client
mock_redis = MockRedis()


# Mock the Redis service functions
def get_default_progress():
    """Return the default progress state."""
    return {
        "status": "idle",
        "total_suppliers": 0,
        "processed_suppliers": 0,
        "current_supplier": "",
        "completed_suppliers": [],
        "empty_suppliers": [],
        "error_suppliers": [],
        "start_time": None,
        "file_path": None,
        "message": "",
    }


def get_export_progress_by_job(job_id):
    """Get the export progress for a specific job."""
    try:
        progress_json = mock_redis.get(f'export_progress:{job_id}')
        if progress_json:
            return json.loads(progress_json)
        else:
            return None
    except Exception as e:
        print(f"Error getting export progress for job {job_id}: {str(e)}")
        return None


def set_export_progress_by_job(job_id, progress):
    """Set the export progress for a specific job."""
    try:
        mock_redis.set(f'export_progress:{job_id}', json.dumps(progress))
        mock_redis.expire(f'export_progress:{job_id}', 86400)  # 24 hours
    except Exception as e:
        print(f"Error setting export progress for job {job_id}: {str(e)}")


def update_export_progress_by_job(job_id, updates):
    """Update specific fields in the export progress for a job."""
    try:
        progress = get_export_progress_by_job(job_id)
        if progress is None:
            print(f"Cannot update non-existent job: {job_id}")
            return None

        progress.update(updates)
        set_export_progress_by_job(job_id, progress)
        return progress
    except Exception as e:
        print(f"Error updating export progress for job {job_id}: {str(e)}")
        return get_export_progress_by_job(job_id)


def test_redis_service_functions():
    """Test the Redis service functions for job-specific operations."""
    print("Testing Redis service functions...")

    # Generate a unique job ID for testing
    job_id = str(uuid.uuid4())
    print(f"Using test job ID: {job_id}")

    # 1. Test setting initial progress
    initial_progress = get_default_progress()
    initial_progress.update({
        "status": "processing",
        "message": "Starting test export...",
        "start_time": datetime.now().isoformat()
    })

    print("\n1. Setting initial progress...")
    set_export_progress_by_job(job_id, initial_progress)

    # 2. Test retrieving progress
    print("\n2. Retrieving progress...")
    retrieved_progress = get_export_progress_by_job(job_id)
    if retrieved_progress:
        print(f"Retrieved progress: {json.dumps(retrieved_progress, indent=2)}")
        print("✅ Progress retrieval successful")
    else:
        print("❌ Failed to retrieve progress")
        return

    # 3. Test updating progress
    print("\n3. Updating progress...")
    updates = {
        "total_suppliers": 10,
        "processed_suppliers": 2,
        "current_supplier": "Test Supplier (ID: 123)",
        "message": "Processing supplier 2 of 10"
    }

    updated_progress = update_export_progress_by_job(job_id, updates)
    if updated_progress:
        print(f"Updated progress: {json.dumps(updated_progress, indent=2)}")
        print("✅ Progress update successful")
    else:
        print("❌ Failed to update progress")
        return

    # 4. Test updating with completed suppliers
    print("\n4. Adding completed suppliers...")
    updates = {
        "processed_suppliers": 3,
        "completed_suppliers": ["Supplier 1 (ID: 101)", "Supplier 2 (ID: 102)"],
        "message": "Processing supplier 3 of 10"
    }

    updated_progress = update_export_progress_by_job(job_id, updates)
    if updated_progress:
        print(f"Updated progress with completed suppliers: {json.dumps(updated_progress, indent=2)}")
        print("✅ Progress update with completed suppliers successful")
    else:
        print("❌ Failed to update progress with completed suppliers")
        return

    # 5. Test updating with error suppliers
    print("\n5. Adding error suppliers...")
    updates = {
        "processed_suppliers": 5,
        "error_suppliers": ["Supplier 3 (ID: 103): Connection error"],
        "message": "Processing supplier 5 of 10"
    }

    updated_progress = update_export_progress_by_job(job_id, updates)
    if updated_progress:
        print(f"Updated progress with error suppliers: {json.dumps(updated_progress, indent=2)}")
        print("✅ Progress update with error suppliers successful")
    else:
        print("❌ Failed to update progress with error suppliers")
        return

    # 6. Test completing the export
    print("\n6. Completing the export...")
    updates = {
        "status": "completed",
        "processed_suppliers": 10,
        "file_path": "/tmp/test_export.zip",
        "message": "Export completed successfully"
    }

    updated_progress = update_export_progress_by_job(job_id, updates)
    if updated_progress:
        print(f"Final progress: {json.dumps(updated_progress, indent=2)}")
        print("✅ Export completion update successful")
    else:
        print("❌ Failed to update export completion")
        return

    # 7. Test non-existent job
    print("\n7. Testing non-existent job...")
    non_existent_job_id = str(uuid.uuid4())
    non_existent_progress = get_export_progress_by_job(non_existent_job_id)
    if non_existent_progress is None:
        print("✅ Correctly returned None for non-existent job")
    else:
        print("❌ Unexpectedly returned data for non-existent job")

    print("\nAll tests completed successfully!")


if __name__ == "__main__":
    print("Running with mock Redis for testing...")
    test_redis_service_functions()
