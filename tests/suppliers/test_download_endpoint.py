#!/usr/bin/env python3
"""
Tests for the export download endpoint using pytest.
"""

import json
import os
import tempfile
import uuid
from unittest.mock import patch

import pytest
from flask_jwt_extended import create_access_token

from app import app
from services.redis_service import get_default_progress


# Mock Redis for testing without a Redis server
class MockRedis:
    def __init__(self):
        self.data = {}

    def set(self, key, value, ex=None):
        self.data[key] = str(value) # Store as string, similar to real redis
        return True

    def get(self, key):
        return self.data.get(key)

    def expire(self, key, _):
        return True

    def ping(self):
        return True

@pytest.fixture
def client():
    """Create a test client for the Flask app with authentication."""
    app.config.update({
        "TESTING": True,
        "JWT_COOKIE_CSRF_PROTECT": False
    })
    with app.test_client() as client:
        with app.app_context():
            access_token = create_access_token(identity="testuser")
            client.set_cookie("access_token_cookie", access_token)
            yield client

@pytest.fixture(autouse=True)
def mock_redis_client():
    """Auto-mock the redis client for all tests in this module."""
    mock_client = MockRedis()
    with patch("services.redis_service.get_redis_client", return_value=mock_client):
        yield mock_client

@pytest.fixture
def completed_job(mock_redis_client):
    """Fixture to set up a completed job with a file path in Redis."""
    job_id = str(uuid.uuid4())
    # Create a temporary file
    fd, file_path = tempfile.mkstemp(suffix='.zip')
    with os.fdopen(fd, 'wb') as tmp:
        tmp.write(b'test-content')

    progress = get_default_progress()
    progress.update({
        "status": "completed",
        "file_path": file_path,
        "message": "Export completed successfully"
    })
    mock_redis_client.set(f'export_progress:{job_id}', json.dumps(progress))

    yield job_id, file_path

    # Cleanup the temporary file
    if os.path.exists(file_path):
        os.remove(file_path)

@pytest.fixture
def processing_job(mock_redis_client):
    """Fixture to set up a job that is still processing."""
    job_id = str(uuid.uuid4())
    progress = get_default_progress()
    progress.update({
        "status": "processing",
        "message": "Still working..."
    })
    mock_redis_client.set(f'export_progress:{job_id}', json.dumps(progress))
    yield job_id

@patch("suppliers.threading.Thread")
def test_download_completed_job(mock_thread, client, completed_job):
    """Test the download endpoint with a completed job."""
    job_id, file_path = completed_job
    response = client.get(f"/download-export/{job_id}")

    assert response.status_code == 200
    assert response.data == b'test-content'
    assert "application/zip" in response.mimetype

def test_download_processing_job(client, processing_job):
    """Test the download endpoint with a job that is still processing."""
    job_id = processing_job
    response = client.get(f"/download-export/{job_id}")

    assert response.status_code == 400
    json_data = response.get_json()
    assert "error" in json_data
    assert json_data["error"] == "Экспорт еще не завершен"

def test_download_nonexistent_job(client):
    """Test the download endpoint with a non-existent job ID."""
    nonexistent_job_id = str(uuid.uuid4())
    response = client.get(f"/download-export/{nonexistent_job_id}")

    assert response.status_code == 404
    json_data = response.get_json()
    assert "error" in json_data
    assert json_data["error"] == "Задача не найдена"