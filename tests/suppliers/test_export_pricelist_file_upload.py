#!/usr/bin/env python3
"""
Unit tests for the export_pricelist function, focusing on file uploads.
"""

import io
from unittest.mock import patch, MagicMock

import pandas as pd
import pytest
from flask_jwt_extended import create_access_token
from werkzeug.datastructures import FileStorage

from app import app


@pytest.fixture
def client():
    """Create a test client for the Flask app."""
    app.config.update({
        "TESTING": True,
        "JWT_COOKIE_CSRF_PROTECT": False
    })
    with app.test_client() as client:
        with app.app_context():
            access_token = create_access_token(identity="testuser")
            client.set_cookie("access_token_cookie", access_token)
            yield client


def create_excel_in_memory(data):
    """Creates an in-memory Excel file from a list of lists."""
    df = pd.DataFrame(data[1:], columns=data[0])
    output = io.BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return output


  # Bypass JWT authentication
@patch("suppliers.Client")  # Mock the ClickHouse client
def test_successful_export(mock_clickhouse_client, client):
    """Test a successful export with a valid Excel file."""
    mock_instance = MagicMock()
    mock_clickhouse_client.return_value = mock_instance

    # Mock the return value of the database query
    mock_db_result = [
        ("SUZUKI", "0110410388", "", 10, 100.0, "RUB", "Supplier A", "2025-08-15"),
    ]
    mock_instance.execute.return_value = mock_db_result

    # Mock the brand normalization
    with patch("suppliers.normilize_brand", side_effect=lambda brand: brand.upper()):
        # Create a mock Excel file in memory
        excel_data = [
            ["Бренд", "Артикул"],
            ["Suzuki", "0110410388"],
        ]
        excel_file = create_excel_in_memory(excel_data)

        # Create a FileStorage object to simulate the upload
        mock_file = FileStorage(
            stream=excel_file,
            filename="test.xlsx",
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

        response = client.post(
            "/get_sup_pricelist",
            data={
                "all_brands": "false",
                "start_date": "2025-08-01",
                "end_date": "2025-09-01",
                "pricelist_file": mock_file,
            },
            content_type="multipart/form-data",
        )

    assert response.status_code == 200
    assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in response.mimetype

    # Verify the content of the returned Excel file
    response_data = io.BytesIO(response.data)
    df = pd.read_excel(response_data, dtype={'article': str})
    assert len(df) == 1
    assert df.iloc[0]["brand"] == "SUZUKI"
    assert df.iloc[0]["article"] == "0110410388"



def test_missing_file_error(client):
    """Test error handling when no file is provided."""
    response = client.post(
        "/get_sup_pricelist",
        data={
            "all_brands": "false",
            "start_date": "2025-08-01",
            "end_date": "2025-09-01",
        },
        content_type="multipart/form-data",
    )

    assert response.status_code == 400
    json_data = response.get_json()
    assert "error" in json_data
    assert json_data["error"] == "Файл не был предоставлен"



def test_invalid_column_name_error(client):
    """Test error handling with an Excel file that has incorrect column names."""
    excel_data = [
        ["Название бренда", "Номер детали"],  # Invalid column names
        ["Suzuki", "0110410388"],
    ]
    excel_file = create_excel_in_memory(excel_data)
    mock_file = FileStorage(
        stream=excel_file,
        filename="test_invalid_cols.xlsx",
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )

    response = client.post(
        "/get_sup_pricelist",
        data={
            "all_brands": "false",
            "start_date": "2025-08-01",
            "end_date": "2025-09-01",
            "pricelist_file": mock_file,
        },
        content_type="multipart/form-data",
    )

    assert response.status_code == 400
    json_data = response.get_json()
    assert "error" in json_data
    assert "Не удалось найти колонку бренда" in json_data["error"]



@patch("suppliers.Client")
def test_no_data_found_for_articles(mock_clickhouse_client, client):
    """Test the case where the file is valid but no data is found in the database."""
    mock_instance = MagicMock()
    mock_clickhouse_client.return_value = mock_instance
    mock_instance.execute.return_value = []  # Mock an empty DB result

    with patch("suppliers.normilize_brand", side_effect=lambda brand: brand.upper()):
        excel_data = [
            ["brand", "article"],
            ["NONEXISTENT", "12345"],
        ]
        excel_file = create_excel_in_memory(excel_data)
        mock_file = FileStorage(
            stream=excel_file,
            filename="test_no_data.xlsx",
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

        response = client.post(
            "/get_sup_pricelist",
            data={
                "all_brands": "false",
                "start_date": "2025-08-01",
                "end_date": "2025-09-01",
                "pricelist_file": mock_file,
            },
            content_type="multipart/form-data",
        )

    assert response.status_code == 404
    json_data = response.get_json()
    assert "error" in json_data
    assert json_data["error"] == "Нет данных для экспорта по указанным брендам и артикулам"
