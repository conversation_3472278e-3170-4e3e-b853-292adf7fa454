#!/usr/bin/env python3
"""
Test script for the export status endpoint.
This script tests the /export-status/<job_id> endpoint.
"""

import json
import os
import sys
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Configuration
BASE_URL = "http://localhost:5010"  # Adjust to match your server URL
TEST_JOB_ID = str(uuid.uuid4())  # Generate a random job ID for testing


# Mock Redis for testing without a Redis server
class MockRedis:
    def __init__(self):
        self.data = {}

    def set(self, key, value):
        self.data[key] = value
        return True

    def get(self, key):
        return self.data.get(key)

    def expire(self, key, _):
        # Just pretend we set an expiration
        return True

    def ping(self):
        return True


# Create a mock Redis client
mock_redis = MockRedis()


# Mock Redis service functions
def get_default_progress():
    """Return the default progress state."""
    return {
        "status": "idle",
        "total_suppliers": 0,
        "processed_suppliers": 0,
        "current_supplier": "",
        "completed_suppliers": [],
        "empty_suppliers": [],
        "error_suppliers": [],
        "start_time": None,
        "file_path": None,
        "message": "",
    }


def set_export_progress_by_job(job_id, progress):
    """Set the export progress for a specific job."""
    try:
        mock_redis.set(f'export_progress:{job_id}', json.dumps(progress))
        return True
    except Exception as e:
        print(f"Error setting export progress for job {job_id}: {str(e)}")
        return False


def get_export_progress_by_job(job_id):
    """Get the export progress for a specific job."""
    try:
        progress_json = mock_redis.get(f'export_progress:{job_id}')
        if progress_json:
            return json.loads(progress_json)
        else:
            return None
    except Exception as e:
        print(f"Error getting export progress for job {job_id}: {str(e)}")
        return None


def setup_test_job():
    """
    Set up a test job in Redis for testing the status endpoint.
    This would normally be done by the export process, but we're doing it manually for testing.
    """
    print(f"Setting up test job with ID: {TEST_JOB_ID}")

    # Create initial progress data
    progress = get_default_progress()
    progress.update({
        "status": "processing",
        "total_suppliers": 10,
        "processed_suppliers": 3,
        "current_supplier": "Test Supplier (ID: 123)",
        "completed_suppliers": ["Supplier 1", "Supplier 2", "Supplier 3"],
        "start_time": datetime.now().isoformat(),
        "message": "Processing supplier 4 of 10"
    })

    # Save to mock Redis
    if set_export_progress_by_job(TEST_JOB_ID, progress):
        print("Test job set up successfully in mock Redis")
        return True
    else:
        print("Failed to set up test job in mock Redis")
        return False


def test_status_endpoint():
    """Test the status endpoint with the test job ID."""
    print("\nTesting status endpoint...")

    # Simulate what the endpoint would do
    print(f"Simulating GET request to: {BASE_URL}/export-status/{TEST_JOB_ID}")

    # Get the job progress from our mock Redis
    progress = get_export_progress_by_job(TEST_JOB_ID)

    if progress is not None:
        print(f"Response: {json.dumps(progress, indent=2)}")
        print("✅ Status endpoint would return 200 OK with the above data")
    else:
        print("❌ Failed to retrieve job progress from mock Redis")

    print("\nTo test the actual endpoint:")
    print(f"1. Start the Flask server")
    print(f"2. Visit {BASE_URL}/export-status/{TEST_JOB_ID} in your browser or use Postman")
    print(f"3. You'll need to be authenticated with a valid JWT token")


def test_nonexistent_job():
    """Test the status endpoint with a non-existent job ID."""
    print("\nTesting status endpoint with non-existent job ID...")

    # Generate a random job ID that doesn't exist
    nonexistent_job_id = str(uuid.uuid4())

    # Simulate what the endpoint would do
    print(f"Simulating GET request to: {BASE_URL}/export-status/{nonexistent_job_id}")

    # Get the job progress from our mock Redis
    progress = get_export_progress_by_job(nonexistent_job_id)

    if progress is None:
        print("✅ Status endpoint would correctly return 404 with 'Job not found' message")
    else:
        print("❌ Unexpectedly found data for a non-existent job")

    print("\nTo test the actual endpoint:")
    print(f"1. Start the Flask server")
    print(f"2. Visit {BASE_URL}/export-status/{nonexistent_job_id} in your browser or use Postman")
    print(f"3. You should receive a 404 error with 'Job not found' message")
    print(f"4. You'll need to be authenticated with a valid JWT token")


if __name__ == "__main__":
    print("Testing export status endpoint...")

    # Set up a test job in Redis
    if setup_test_job():
        # Test the status endpoint
        test_status_endpoint()

        # Test with a non-existent job ID
        test_nonexistent_job()
    else:
        print("Failed to set up test job, skipping endpoint tests")

    print("\nTest script completed")
