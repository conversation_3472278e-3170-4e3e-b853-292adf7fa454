"""
Global pytest configuration file.

This file configures pytest behavior for all test runs in the project.
It automatically enables verbose mode (-v) for all pytest runs without
having to specify it on the command line.

To disable verbose mode for a specific run, use:
    pytest --no-verbose

This is useful for CI/CD pipelines or when you want less output.
"""


def pytest_addoption(parser):
    """Add command-line options to pytest."""
    parser.addoption(
        "--no-verbose",
        action="store_true",
        default=False,
        help="Disable verbose output (override default verbose mode)",
    )


def pytest_configure(config):
    """Configure pytest before test collection."""
    # If --no-verbose is not specified, add -v to the command line arguments
    if not config.getoption("--no-verbose"):
        config.option.verbose = True