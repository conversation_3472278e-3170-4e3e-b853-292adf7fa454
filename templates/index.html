<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Отчеты по поставщикам</title>
<!--    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body.light {
            background-color: #f8f9fa;
            color: #212529;
            font-family: Arial, sans-serif;
            padding-top: 50px;
        }
        body.dark {
            background-color: #1e1e1e;
            color: #dcdcdc;
            font-family: Arial, sans-serif;
            padding-top: 50px;
        }
        .container {
            padding: 20px;
            margin: 0 auto;
            max-width: 1200px;
        }
        .card.light {
            background-color: #ffffff;
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .card.dark {
            background-color: #2d2d2d;
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        .card-header.light {
            background-color: #007bff;
            color: white;
            padding: 15px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .card-header.dark {
            background-color: #1e90ff;
            color: white;
            padding: 15px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .card-body {
            padding: 20px;
        }
        .form-group label {
            font-weight: bold;
        }
        .form-control.light {
            background-color: #ffffff;
            color: #212529;
            border: 1px solid #ced4da;
        }
        .form-control.dark {
            background-color: #333;
            color: #dcdcdc;
            border: 1px solid #555;
        }
        .form-control.light:focus {
            background-color: #e9ecef;
            border-color: #80bdff;
            color: #212529;
        }
        .form-control.dark:focus {
            background-color: #444;
            border-color: #1e90ff;
            color: #dcdcdc;
        }
        {#.btn-primary.light {#}
        {#    background-color: #007bff;#}
        {#    border-color: #007bff;#}
        {#}#}
        {#.btn-primary.dark {#}
        {#    background-color: #1e90ff;#}
        {#    border-color: #1e90ff;#}
        {#}#}
        {#.btn-primary.light:hover {#}
        {#    background-color: #0056b3;#}
        {#    border-color: #0056b3;#}
        {#}#}
        {#.btn-primary.dark:hover {#}
        {#    background-color: #007bff;#}
        {#    border-color: #007bff;#}
        {#}#}
        .spinner-border {
            display: none; /* Скрываем лоадер по умолчанию */
        }
        .spinner-border.light {
            color: #007bff;
        }
        .spinner-border.dark {
            color: #1e90ff;
        }
        .loading-spinner {
            display: none; /* Скрываем кастомный лоадер по умолчанию */
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }
        .loading-spinner.show {
            display: block; /* Показываем кастомный лоадер при добавлении класса show */
        }
        .loading-spinner .spinner {
            width: 3rem;
            height: 3rem;
            border: 0.4em solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 0.4em solid #007bff;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        .toggle-container {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 20px;
        }
        .toggle {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            color: #007bff;
        }
        .toggle-switch {
            width: 40px;
            height: 20px;
            border-radius: 10px;
            background-color: #ccc;
            margin-right: 10px;
            position: relative;
            transition: background-color 0.3s;
        }
        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: #fff;
            top: 1px;
            left: 1px;
            transition: transform 0.3s;
        }
        body.dark .toggle-switch {
            background-color: #007bff;
        }
        body.dark .toggle-switch::before {
            transform: translateX(20px);
        }
        .settings-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 48px;
            cursor: pointer;
            transition: color 0.3s;
            color: #0056b3;
        }
        .settings-btn:hover {
            color: #007bff;
        }
        .widget {
            margin-top: 20px;
        }
        .widget .card-header {
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .widget .card-header:hover {
            background-color: #0056b3;
        }
        .widget .card-body {
            position: relative;
        }
        .widget .btn-source {
            width: 100%; /* Делает кнопку шириной на всю доступную ширину */
            max-width: 200px; /* Ограничивает максимальную ширину кнопок */
            margin: 5px 0; /* Отступы сверху и снизу для кнопок */
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background-color: #f8f9fa;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            transition: right 0.3s;
            overflow: auto;
            z-index: 1000;
            padding: 20px;
        }
        .sidebar.dark {
            background-color: #2d2d2d;
        }
        .sidebar.active {
            right: 0;
        }
        .close-sidebar {
            text-align: right;
            cursor: pointer;
            font-size: 24px;
            color: #0056b3;
        }
        .close-sidebar:hover {
            color: #007bff;
        }
        .table-container {
            max-width: 100%;
            overflow-x: auto;
        }
        .table {
            width: 100%;
            margin-top: 20px;
        }
        .table th, .table td {
            text-align: center;
        }
        .table-hover tbody tr:hover {
            background-color: #f1f1f1;
        }
        .table-warning {
            background-color: #fff3cd;
        }
        .table-success {
            background-color: #d4edda;
        }
        .table-danger {
            background-color: #f8d7da;
        }
    </style>
</head>
<body class="light">
<div class="toggle-container">
    <div class="toggle" id="themeToggle">
        <div class="toggle-switch"></div>
        <span>Dark Mode</span>
    </div>
</div>
<div class="settings-btn" onclick="location.href='/settings'">
    &#9881; <!-- Cog icon -->
</div>
<div class="container">
    <div class="card light">
        <div class="card-header light">
            <h2>Отчеты по поставщикам</h2>
        </div>
        <div class="card-body">
            <form id="reportForm" method="post">
                <div class="form-group">
                    <label for="report_type">Select Report:</label>
                    <select class="form-control light" id="report_type" name="report_type">
                        {% for key, value_r in reports.items() %}
                        <option value="{{ key }}">{{ value_r.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="btn btn-primary light" id="generateBtn">Generate Report</button>
                <div class="spinner-border light loading-spinner" id="loadingSpinner">
                    <div class="spinner"></div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="widget">
                <div class="card light">
                    <div class="card-header light text-bg-success">
                        <h2>Проценка кросс-док</h2>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-success btn-source" onclick="location.href='/percentage'">Создать
                            задачу
                        </button>
                        <button class="btn btn-outline-secondary btn-source ms-2" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/cross_dock/tasks?auto=false'">Статусы задач
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="widget">
                <div class="card light">
                    <div class="card-header text-bg-success">
                        <h2>Проценка по парсеру</h2>
                    </div>
                    <div class="card-body">
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-outline-success btn-source" style="flex: 1; min-width: 150px;"
                                    onclick="location.href='/parser'">Создать задачу
                            </button>
                            <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                    onclick="location.href='/task_status_parser?auto=false'">Статусы задач
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="widget">
                <div class="card light">
                    <div class="card-header light">
                        <h2>Автоматическая проценка кросс-док</h2>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary btn-source" onclick="location.href='/percentage_scheduller'">
                            Создать задачу
                        </button>
                        <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/cross_dock/tasks?auto=true'">Статусы задач
                        </button>
                        <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/schedulle_tasks_cross_doc'">Управление задачами
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="widget">
                <div class="card light">
                    <div class="card-header text-bg-success">
                        <h2>Автоматическая проценка</h2>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-success btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/parser_sheduller'">Создать задачу
                        </button>
                        <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/task_status_parser?auto=true'">Статусы задач
                        </button>
                        <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/schedulle_tasks_parser'">Управление задачами
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="widget">
                <div class="card light">
                    <div class="card-header light">
                        <h2>Формирование цен на основе исторических данных</h2>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/parser_history'">Создать задачу
                        </button>
                        <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                onclick="location.href='/task_status_parser_history?auto=false'">Статусы задач
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="widget">
                <div class="card light">
                    <div class="card-header light">
                        <h2>Формирование выгодной цены</h2>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-success btn-source" id="export_sup_price_Btn" onclick="location.href='/best_price'"> Открыть</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    

    <div class="widget">
        <div class="card light">
            <div class="card-header light">
                <h2>Парсер EAN-13, OEM</h2>
            </div>
            <div class="card-body">
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-primary btn-source" style="flex: 1; min-width: 150px;"
                                    onclick="location.href='/ean_oem'">Создать задачу
                    </button>
                    <button class="btn btn-secondary btn-source" style="flex: 1; min-width: 150px;"
                                    onclick="location.href='/task_status_ean_oem'">Статусы задач
                    </button>
                        </div>
                    </div>
                </div>
            </div>
    <div class="widget">
        <div class="card light">
            <div class="card-header light">
                <h2>Мониторинг рынка - определение объёма закупки</h2>
            </div>
            <div class="card-body">
                <button class="btn btn-primary btn-source" id="monitoring_market_Btn" onclick="location.href='/demand_quantity_form'"> Открыть </button>
            </div>
        </div>
    </div>
    <div class="widget">
        <div class="card light">
            <div class="card-header text-bg-success">
                <h2>Мониторинг прайс-листов</h2>
            </div>
            <div class="card-body">
                <button class="btn btn-outline-secondary btn-source disabled" id="monitoringBtn">Открыть отчёт</button>
                <button class="btn btn-outline-success ms-3" id="relevanceBtn" onclick="handleRelevanceClick()">
                    <span class="spinner-grow spinner-grow-sm d-none me-2" aria-hidden="true"></span>
                    <span role="status">Актуальность прайс-листов</span>
                </button>
            </div>
        </div>
    </div>

    <div class="widget">
        <div class="card light">
            <div class="card-header text-bg-success">
                <h2>Формирование потребности</h2>
            </div>
            <div class="card-body">
                <form id="demandForm" method="post">
                    <div class="form-group">
                        <label for="demand_type">Выберите источник (old)</label>
                        <select class="form-control light" id="demand_type" name="report_type">
                            <option value="emex">Emex</option>
                            <option value="dif">DIF</option>
                        </select>
                    </div>
                    <div class="d-flex gap-2 mt-3">
                        <button type="submit" class="btn btn-outline-secondary light" id="generateBtn">Перейти к формированию (old)</button>
                        <a href="/demand_report" class="btn btn-outline-success light" title="Объединенный отчет DIF и EMEX">
                            Формирование потребности
                        </a>
                    </div>
                    <div class="spinner-border light loading-spinner" id="loadingSpinner">
                        <div class="spinner"></div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="widget">
        <div class="card light">
            <div class="card-header light" onclick="location.href='/suppliers'">
                <h2>Список поставщиков (добавление/редактирование/удаление)</h2>
            </div>
            <div class="card-body">
                <button class="btn btn-primary btn-source" id="suppliers_Btn" onclick="location.href='/suppliers'"> Открыть </button>
            </div>
        </div>
    </div>

    <div class="widget">
        <div class="card light">
            <div class="card-header light" onclick="location.href='/load_sup_price'">
                <h2>Загрузить прайс-лист поставщика</h2>
            </div>
            <div class="card-body">
                <button class="btn btn-primary btn-source" id="load_sup_price_Btn" onclick="location.href='/load_sup_price'"> Открыть </button>
            </div>
        </div>
    </div>

    <div class="widget">
        <div class="card light">
            <div class="card-header text-bg-success" onclick="location.href='/export_pricelist'">
                <h2>Выгрузить прайс-лист поставщика</h2>
            </div>
            <div class="card-body">
                <button class="btn btn-outline-success btn-source" id="export_sup_price_Btn" onclick="location.href='/export_pricelist'"> Открыть </button>
            </div>
        </div>
    </div>

    <div class="widget">
        <div class="card light">
            <div class="card-header text-bg-success" onclick="location.href='/item_catalog/upload'">
                <h2>Обновить базу ВГХ</h2>
            </div>
            <div class="card-body">
                <button class="btn btn-outline-success btn-source" onclick="location.href='/item_catalog/upload'"> Открыть </button>
            </div>
        </div>
    </div>
</div>

<div class="sidebar" id="monitoringSidebar">
    <div class="close-sidebar" id="closeSidebar">&times;</div>
    <h2>Актуализация прайсов</h2>
    <div class="loading-spinner dark" id="loadingSpinnerSidebar">
        <div class="spinner"></div>
    </div>
    <div class="table-container" id="tableContainer">
        <!-- Таблица будет вставлена сюда -->
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!--<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>-->
<!--<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>-->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
<script>
    $(document).ready(function () {
        // Переключение темы
        $('#themeToggle').click(function () {
            $('body').toggleClass('dark');
            $('.card').toggleClass('dark').toggleClass('light');
            $('.card-header').toggleClass('dark').toggleClass('light');
            $('.form-control').toggleClass('dark').toggleClass('light');
            $('.btn-primary').toggleClass('dark').toggleClass('light');
            $('.spinner-border').toggleClass('dark').toggleClass('light');
            $('.sidebar').toggleClass('dark');
        });

        // Открытие боковой панели мониторинга
        $('#monitoringBtn').click(function () {
            $('#monitoringSidebar').addClass('active');
            $('#loadingSpinnerSidebar').addClass('show');

            $.get('/get_price_stats', function (response) {
                $('#loadingSpinnerSidebar').removeClass('show');

                // Создание HTML таблицы
                var tableHtml = `
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>ID Поставщика</th>
                                    <th>Название Поставщика</th>
                                    <th>Дата Обновления</th>
                                    <th>Количество Записей</th>
                                    <th>Столбцы с нулевыми значениями</th>
                                    <th>Более 3х цифр в бренде</th>
                                    <th>Пустые строки в бренде</th>
                                    <th>Пустые строки в артикуле</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                response.data.forEach(function (supplier) {
                    var dateUpd = new Date(supplier.dateupd).toISOString().slice(0, 10);
                    var today = new Date().toISOString().slice(0, 10);
                    var thresholdDate = new Date();
                    thresholdDate.setDate(thresholdDate.getDate() - 5)
                    thresholdDate = thresholdDate.toISOString().slice(0, 10);

                    var rowClass = 'table-danger'; // Default class
                    if (dateUpd === today) {
                        rowClass = 'table-success';
                    } else if (dateUpd > thresholdDate) {
                        rowClass = 'table-warning';
                    }

                    tableHtml += `
                            <tr class="${rowClass}">
                                <td>${supplier.supid}</td>
                                <td>${supplier.name}</td>
                                <td>${supplier.dateupd}</td>
                                <td>${supplier.count_price}</td>
                                <td style="color: ${supplier.empty_columns ? 'red' : 'black'};">
                                    ${supplier.empty_columns || ''}
                                </td>
                                 <td style="color: ${supplier.above_3_digits_brand ? 'red' : 'black'};">
                                    ${supplier.above_3_digits_brand || ''}
                                </td>
                                </td>
                                 <td style="color: ${supplier.empty_b_count ? 'red' : 'black'};">
                                    ${supplier.empty_b_count || ''}
                                </td>
                                </td>
                                 <td style="color: ${supplier.empty_a_count ? 'red' : 'black'};">
                                    ${supplier.empty_a_count || ''}
                                </td>
                            </tr>
                        `;
                });

                tableHtml += `
                        </tbody>
                    </table>
                    `;
                $('#tableContainer').html(tableHtml);
            }).fail(function () {
                $('#loadingSpinnerSidebar').removeClass('show');
                alert('Error loading data');
            });
        });

        // Закрытие боковой панели и скрытие лоадера
        $('#closeSidebar').click(function () {
            $('#monitoringSidebar').removeClass('active');
            $('#loadingSpinnerSidebar').removeClass('show');
        });

        // Перенаправление при нажатии на кнопку "Генерировать отчет"
        $('#reportForm').on('submit', function (event) {
            event.preventDefault(); // Предотвращаем отправку формы по умолчанию
            var reportType = $('#report_type').val();
            window.location.href = '/filters/' + encodeURIComponent(reportType); // Редирект на страницу /filters с параметром
        });

        $('#demandForm').on('submit', function (event) {
            event.preventDefault(); // Предотвращаем отправку формы по умолчанию
            var demandType = $('#demand_type').val();
            window.location.href = '/generate_demand?demand_type=' + encodeURIComponent(demandType); // Редирект на страницу /filters с параметром
        });

    });
</script>
<script>
    function handleRelevanceClick() {
        const btn = document.getElementById('relevanceBtn');
        const spinner = btn.querySelector('.spinner-grow');
        const buttonText = btn.querySelector('[role="status"]');

        // Disable button and show spinner
        btn.disabled = true;
        spinner.classList.remove('d-none');

        // Store the current state in sessionStorage
        sessionStorage.setItem('relevanceButtonClicked', 'true');

        // Navigate to the page
        window.location.href = '/price_relevance';
    }

    // Reset button state when page is shown (including when using back button)
    window.addEventListener('pageshow', function(event) {
        // Reset only if coming back to the page (not initial load)
        if (sessionStorage.getItem('relevanceButtonClicked')) {
            const btn = document.getElementById('relevanceBtn');
            if (btn) {
                btn.disabled = false;
                const spinner = btn.querySelector('.spinner-grow');
                if (spinner) {
                    spinner.classList.add('d-none');
                }
            }
            sessionStorage.removeItem('relevanceButtonClicked');
        }
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Reset all spinners on page load
        const btn = document.getElementById('relevanceBtn');
        if (btn) {
            btn.disabled = false;
            const spinner = btn.querySelector('.spinner-grow');
            if (spinner) {
                spinner.classList.add('d-none');
            }
        }
    });
</script>
</body>
</html>
