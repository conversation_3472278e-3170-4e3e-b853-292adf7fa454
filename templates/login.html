<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap">
    <style>
        body {
            background-color: #121212;
            color: #ffffff;
            font-family: 'Roboto', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .login-container {
            background-color: #1e1e1e;
            padding: 3em;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            width: 400px;
            text-align: center;
        }
        h2 {
            margin-bottom: 1.5em;
            color: #1936d8;
            font-size: 2em;
        }
        label {
            display: block;
            margin-bottom: 1em;
            color: #bbbbbb;
            text-align: left;
            font-size: 1.25em;
        }
        input[type="text"],
        input[type="password"] {
            width: calc(100% - 2em);
            padding: 0.75em;
            margin-bottom: 1.5em;
            border: 1px solid #333;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: #ffffff;
            font-size: 1.25em;
        }
        input[type="submit"] {
            background-color: #2e55d3;
            color: #121212;
            border: none;
            border-radius: 4px;
            padding: 1em 2em;
            cursor: pointer;
            font-size: 1.25em;
            transition: background-color 0.3s;
        }
        input[type="submit"]:hover {
            background-color: #3700b3;
        }
        .error-message {
            color: #557be2;
            margin-top: 1.5em;
            font-size: 1.25em;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>Login</h2>
        <form id="loginForm">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username"><br><br>
            <label for="password">Password:</label>
            <input type="password" id="password" name="password"><br><br>
            <input type="submit" value="Login">
        </form>
        <p id="error-message" class="error-message" style="display:none;"></p>
    </div>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            fetch('/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: username,
                    password: password
                }),
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                if (!response.ok) {
                    return response.json().then(error => {
                        // Отображаем сообщение об ошибке пользователю
                        alert(error.error);
                    });
                }
                // Если успешный вход, сохраняем токен
                
                window.location.href = '/'; // Пример перенаправления
            }).catch(error => {
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>