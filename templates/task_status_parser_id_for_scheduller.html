<!DOCTYPE html>
<html>
<head>
    <title>Tasks Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .task-container {
            width: 80%;
            margin: 0 auto;
        }
        .task-header {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .task-detail {
            margin-bottom: 10px;
        }
        .task-status {
            color: #333;
        }
        .task-status.success {
            color: green;
        }
        .task-status.failed {
            color: red;
        }
        .task-status.pending {
            color: orange;
        }
        .task-table {
            width: 100%;
            border-collapse: collapse;
        }
        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .task-table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .download-button {
            padding: 5px 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            cursor: pointer;
        }
        .download-button:hover {
            background-color: #0056b3;
        }
        .detail-button {
            padding: 5px 10px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .detail-button:hover {
            background-color: #218838;
        }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
</style>
</head>
{% include '_back_button.html' %}
<body>
    <div class="task-container">
        <div class="task-header">История проценок по парсеру для задачи {{ task_id }}</div>
        <table class="task-table">
            <thead>
                <tr>
                    <th>Task UID</th>
                    <th>Имя входного файла</th>
                    <th>Платформы для проценки</th>
                    <th>Статус</th>
                    <th>Прогресс</th>
                    <th>Результат</th>
                    <th>Дата запуска</th>
                    <th>Дата завершения</th>
                    <th>Время выполнения</th>
                    <th>Строки без результата</th>
                </tr>
            </thead>
            <tbody>
                {% for task_id, task in tasks_info.items() %}
                    <tr>
                        <td>{{ task_id }}</td>
                        <td class="task-detail">{{ task['filename'] }}</td>
                        <td class="task-detail">{{ task['platforms'] | join(', ') }}</td>
                        <td class="task-status {{ task['state'] | lower }}">{{ task['status'] }}</td>
                        <td>{{ task['progress'] }}</td>
                        <td>
                            {% if task['url'] %}
                                {% set result_url = task['url'].strip("'") %}
                                {% if result_url.startswith('http://') or result_url.startswith('https://') %}
                                    <a href="{{ result_url }}" class="download-button" target="_blank">Скачать</a>
                                {% else %}
                                    {{ result_url }}
                                {% endif %}
                            {% else %}
                                Нет результата
                            {% endif %}
                        </td>
                        <td>{{ task['received']}}</td>
                        <td>{{ task['succeeded']}}</td>
                        <td>{{ task['runtime'] }}</td>
                        <td>
                            {% if task['rows_without_result'] %}
                                <button class="detail-button" onclick='showDetail({{ task["rows_without_result"] | tojson | safe }})'>
                                    {{ task['rows_without_result']|length }}
                                </button>
                            {% else %}
                                0
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
<script>
    function showDetail(rowsWithoutResult) {
        let detailsWindow = window.open('', '_blank', 'width=600,height=400');
        detailsWindow.document.write('<html><head><title>Rows Without Result</title></head><body>');
        detailsWindow.document.write('<table border="1" cellpadding="5" cellspacing="0">');
        detailsWindow.document.write('<tr><th>№ Строки</th><th>Бренд</th><th>Артикул</th><th>Сайт</th><th>Замены на сайте</th></tr>');
        
        for (let [row, sites] of Object.entries(rowsWithoutResult)) {
            let [num, brand, article] = row.split(', ');
            for (let [site, replacements] of Object.entries(sites)) {
                detailsWindow.document.write('<tr>');
                detailsWindow.document.write(`<td>${num}</td>`);
                detailsWindow.document.write(`<td>${brand}</td>`);
                detailsWindow.document.write(`<td>${article}</td>`);
                detailsWindow.document.write(`<td>${site}</td>`);
                detailsWindow.document.write(`<td>${replacements.join(', ')}</td>`);
                detailsWindow.document.write('</tr>');
            }
        }

        detailsWindow.document.write('</table>');
        detailsWindow.document.write('</body></html>');
        detailsWindow.document.close();
    }
</script>
</html>