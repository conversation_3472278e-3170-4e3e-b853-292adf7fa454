<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <title>Загрузка данных</title>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <style>
      /* Hide elements until Vue is initialized */
      [v-cloak] {
        display: none;
      }

      .loading-indicator {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 20px;
        border-radius: 5px;
        font-size: 18px;
        z-index: 1000;
      }
      /* Спиннер загрузки */
      .spinner-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 500;
      }
      .spinner {
        border: 8px solid #f3f3f3;
        border-top: 8px solid #1565c0;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
        margin-right: 15px;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .spinner-text {
        font-size: 18px;
        color: #1565c0;
      }
      /* Progress container styles */
      .progress-container {
        background: white;
        border-radius: 8px;
        padding: 20px;
        width: 80%;
        max-width: 800px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      .progress-details {
        margin: 15px 0;
      }
      .completed-suppliers,
      .error-suppliers {
        margin-top: 15px;
      }
      .list-group {
        max-height: 200px;
        overflow-y: auto;
      }
    </style>
  </head>
  <body class="container mt-5">
    {% include '_back_button.html' %}
    <div id="app" class="mt-3" v-cloak>
      <!-- Индикатор загрузки при отправке данных -->

      <div v-if="loading && !showProgress" class="spinner-overlay">
        <div class="spinner"></div>
        <div class="spinner-text">
          Отправка данных, пожалуйста, подождите...
        </div>
      </div>

      <div v-if="showProgress" class="spinner-overlay">
        <!-- Detailed progress information -->
        <div class="progress-container position-relative">
          <!-- Cancel button - only shown during processing -->
          <button
            v-if="!showDownloadButton"
            @click="cancelExport"
            type="button"
            class="btn-close position-absolute"
            style="top: 10px; right: 10px"
            aria-label="Close"
            title="Отменить выгрузку"
          ></button>
          <h4>Экспорт данных</h4>
          <div
            class="progress mb-3"
            role="progressbar"
            :aria-valuenow="progressPercentage"
            aria-valuemin="0"
            aria-valuemax="100"
          >
            <div
              :class="{
                'progress-bar progress-bar-striped progress-bar-animated': !showDownloadButton,
                'progress-bar text-bg-success': showDownloadButton
              }"
              :style="{width: progressPercentage + '%'}"
            >
              [[ progressPercentage ]]%
            </div>
          </div>

          <div class="progress-details">
            <p>
              <strong>Обработано:</strong> [[ processedSuppliers ]] из [[
              totalSuppliers ]]
            </p>
            <p><strong>Текущий поставщик:</strong> [[ currentSupplier ]]</p>
            <p><strong>Статус:</strong> [[ progressMessage ]]</p>
            <p v-if="elapsedTime">
              <strong>Прошло времени:</strong> [[ elapsedTime ]]
            </p>
          </div>

          <div class="mt-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h5>
                Обработанные поставщики ([[ completedSuppliers.length ]]):
              </h5>
              <span
                v-if="emptySuppliers && emptySuppliers.length > 0"
                class="badge bg-warning text-dark"
                >Пустых поставщиков: [[ emptySuppliers.length ]]</span
              >
            </div>
            <ul
              v-if="completedSuppliers.length > 0"
              class="list-group"
              style="max-height: 200px; overflow-y: auto"
            >
              <li
                v-for="supplier in completedSuppliersReversed"
                class="list-group-item list-group-item-success"
              >
                [[ supplier ]]
              </li>
            </ul>
          </div>

          <div v-if="errorSuppliers.length > 0" class="error-suppliers mt-3">
            <h5>Ошибки ([[ errorSuppliers.length ]]):</h5>
            <ul class="list-group" style="max-height: 200px; overflow-y: auto">
              <li
                v-for="error in errorSuppliers"
                class="list-group-item list-group-item-danger"
              >
                [[ error ]]
              </li>
            </ul>
          </div>

          <!-- Download button appears when export is complete -->
          <div v-if="showDownloadButton" class="mt-4 text-center">
            <div class="alert alert-success" role="alert">
              <h4 class="alert-heading">Экспорт успешно завершен!</h4>
              <p>
                Все данные были успешно экспортированы. Теперь вы можете скачать
                файл.
              </p>
              <p v-if="fileSize" class="mb-0">
                <strong>Размер файла:</strong> [[ fileSize ]]
              </p>
            </div>
            <div class="d-flex justify-content-center gap-3">
              <button
                type="button"
                class="btn btn-success btn-lg shadow-sm"
                @click="downloadExport"
              >
                <i class="bi bi-download"></i> Скачать файл
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary btn-lg"
                @click="resetExport"
              >
                Закрыть
              </button>
            </div>
          </div>
        </div>
      </div>

      <h2>Выбор бренда и диапазона дат</h2>
      <form @submit.prevent="submitForm">
        <div class="mb-3">
          <div class="input-group">
            <input
              type="file"
              @change="handleFileUpload"
              class="form-control"
              :required="!allBrands"
              :disabled="allBrands"
              accept=".xlsx"
              ref="fileInput"
            />
            <button
              type="button"
              class="btn btn-outline-secondary"
              @click="toggleAllBrands"
              v-text="buttonText"
            ></button>
          </div>
          <div class="form-text" v-if="!allBrands">
              Ожидаемый формат: <span class="text-success">.xlsx</span> файл с колонками <code>бренд</code>, <code>артикул</code>
          </div>
          <div class="form-text" v-if="allBrands">
            Будут экспортированы данные по всем брендам
          </div>
          <div v-if="fileError" class="text-danger mt-2">[[ fileError ]]</div>
        </div>

        <div class="mb-3">
          <label>Дата начала:</label>
          <input
            type="date"
            v-model="startDate"
            class="form-control"
            required
          />
        </div>

        <div class="mb-3">
          <label>Дата окончания:</label>
          <input type="date" v-model="endDate" class="form-control" required />
        </div>

        <button type="submit" class="btn btn-primary mt-3">
          Получить данные
        </button>
      </form>

      <!-- Индикатор загрузки -->
      <div v-if="loading" class="loading-indicator">
        Загружаем данные, пожалуйста, подождите...
      </div>
    </div>

    <script>
      new Vue({
        el: "#app",
        delimiters: ["[[", "]]"],
        data: {
          file: null,
          fileError: null,
          startDate: "",
          endDate: "",
          loading: false,
          allBrands: false,
          showProgress: false,
          progressPercentage: 0,
          currentSupplier: "-",
          processedSuppliers: 0,
          totalSuppliers: 0,
          progressMessage: "Загрузка...",
          elapsedTime: null,
          startTime: null,
          completedSuppliers: [],
          emptySuppliers: [],
          errorSuppliers: [],
          jobId: null,
          showDownloadButton: false,
          pollingInterval: null,
          pollingRetryCount: 0,
          maxPollingRetries: 5,
          fileSize: null,
        },
        computed: {
          buttonText() {
            return this.allBrands ? "Экспорт по файлу" : "Все бренды";
          },
          completedSuppliersReversed() {
            // Return a reversed copy of the completedSuppliers array
            // so new suppliers appear at the top
            return [...this.completedSuppliers].reverse();
          },
        },
        mounted() {
          // Set default date range to last 30 days
          const today = new Date();
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(today.getDate() - 30);

          this.endDate = this.formatDate(today);
          this.startDate = this.formatDate(thirtyDaysAgo);

          // Check for running jobs on page load
          this.checkForRunningJobs();
        },
        beforeDestroy() {
          // Clean up any polling intervals when component is destroyed
          this.stopPolling();
        },
        methods: {
          formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            return `${year}-${month}-${day}`;
          },

          handleFileUpload(event) {
            this.file = event.target.files[0];
            this.fileError = null;
          },

          toggleAllBrands() {
            this.allBrands = !this.allBrands;
            this.fileError = null;
            if (this.allBrands) {
              this.file = null;
              if (this.$refs.fileInput) {
                this.$refs.fileInput.value = "";
              }
            }
          },

          cancelExport() {
            if (!this.jobId) {
              console.error("No job ID available for cancellation");
              return;
            }

            if (confirm("Вы уверены, что хотите отменить выгрузку?")) {
              console.log(`Cancelling job ${this.jobId}`);

              // Send cancellation request to the server with the job ID
              fetch(`/cancel-export/${this.jobId}`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
              })
                .then((response) => {
                  if (!response.ok) {
                    throw new Error("Ошибка при отмене экспорта");
                  }
                  return response.json();
                })
                .then((data) => {
                  console.log("Cancellation response:", data);
                  this.progressMessage = "Экспорт был отменен";
                })
                .catch((error) => {
                  console.error("Error cancelling export:", error);
                  alert(`Ошибка при отмене: ${error.message}`);
                })
                .finally(() => {
                  // Reset UI state
                  this.loading = false;
                  this.showProgress = false;
                });
            }
          },

          submitForm() {
            this.loading = true;
            this.fileError = null;

            // Reset progress tracking variables
            this.showProgress = false;
            this.progressPercentage = 0;
            this.currentSupplier = "-";
            this.processedSuppliers = 0;
            this.totalSuppliers = 0;
            this.progressMessage = "Загрузка...";
            this.elapsedTime = null;
            this.startTime = null;
            this.completedSuppliers = [];
            this.emptySuppliers = [];
            this.errorSuppliers = [];
            this.jobId = null;
            this.fileSize = null;

            const formData = new FormData();
            formData.append("start_date", this.startDate);
            formData.append("end_date", this.endDate);
            formData.append("all_brands", this.allBrands);

            if (this.allBrands) {
              // For all brands export, use polling approach
              this.submitAllBrandsExport(formData);
            } else {
              // For single brand export, use direct download
              this.submitSingleBrandExport(formData);
            }
          },

          async submitAllBrandsExport(formData) {
            try {
              console.log("Starting all-brands export with polling approach");

              // Submit form data
              const response = await fetch("/get_sup_pricelist", {
                method: "POST",
                body: formData,
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Ошибка сети");
              }

              const data = await response.json();
              console.log("All-brands export started:", data);

              if (data.status === "started" && data.job_id) {
                // Store the job ID
                this.jobId = data.job_id;
                console.log("Job ID received:", this.jobId);

                // Save job ID to localStorage
                localStorage.setItem("exportJobId", this.jobId);
                console.log("Job ID saved to localStorage");

                // Start polling for status updates
                this.startPolling();

                // Show progress UI
                this.showProgress = true;
              } else {
                throw new Error("Неверный ответ от сервера");
              }
            } catch (error) {
              console.error("Error starting all-brands export:", error);

              // Provide more specific error messages based on the error type
              let errorMessage = error.message;

              if (
                error.message.includes("Failed to fetch") ||
                error.message === "Ошибка сети"
              ) {
                errorMessage =
                  "Не удалось подключиться к серверу. Проверьте сетевое соединение и попробуйте снова.";
              } else if (error.message.includes("Redis")) {
                errorMessage =
                  "Ошибка подключения к Redis. Пожалуйста, сообщите администратору.";
              }

              alert(`Ошибка при запуске экспорта: ${errorMessage}`);
              this.loading = false;
              this.showProgress = false;
            }
          },

          submitSingleBrandExport(formData) {
            if (!this.file) {
                this.fileError = "Пожалуйста, выберите файл для экспорта.";
                this.loading = false;
                return;
            }
            formData.append('pricelist_file', this.file);

            console.log("Starting single brand export with direct download");

            fetch("/get_sup_pricelist", {
              method: "POST",
              body: formData,
            })
              .then((response) => {
                if (!response.ok) {
                  return response.json().then(err => {
                    throw new Error(err.error || 'Произошла неизвестная ошибка при обработке файла.');
                  });
                }
                const disposition = response.headers.get("Content-Disposition");
                const filename = disposition
                  ? disposition.split("filename=")[1].replace(/"/g, "")
                  : "pricelist.xlsx";
                return response.blob().then(blob => ({ blob, filename }));
              })
              .then(({ blob, filename }) => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.style.display = "none";
                a.href = url;
                a.download = filename;

                document.body.appendChild(a);
                a.click();
                a.remove();

                window.URL.revokeObjectURL(url);
                this.loading = false;
              })
              .catch((error) => {
                console.error("Ошибка:", error);
                this.fileError = error.message;
                this.loading = false;
              });
          },

          startPolling() {
            console.log(`Starting polling for job ID: ${this.jobId}`);

            // Clear any existing polling interval
            this.stopPolling();

            // Initialize retry counter
            this.pollingRetryCount = 0;
            this.maxPollingRetries = 5;

            // Set up polling interval (every 2 seconds)
            this.pollingInterval = setInterval(async () => {
              try {
                if (!this.jobId) {
                  console.error("No job ID available for polling");
                  this.stopPolling();
                  return;
                }

                // Fetch the current status from the server
                const response = await fetch(`/export-status/${this.jobId}`);

                // Reset retry counter on successful response
                if (response.ok) {
                  this.pollingRetryCount = 0;
                } else {
                  // Increment retry counter on error
                  this.pollingRetryCount++;

                  // If we've reached max retries, throw an error
                  if (this.pollingRetryCount >= this.maxPollingRetries) {
                    const errorData = await response.json();
                    throw new Error(
                      errorData.error ||
                        "Ошибка получения статуса после нескольких попыток"
                    );
                  }

                  // Otherwise, just log the error and continue polling
                  console.warn(
                    `Error fetching status (attempt ${this.pollingRetryCount}/${this.maxPollingRetries}), will retry...`
                  );
                  return;
                }

                const data = await response.json();

                // Update progress variables
                this.currentSupplier = data.current_supplier || "-";
                this.processedSuppliers = data.processed_suppliers || 0;
                this.totalSuppliers = data.total_suppliers || 0;
                this.progressMessage = data.message || "Загрузка...";

                // Store start time when we first receive processing status
                if (
                  data.status === "processing" &&
                  !this.startTime &&
                  data.start_time
                ) {
                  this.startTime = new Date(data.start_time);
                }

                // Calculate elapsed time
                if (this.startTime) {
                  const now = new Date();
                  const elapsedMs = now - this.startTime;
                  const seconds = Math.floor((elapsedMs / 1000) % 60);
                  const minutes = Math.floor((elapsedMs / (1000 * 60)) % 60);
                  const hours = Math.floor(elapsedMs / (1000 * 60 * 60));

                  // Format according to requirements:
                  // "X мин Y сек" when < 1 hour, "X ч Y мин Z сек" when ≥ 1 hour
                  if (hours > 0) {
                    this.elapsedTime = `${hours} ч ${minutes} мин ${seconds} сек`;
                  } else {
                    this.elapsedTime = `${minutes} мин ${seconds} сек`;
                  }
                }

                // Update supplier lists
                if (data.completed_suppliers) {
                  this.completedSuppliers = data.completed_suppliers;
                }

                if (data.empty_suppliers) {
                  this.emptySuppliers = data.empty_suppliers;
                }

                if (data.error_suppliers) {
                  this.errorSuppliers = data.error_suppliers;
                }

                // Calculate progress percentage
                if (this.totalSuppliers > 0) {
                  this.progressPercentage = Math.round(
                    (this.processedSuppliers / this.totalSuppliers) * 100
                  );
                }

                // Check if the export is complete, has an error, or was cancelled
                if (data.status === "completed") {
                  console.log("Export completed successfully");
                  // Set file size if available
                  if (data.file_size) {
                    this.fileSize = data.file_size;
                    console.log(`File size: ${this.fileSize}`);
                  }
                  this.showDownloadButton = true;
                  this.stopPolling();
                  this.loading = false;

                  // Clear job ID from localStorage
                  localStorage.removeItem("exportJobId");
                  console.log("Job ID removed from localStorage");
                } else if (data.status === "error") {
                  console.error("Export failed:", data.message);
                  alert(`Ошибка при экспорте: ${data.message}`);
                  this.stopPolling();
                  this.loading = false;
                  this.showProgress = false;

                  // Clear job ID from localStorage
                  localStorage.removeItem("exportJobId");
                  console.log("Job ID removed from localStorage due to error");
                } else if (data.status === "cancelled") {
                  console.log("Export was cancelled");
                  this.stopPolling();
                  this.loading = false;
                  this.showProgress = false;

                  // Clear job ID from localStorage
                  localStorage.removeItem("exportJobId");
                  console.log(
                    "Job ID removed from localStorage due to cancellation"
                  );
                }
              } catch (error) {
                console.error("Error during polling:", error);

                // Provide more specific error messages based on the error type
                let errorMessage = error.message;

                if (
                  error.message.includes("Failed to fetch") ||
                  error.message.includes("Ошибка получения статуса")
                ) {
                  errorMessage =
                    "Не удалось получить статус экспорта. Проверьте сетевое соединение.";
                } else if (error.message.includes("Job not found")) {
                  errorMessage =
                    "Задание не найдено. Возможно, оно было удалено или истек срок его хранения.";
                } else if (error.message.includes("Redis")) {
                  errorMessage =
                    "Ошибка подключения к Redis. Пожалуйста, сообщите администратору.";
                }

                alert(`Ошибка при получении статуса: ${errorMessage}`);
                this.stopPolling();
                this.loading = false;
                this.showProgress = false;
              }
            }, 2000); // Poll every 2 seconds
          },

          stopPolling() {
            if (this.pollingInterval) {
              console.log("Stopping polling");
              clearInterval(this.pollingInterval);
              this.pollingInterval = null;
            }
          },

          downloadExport() {
            if (!this.jobId) {
              console.error("No job ID available for download");
              alert("Ошибка: Нет доступного идентификатора задания");
              return;
            }

            console.log(`Downloading export for job ${this.jobId}`);
            this.loading = true;

            // First check if the export is actually complete
            fetch(`/export-status/${this.jobId}`)
              .then((response) => {
                if (!response.ok) {
                  throw new Error("Ошибка при проверке статуса экспорта");
                }
                return response.json();
              })
              .then((data) => {
                if (data.status !== "completed") {
                  throw new Error("Экспорт еще не завершен");
                }

                // Create a hidden link and trigger the download
                const downloadUrl = `/download-export/${this.jobId}`;
                const a = document.createElement("a");
                a.style.display = "none";
                a.href = downloadUrl;

                document.body.appendChild(a);
                a.click();
                a.remove();

                console.log("Download initiated successfully");
              })
              .catch((error) => {
                console.error("Error downloading file:", error);

                // Provide more specific error messages based on the error type
                let errorMessage = error.message;

                if (
                  error.message.includes("Failed to fetch") ||
                  error.message.includes("Ошибка при проверке статуса")
                ) {
                  errorMessage =
                    "Не удалось подключиться к серверу. Проверьте сетевое соединение.";
                } else if (error.message.includes("Job not found")) {
                  errorMessage =
                    "Файл не найден. Возможно, он был удален или истек срок его хранения.";
                } else if (error.message.includes("Экспорт еще не завершен")) {
                  errorMessage =
                    "Экспорт еще не завершен. Пожалуйста, подождите.";
                }

                alert(`Ошибка при загрузке файла: ${errorMessage}`);
              })
              .finally(() => {
                this.loading = false;
              });
          },

          resetExport() {
            // Reset all progress tracking variables
            this.showProgress = false;
            this.progressPercentage = 0;
            this.currentSupplier = "-";
            this.processedSuppliers = 0;
            this.totalSuppliers = 0;
            this.progressMessage = "Загрузка...";
            this.elapsedTime = null;
            this.startTime = null;
            this.completedSuppliers = [];
            this.emptySuppliers = [];
            this.errorSuppliers = [];
            this.jobId = null;
            this.fileSize = null;
            this.showDownloadButton = false;
            this.loading = false;

            // Clear job ID from localStorage
            localStorage.removeItem("exportJobId");
            console.log("Job ID removed from localStorage by user");
          },

          async checkForRunningJobs() {
            try {
              // Check if there's a job ID in localStorage
              const savedJobId = localStorage.getItem("exportJobId");

              if (!savedJobId) {
                console.log("No saved job ID found in localStorage");
                return;
              }

              console.log(`Found saved job ID in localStorage: ${savedJobId}`);

              // Check if the job is still running
              const response = await fetch(`/export-status/${savedJobId}`);

              if (!response.ok) {
                console.warn(
                  `Job ${savedJobId} not found or error occurred, clearing localStorage`
                );
                localStorage.removeItem("exportJobId");
                return;
              }

              const data = await response.json();
              console.log(`Job status for ${savedJobId}:`, data.status);

              // If job is completed, error, or cancelled, just clear localStorage
              if (
                data.status === "completed" ||
                data.status === "error" ||
                data.status === "cancelled"
              ) {
                console.log(
                  `Job ${savedJobId} is already ${data.status}, clearing localStorage`
                );
                localStorage.removeItem("exportJobId");
                return;
              }

              // If job is still processing, ask user if they want to reconnect
              if (data.status === "processing") {
                if (
                  confirm(
                    `Обнаружена запущенная выгрузка. Хотите продолжить отслеживание прогресса? Нажмите Отмена, чтобы отменить выгрузку.`
                  )
                ) {
                  // Reconnect to the job
                  this.jobId = savedJobId;
                  this.startPolling();
                  this.showProgress = true;

                  // Update UI with current progress
                  this.currentSupplier = data.current_supplier || "-";
                  this.processedSuppliers = data.processed_suppliers || 0;
                  this.totalSuppliers = data.total_suppliers || 0;
                  this.progressMessage = data.message || "Загрузка...";

                  if (data.start_time) {
                    this.startTime = new Date(data.start_time);
                  }

                  if (data.completed_suppliers) {
                    this.completedSuppliers = data.completed_suppliers;
                  }

                  if (data.empty_suppliers) {
                    this.emptySuppliers = data.empty_suppliers;
                  }

                  if (data.error_suppliers) {
                    this.errorSuppliers = data.error_suppliers;
                  }

                  // Calculate progress percentage
                  if (this.totalSuppliers > 0) {
                    this.progressPercentage = Math.round(
                      (this.processedSuppliers / this.totalSuppliers) * 100
                    );
                  }

                  console.log(`Reconnected to job ${savedJobId}`);
                } else {
                  // User declined to reconnect, cancel the job on the server
                  console.log(
                    `Cancelling job ${savedJobId} as user declined to reconnect`
                  );

                  // Send cancellation request to the server
                  fetch(`/cancel-export/${savedJobId}`, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                  })
                    .then((response) => {
                      if (response.ok) {
                        console.log(`Successfully cancelled job ${savedJobId}`);
                      } else {
                        console.error(`Failed to cancel job ${savedJobId}`);
                      }
                    })
                    .catch((error) => {
                      console.error(
                        `Error cancelling job ${savedJobId}:`,
                        error
                      );
                    })
                    .finally(() => {
                      // Clear localStorage regardless of cancellation success
                      localStorage.removeItem("exportJobId");
                      console.log(
                        "Cleared localStorage after cancellation attempt"
                      );
                    });
                }
              }
            } catch (error) {
              console.error("Error checking for running jobs:", error);
              localStorage.removeItem("exportJobId");
            }
          },
        },
      });
    </script>
  </body>
</html>