<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Управление записями</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0/dist/js/select2.min.js"></script>
    <style>
        /* Ваши стили остаются без изменений */
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        form {
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007BFF;
            color: #fff;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            background: #fff;
        }
        th {
            background: #007BFF;
            color: #fff;
        }
        tr:hover {
            background: #f1f1f1;
        }
        .group-list {
            margin: 10px 0;
        }
        .group-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        .group-item input[type="text"] {
            background-color: #e9e9e9;
            border: none;
            pointer-events: none;
            margin-right: 10px;
        }
        .select2-container {
            width: 100% !important;
        }
        /* Модальное окно */
        .modal {
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0;
            top: 0;
            width: 100%; 
            height: 100%; 
            overflow: auto; 
            background-color: rgb(0,0,0); 
            background-color: rgba(0,0,0,0.4); 
            padding-top: 60px;
        }
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto; 
            padding: 20px;
            border: 1px solid #888;
            width: 80%; 
            max-width: 500px; 
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Управление записями</h1>
    <form id="add-record-form">
        <input type="text" name="skl" placeholder="SKU" required>
        <input type="text" name="name" placeholder="Имя" required>
        <input type="text" name="post" placeholder="Срок поставки (числовое значение)" required>
        <div class="group-list">
            <label>Группы:</label>
            <div id="group-container"></div>
        </div>
        <button type="submit">Добавить запись</button>
    </form>

    <table id="records-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>SKL</th>
                <th>Имя</th>
                <th>Срок доставки</th>
                <th>Список групп</th>
                <th>Редактировать шаблон загрузки прайс-листа</th>
                <th>Действия</th>
            </tr>
        </thead>
        <tbody>
            <!-- Данные будут загружены динамически -->
        </tbody>
    </table>

    <div id="groupModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Выберите группу</h2>
            <select id="group-select-modal" multiple="multiple"></select>
            <button id="confirm-add-group">Добавить выбранные группы</button>
        </div>
    </div>

    <script>
        let availableGroups = [];

        $(document).ready(function() {
            loadGroups();  // Загрузка групп
            loadRecords(); // Загрузка записей


            $('#group-select-modal').select2({
                placeholder: "Выберите группы",
                allowClear: true
            });

            $('#add-record-form').on('submit', function(e) {
                e.preventDefault();
                addRecord();
            });

            $(document).on('click', '.add-group', function() {
                $('#groupModal').show();
                const currentRow = $(this).closest('tr');
                $('#confirm-add-group').off('click').on('click', function() {
                    const selectedGroups = $('#group-select-modal').val();
                    if (selectedGroups && selectedGroups.length > 0) {
                        const groupContainer = currentRow.find('.group-list');
                        selectedGroups.forEach(group => {
                            if (!groupContainer.find(`input[value="${group}"]`).length) {
                                groupContainer.append(`
                                    <div class="group-item">
                                        <input type="text" value="${group}" readonly>
                                        <button class="remove-group">Удалить</button>
                                    </div>
                                `);
                            } else {
                                alert(`Группа "${group}" уже добавлена.`);
                            }
                        });
                    }
                    $('#groupModal').hide();
                });
            });

            $('.close').on('click', function() {
                $('#groupModal').hide();
            });

            $(window).on('click', function(event) {
                if (event.target == document.getElementById('groupModal')) {
                    $('#groupModal').hide();
                }
            });

            function loadGroups() {
                $.get('/get_sup_lists', function(data) {
                    availableGroups = data['sups'];
                    renderGroupOptionsModal(); // Рендеринг групп для модального окна
                    renderGroupOptionsForm(); // Рендеринг групп для формы добавления записи
                }).fail(function() {
                    alert('Ошибка при загрузке групп');
                });
            }

            function renderGroupOptionsModal() {
                $('#group-select-modal').empty();
                availableGroups.forEach(group => {
                    $('#group-select-modal').append(`<option value="${group}">${group}</option>`);
                });
                $('#group-select-modal').select2(); // Инициализация select2 после добавления опций
            }

            function renderGroupOptionsForm() {
                $('#group-container').empty(); // Очистка контейнера групп в форме
                // Добавляем мультивыбор групп в форму
                const groupSelect = $('<select id="group-select-form" multiple="multiple" style="width:100%"></select>');
                availableGroups.forEach(group => {
                    groupSelect.append(`<option value="${group}">${group}</option>`);
                });
                $('#group-container').append(groupSelect);
                $('#group-select-form').select2({
                    placeholder: "Выберите группы",
                    allowClear: true
                });
            }

            function loadRecords() {
                $.get('/sups/records/data', function(data) {
                    $('#records-table tbody').empty();
                    if (Array.isArray(data)) {
                        data.forEach(record => {
                            $('#records-table tbody').append(`
                                <tr data-id="${record[0]}">
                                    <td>${record[0]}</td>
                                    <td contenteditable="true">${record[1]}</td>
                                    <td contenteditable="true">${record[2]}</td>
                                    <td contenteditable="true">${record[3]}</td>
                                    <td>
                                        <div class="group-list">
                                            ${record[4].map(group => `
                                                <div class="group-item">
                                                    <input type="text" value="${group}" readonly>
                                                    <button class="remove-group">Удалить</button>
                                                </div>
                                            `).join('')}
                                        </div>
                                        <button class="add-group">Добавить группу</button>
                                    </td>
                                    <td>
                                        <button class="edit-template-btn">Перейти</button> <!-- Кнопка Перейти -->
                                    </td>
                                    <td>
                                        <button class="update-btn"><i class="fas fa-edit"></i> Обновить</button>
                                        <button class="delete-btn"><i class="fas fa-trash"></i> Удалить</button>
                                    </td>
                                </tr>
                            `);
                        });
                    } else {
                        alert('Ошибка: данные не в ожидаемом формате.');
                    }
                    attachEventListeners();
                    attachEventListener();
                }).fail(function() {
                    alert('Ошибка при загрузке записей');
                });
            }

            function addRecord() {
                const skl = $('[name="skl"]').val().trim();
                const name = $('[name="name"]').val().trim();
                const post = $('[name="post"]').val().trim();

                if (!skl || !name || !post) {
                    alert("Все поля должны быть заполнены.");
                    return;
                }

                const selectedGroups = $('#group-container #group-select-form').val(); // Получаем значение из селектора
                const lists = Array.isArray(selectedGroups) ? selectedGroups : (selectedGroups ? [selectedGroups] : []); // Преобразуем в массив, если это не массив

                $.ajax({
                url: '/sups/records',
                type: 'POST',
                contentType: 'application/json', // Установка Content-Type в application/json
                data: JSON.stringify({ skl, name, post, lists }), // Преобразуем объект в JSON-строку
                success: function() {
                    loadRecords(); // Перезагрузка записей после добавления
                    $('#add-record-form')[0].reset(); // Сброс формы
                    $('#group-container').empty(); // Очистка групп
                },
                error: function() {
                    alert('Ошибка при добавлении записи');
                }
            });
            }

            function attachEventListener() {
                $('.edit-template-btn').off('click').on('click', function() {
                    window.location.href = 'http://87.249.37.86/admin/assignments/sprsupdif/';
                });
            }

            function attachEventListeners() {
                $('.update-btn').off('click').on('click', function() {
                const row = $(this).closest('tr');
                const id = row.data('id');
                const skl = row.find('td:eq(1)').text().trim();
                const name = row.find('td:eq(2)').text().trim();
                const post = row.find('td:eq(3)').text().trim();
                
                const lists = row.find('.group-list input').map(function() {
                    return $(this).val();
                }).get();

                const data = JSON.stringify({ skl, name, post, lists });

                $.ajax({
                    url: `/sups/records/${id}`,
                    type: 'PUT',
                    contentType: 'application/json',
                    data: data,
                    success: function() {
                        loadRecords();
                    },
                    error: function() {
                        alert('Ошибка при обновлении записи');
                    }
                });
            });

                $('.delete-btn').off('click').on('click', function() {
                    const row = $(this).closest('tr');
                    const id = row.data('id');
                    if (confirm('Вы уверены, что хотите удалить эту запись?')) {
                        $.ajax({
                            url: `/sups/records/${id}`,
                            type: 'DELETE',
                            success: function() {
                                loadRecords();
                            },
                            error: function() {
                                alert('Ошибка при удалении записи');
                            }
                        });
                    }
                });

                $('.remove-group').off('click').on('click', function() {
                    $(this).closest('.group-item').remove();
                });
            }
        });
    </script>
</body>
</html>