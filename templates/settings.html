<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Settings</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link
    href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    rel="stylesheet"
  />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.2/font/bootstrap-icons.css">
    <style>
        :root {
            --font-size: 16px;
            --large-font-size: 20px;
            --button-padding: 16px 24px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: var(--font-size);
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            align-items: center;
            justify-content: center;
        }

        .container.light {
            background-color: #ffffff;
            color: #000000;
            --border-color: #cccccc;
        }

        .container.dark {
            background-color: #1a1a1a;
            color: #ffffff;
            --border-color: #444444;
        }

        h1 {
            text-align: center;
            border-bottom: 2px solid currentColor;
            padding-bottom: 10px;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        li {
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }

        li.light {
            background-color: #e0e0e0;
        }

        li.light:hover {
            background-color: #d0d0d0;
        }

        li.dark {
            background-color: #333333;
        }

        li.dark:hover {
            background-color: #444444;
        }

        button {
            padding: 12px 18px;
            font-size: var(--font-size);
            background-color: #007bff;
            color: #ffffff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--modal-bg-color);
            margin: 5% auto;
            padding: 30px;
            border: 1px solid var(--border-color);
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .modalcheckfile {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content-check-file {
            background-color: var(--modal-bg-color);
            margin: 5% auto;
            padding: 30px;
            border: 1px solid var(--border-color);
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            --modal-bg-color: #ffffff;
        }

        .modal-content.light {
            --modal-bg-color: #ffffff;
        }

        .modal-content.dark {
            --modal-bg-color: #2c2c2c;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 32px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }
        
        form {
            align-items: center;
        }
        table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: #007bff;
            color: #ffffff;
        }

        .modal-body {
            max-height: 400px;
            overflow-y: auto;
        }

        #selectedSuppliers {
            max-height: 150px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: var(--font-size);
            cursor: pointer;
        }

        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            width: calc(100% - 24px);
            padding: 10px;
            font-size: var(--font-size);
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .widget {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            text-align: center;
        }

        .widget.light {
            background-color: #f9f9f9;
        }

        .widget.dark {
            background-color: #2a2a2a;
        }

        .widget h2 {
            font-size: var(--large-font-size);
            margin-bottom: 20px;
        }

        .widget button {
            font-size: var(--large-font-size);
            padding: var(--button-padding);
        }

        #uploadResult {
            margin-top: 20px;
        }

        #uploadFormSaveEmex {
            text-align: center;
        }

        #uploadFormCheckEmex {
            text-align: center;
        }

        .loader {
            display: none; /* По умолчанию скрыт */
            border: 16px solid #f3f3f3; /* Светло-серый фон */
            border-top: 16px solid #3498db; /* Синий верх */
            border-radius: 50%;
            width: 80px;
            height: 80px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
            margin-top: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .disabled {
            pointer-events: none; /* Блокирует взаимодействие */
            opacity: 0.6; /* Полупрозрачность */
        }

        .brand-filter, .original-brands-option {
        margin-bottom: 20px;
    }
    
    .brand-filter label, .original-brands-option label {
        font-weight: bold;
        display: block;
        margin-bottom: 5px;
    }
    
    .brand-filter select {
        width: 100%;
        height: 100px;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ccc;
        margin-bottom: 10px;
    }
    
    .brand-filter input[type="text"] {
        width: calc(100% - 22px);
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ccc;
        margin-right: 5px;
    }
    
    .brand-filter button {
        margin-right: 0;
    }
    
    .original-brands-option input {
        margin-right: 10px;
    }
    </style>

</head>
<body class="light">
    <div class="widget light">
        <h2>Управление списком брендов</h2>
        <button onclick="window.location.href='/brands'">Перейти</button>
    </div>
    <div class="widget light">
        <h2>Управление списком поставщиков</h2>
        <button onclick="window.location.href='/sups/records'">Перейти</button>
    </div>
    <div class="container light">
        <h4>Группы поставщиков</h4>
        <ul>
            {% for list in sup_lists %}
                <li class="light">
                    <span>{{ list }}</span>
                    <div>
                        <button onclick="openModal('{{ list }}')" data-name="{{ list }}">✏️ Edit</button>
                        <button onclick="deleteGroup('{{ list }}')" data-name="{{ list }}">❌ Delete</button>
                    </div>
                </li>
            {% endfor %}
        </ul>
        <button onclick="openAddGroupModal()">➕ Add Group</button>
    </div>

    <div class="widget light">
        <h2>Статистика по поставщикам с флагами дифференцировано по брендам</h2>
        <!-- Блок для списка брендов -->
        <div class="brand-filter">
            <label for="brandList">Список брендов для фильтрации:</label>
            <select id="brandList" multiple>
                <!-- Пример значений, добавьте или удалите по необходимости -->
            </select>
            <input type="text" id="brandInput" placeholder="Введите название бренда">
            <button onclick="addBrand()">Добавить бренд</button>
            <button onclick="removeBrand()">Удалить бренд</button>
    </div>
        
        <!-- Опция "Только оригинальные бренды" -->
        <div class="original-brands-option">
            <label>
                <input type="checkbox" id="originalBrandsOnly">
                Только оригинальные бренды
            </label>
        </div>
        
        <button onclick="downloadSummaryExcel()">Выгрузить сводный файл-excel по поставщикам</button>
    </div>
    <!-- Loader indicators -->
<div id="loaderCheck" class="text-center my-3 text-muted" style="display:none;">
  <div class="spinner-border spinner-border-sm me-2" role="status"></div>
  Загрузка...
</div>

<div id="loaderSave" class="text-center my-3 text-muted" style="display:none;">
  <div class="spinner-border spinner-border-sm me-2" role="status"></div>
  Загрузка...
</div>

<div class="container mt-5" style="max-width: 600px;">
  <!-- Check form -->
  <div class="card shadow-sm border-0 rounded-3 mb-4">
    <div class="card-body p-4">
      <h4 class="mb-4">Проверить обновленный TXT-файл EMEX на изменения</h4>
      <form id="uploadFormCheckEmex" action="/upload_txt_emex_check" method="post" enctype="multipart/form-data">

        <div class="mb-3">
          <label for="txtFileCheck" class="form-label">
            Загрузите txt-файл
            <i
              class="bi bi-question-circle ms-1 text-muted"
              data-bs-toggle="tooltip"
              data-bs-html="true"
              title="Формат: 16 столбцов, разделенных TAB'ом, с заголовком и первой строкой данных в кодировке cp1251">
            </i>
          </label>
          <input class="form-control" type="file" id="txtFileCheck" name="txtFile" accept=".txt">
        </div>

        <button type="submit" class="btn btn-outline-primary w-100">Начать проверку</button>
      </form>
      <div id="uploadResultCheck" class="mt-3"></div>
    </div>
  </div>

  <!-- Save form -->
  <div class="card shadow-sm border-0 rounded-3">
    <div class="card-body p-4">
      <h4 class="mb-4">Загрузить обновленный TXT-файл EMEX в базу данных</h4>
      <form id="uploadFormSaveEmex" action="/upload_txt_emex_save" method="post" enctype="multipart/form-data">

        <div class="mb-3">
          <label for="txtFileSave" class="form-label">
            Загрузите txt-файл
            <i
              class="bi bi-question-circle ms-1 text-muted"
              data-bs-toggle="tooltip"
              data-bs-html="true"
              title="Формат: 16 столбцов, разделенных TAB'ом, с заголовком и первой строкой данных в кодировке cp1251">
            </i>
          </label>
          <input class="form-control" type="file" id="txtFileSave" name="txtFile" accept=".txt">
        </div>

        <button type="submit" class="btn btn-primary w-100">Начать загрузку</button>
      </form>
      <div id="uploadResultSave" class="mt-3"></div>
    </div>
  </div>
</div>


    <!-- Модальное окно -->
    <div id="myModal" class="modal">
        <div class="modal-content light">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalGroupName">Supplier Group Name</h2>
            <div class="search-container">
                <input type="text" id="searchModal" class="search-input" placeholder="Search Suppliers">
            </div>
            <div id="modalBody" class="modal-body"></div>
            <div id="modalButtons">
                <button onclick="addSupplier()">➕ Add Supplier</button>
            </div>
        </div>
    </div>

    <!-- Модальное окно для добавления поставщика -->
    <div id="addSupplierModal" class="modal">
        <div class="modal-content light">
            <span class="close" onclick="closeAddSupplierModal()">&times;</span>
            <h2>Select Suppliers to Add</h2>
            <div class="search-container">
                <input type="text" id="searchSupplier" class="search-input" placeholder="Search Suppliers">
            </div>
            <div id="addSupplierBody" class="modal-body"></div>
            <div id="addSupplierButtons">
                <button onclick="applySuppliers()">Apply</button>
            </div>
        </div>
    </div>

    
    <!-- Модальное окно для добавления группы -->
    <div id="addGroupModal" class="modal">
        <div class="modal-content light">
            <span class="close" onclick="closeAddGroupModal()">&times;</span>
            <h2>Create New Group</h2>
            <input type="text" id="newGroupName" placeholder="Group Name" style="font-size: var(--font-size); padding: 10px; width: calc(100% - 22px);">
            <button onclick="selectSuppliers()" style="font-size: var(--font-size); padding: 10px; margin-top: 10px;">Select Suppliers</button>
            <div id="selectedSuppliers"></div>
            <button onclick="createGroup()" style="font-size: var(--font-size); padding: 10px; margin-top: 10px;">Create Group</button>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentGroupName = '';

        function openModal(name) {
            currentGroupName = name;
            fetch(`/list_of_sups/${name}`)
                .then(response => response.json())
                .then(data => {
                    const tableHtml = `<table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.sups.map(sup => `<tr>
                                <td>${sup[0]}</td>
                                <td>${sup[1]}</td>
                                <td><button onclick="deleteSupplier(${sup[0]})">❌ Delete</button></td>
                            </tr>`).join('')}
                        </tbody>
                    </table>`;
                    document.getElementById('modalGroupName').textContent = `Supplier Group: ${currentGroupName}`;
                    document.getElementById('modalBody').innerHTML = tableHtml;
                    document.getElementById('myModal').style.display = 'block';

                    document.getElementById('searchModal').addEventListener('input', function() {
                        const filter = this.value.toLowerCase();
                        const rows = document.querySelectorAll('#modalBody tbody tr');
                        rows.forEach(row => {
                            const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                            row.style.display = name.includes(filter) ? '' : 'none';
                        });
                    });
                })
                .catch(error => console.error('Error fetching supplier list:', error));
        }

        function closeModal() {
            document.getElementById('myModal').style.display = 'none';
        }

        function deleteSupplier(supId) {
            if (confirm(`Are you sure you want to delete supplier ${supId} from group ${currentGroupName}?`)) {
                fetch(`/del_sup_from_list`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ dif_id: supId, name: currentGroupName })
                }).then(response => {
                    if (response.ok) {
                        closeModal();
                    } else {
                        alert("Failed to delete supplier");
                    }
                }).catch(error => console.error('Error:', error));
            }
        }

        function deleteGroup(groupName) {
            if (confirm(`Are you sure you want to delete the group ${groupName}?`)) {
                fetch(`/del_sup_list`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name: groupName })
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert("Failed to delete group");
                    }
                }).catch(error => console.error('Error:', error));
            }
        }

        function addSupplier() {
            fetch('/get_all_sups')
                .then(response => response.json())
                .then(data => {
                    const addSupplierHtml = `<table>
                        <thead>
                            <tr>
                                <th>Select</th>
                                <th>ID</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.sups.map(sup => `<tr>
                                <td><input type="checkbox" name="supplier" value="${sup[0]}" data-name="${sup[1]}"></td>
                                <td>${sup[0]}</td>
                                <td>${sup[1]}</td>
                            </tr>`).join('')}
                        </tbody>
                    </table>`;
                    document.getElementById('addSupplierBody').innerHTML = addSupplierHtml;
                    document.getElementById('addSupplierModal').style.display = 'block';

                    document.getElementById('searchSupplier').addEventListener('input', function() {
                        const filter = this.value.toLowerCase();
                        const rows = document.querySelectorAll('#addSupplierBody tbody tr');
                        rows.forEach(row => {
                            const name = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                            row.style.display = name.includes(filter) ? '' : 'none';
                        });
                    });
                })
                .catch(error => console.error('Error fetching all suppliers:', error));
        }

        function closeAddSupplierModal() {
            document.getElementById('addSupplierModal').style.display = 'none';
        }

        function applySuppliers() {
            const checkboxes = document.querySelectorAll('input[name="supplier"]:checked');
            const selectedSuppliers = Array.from(checkboxes).map(checkbox => ({
                dif_id: checkbox.value,
                name: currentGroupName,
            }));

            fetch('/set_sup_in_list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(selectedSuppliers)
            }).then(response => {
                if (response.ok) {
                    closeAddSupplierModal();
                    closeModal();
                } else {
                    alert("Failed to add suppliers");
                }
            }).catch(error => console.error('Error:', error));
        }

        function openAddGroupModal() {
            document.getElementById('addGroupModal').style.display = 'block';
        }

        function closeAddGroupModal() {
            document.getElementById('addGroupModal').style.display = 'none';
        }

        function selectSuppliers() {
            fetch('/get_all_sups')
                .then(response => response.json())
                .then(data => {
                    const selectSupplierHtml = `<table>
                        <thead>
                            <tr>
                                <th>Select</th>
                                <th>ID</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.sups.map(sup => `<tr>
                                <td><input type="checkbox" name="new-supplier" value="${sup[0]}" data-name="${sup[1]}"></td>
                                <td>${sup[0]}</td>
                                <td>${sup[1]}</td>
                            </tr>`).join('')}
                        </tbody>
                    </table>`;
                    document.getElementById('selectedSuppliers').innerHTML = selectSupplierHtml;
                })
                .catch(error => console.error('Error fetching all suppliers:', error));
        }

        function createGroup() {
            const groupName = document.getElementById('newGroupName').value;
            const checkboxes = document.querySelectorAll('input[name="new-supplier"]:checked');
            const selectedSuppliers = Array.from(checkboxes).map(checkbox => ({
                dif_id: checkbox.value,
                name: groupName,
            }));

            fetch('/set_sup_in_list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(selectedSuppliers)
            }).then(response => {
                if (response.ok) {
                    closeAddGroupModal();
                    location.reload();
                } else {
                    alert("Failed to create group");
                }
            }).catch(error => console.error('Error:', error));
        }

        function addBrand() {
            const input = document.getElementById('brandInput');
            const brand = input.value.trim();
            
            if (brand) {
                const select = document.getElementById('brandList');
                const existingOptions = Array.from(select.options).map(option => option.value);
                
                if (!existingOptions.includes(brand)) {
                    const option = document.createElement('option');
                    option.value = brand;
                    option.text = brand;
                    select.add(option);
                }
                
                input.value = ''; // Очистить поле ввода
            } else {
                alert('Введите название бренда.');
            }
        }

        function removeBrand() {
            const select = document.getElementById('brandList');
            const selectedOptions = Array.from(select.selectedOptions);
            selectedOptions.forEach(option => select.remove(option.index));
        }

        function downloadSummaryExcel() {
            const select = document.getElementById('brandList');
            let selectedBrands = Array.from(select.options).map(option => option.value);
            
            const originalBrandsOnly = document.getElementById('originalBrandsOnly').checked;
            
            if (selectedBrands.length === 0 && !originalBrandsOnly) {
                alert('Пожалуйста, выберите хотя бы один бренд или опцию "Только оригинальные бренды".');
                return;
            }

            if (selectedBrands.length === 0) {
                selectedBrands = null;
            }
            
            const requestData = {
                brands: selectedBrands,
                originalBrandsOnly: originalBrandsOnly
            };
            
            document.getElementById('loaderCheck').style.display = 'block';
            
            fetch('/download_suppliers_summary_excel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loaderCheck').style.display = 'none';
                if (data.error) {
                    alert('Ошибка при формировании excel-файла.');
                } else {
                    const link = document.createElement('a');
                    link.href = data.url;
                    link.download = 'suppliers_summary.xlsx';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch(error => {
                document.getElementById('loaderCheck').style.display = 'none';
                console.error('Error downloading Excel file:', error);
                alert('Произошла ошибка при скачивании файла.');
            });
        }

        async function handleFormSubmit(event, form, resultElement, loader) {
            event.preventDefault();
            const fileInput = form.querySelector('input[type="file"]');
            const file = fileInput.files[0];

            resultElement.textContent = '';
            resultElement.className = '';

            if (!file) {
                resultElement.textContent = 'Пожалуйста, выберите файл.';
                resultElement.className = 'error';
                return;
            }

            // 1. Read file chunk for validation
            loader.style.display = 'block';
            resultElement.textContent = 'Чтение файла для проверки...';
            const reader = new FileReader();
            reader.readAsArrayBuffer(file.slice(0, 4096));

            reader.onload = async function(e) {
                resultElement.textContent = 'Отправка файла на проверку...';
                const chunk = e.target.result;

                // 2. Send chunk to backend for validation
                const validationResponse = await fetch('/upload_txt_emex_validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/octet-stream'
                    },
                    body: chunk
                });

                const validationData = await validationResponse.json();

                if (!validationData.success) {
                    loader.style.display = 'none';
                    resultElement.textContent = `Ошибка: ${validationData.error}`;
                    resultElement.className = 'error';
                    return;
                }

                // 3. If validation is successful, proceed with full upload
                resultElement.textContent = 'Формат файла корректен. Начинаем обработку...';
                const mainFormData = new FormData(form);
                const finalResponse = await fetch(form.action, {
                    method: form.method,
                    body: mainFormData
                });
                
                const finalData = await finalResponse.json();
                loader.style.display = 'none';

                if (finalData.error) {
                    resultElement.textContent = 'Ошибка: ' + finalData.error;
                    resultElement.className = 'error';
                } else if (finalData.result) {
                    const tableHtml = `<table>
                        <thead>
                            <tr>
                                ${Object.keys(finalData.result).map(key => `<th>${key}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                ${Object.values(finalData.result).map(value => `<td>${value}</td>`).join('')}
                            </tr>
                        </tbody>
                    </table>`;
                    showModalcheckfile('Результаты', tableHtml);
                    resultElement.textContent = '';
                } else {
                    resultElement.textContent = 'Произошла неизвестная ошибка.';
                    resultElement.className = 'error';
                }
            };

            reader.onerror = function() {
                loader.style.display = 'none';
                resultElement.textContent = 'Не удалось прочитать файл на клиенте.';
                resultElement.className = 'error';
            };
        }

        // Attach event listeners
        const uploadFormCheck = document.getElementById('uploadFormCheckEmex');
        const uploadFormSave = document.getElementById('uploadFormSaveEmex');

        uploadFormCheck.addEventListener('submit', (e) => handleFormSubmit(
            e,
            uploadFormCheck,
            document.getElementById('uploadResultCheck'),
            document.getElementById('loaderCheck')
        ));
        
        uploadFormSave.addEventListener('submit', (e) => handleFormSubmit(
            e,
            uploadFormSave,
            document.getElementById('uploadResultSave'),
            document.getElementById('loaderSave')
        ));


        function showModalcheckfile(title, content) {
            const modalHtml = `<div class="modalcheckfile">
                <div class="modal-content-check-file">
                    <span class="close" onclick="closeModalCheckFile()">&times;</span>
                    <h2>${title}</h2>
                    <div>${content}</div>
                </div>
            </div>`;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            document.querySelector('.modalcheckfile').style.display = 'block';
        }

        function closeModalCheckFile() {
            const modal = document.querySelector('.modalcheckfile');
            if (modal) {
                modal.remove();
            }
        }

    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
      var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
          html: true,
          container: 'body',
          boundary: 'window',
          sanitize: false
        });
      });
    });
    </script>
</body>
</html>
