{% extends "parser/base_parser.html" %}
{% block title %}Автоматическая проценка по парсеру{% endblock title %}
{% block page_type %}parser_scheduller{% endblock page_type %}
{% block content %}

<div class="container">
  <h2>Загрузка файла</h2>
  <form id="uploadForm">
    <div class="form-group">
      <label for="platforms">Выберите площадки:</label>
      <select id="platforms" name="platforms" multiple="multiple">
        <option value="Emex">Emex</option>
        <option value="Part-kom">Part-kom</option>
        <option value="Part-kom_OPT">Part-kom_OPT</option>
        <option value="Autopiter">Autopiter</option>
        <option value="EmexOfficial">EmexOfficial</option>
      </select>
    </div>
    <div class="form-group">
      <label for="fileInput">Выберите файл:</label>
      <input
        type="file"
        name="fileInput"
        id="fileInput"
        accept=".xls, .xlsx, .csv"
      />
    </div>
    <div class="form-group">
      <label for="schedulleType">Выберите тип планировщика:</label>
      <select id="schedulleType" name="schedulleType" multiple="multiple">
        <option value="everyday">Ежедневный</option>
        <option value="everyweek|0">Еженедельный - по понедельникам</option>
        <option value="everyweek|1">Еженедельный - по вторникам</option>
        <option value="everyweek|2">Еженедельный - по средам</option>
        <option value="everyweek|3">Еженедельный - по четвергам</option>
        <option value="everyweek|4">Еженедельный - по пятницам</option>
        <option value="everyweek|5">Еженедельный - по субботам</option>
        <option value="everyweek|6">Еженедельный - по воскресеньям</option>
      </select>
    </div>

    <!-- Поля фильтров -->
    <div class="form-group">
      <label for="deliveryValue">Срок доставки менее (укажите число в днях):</label>
      <input type="number" id="deliveryValue" value="5" name="deliveryValue" placeholder="Введите число" min="0" />
    </div>
    <div class="form-group">
      <label for="percent">Рейтинг поставщика (%):</label>
      <input type="number" id="percent" value="60" name="percent" placeholder="Введите процент" min="0" max="100"/>
    </div>
    <div class="form-group">
      <label>Выгружать аналоги и замены:</label>
      <input type="checkbox" id="exportAnalogs" name="exportAnalogs" />
    </div>

    <!-- Динамическое добавление фильтров для площадок -->
    <div id="platformFiltersContainer"></div>

    <p class="instructions">
      * Файл загрузки должен иметь следующий шаблон: 1 - Бренд, 2 - Артикул.
      Формат .xlsx или .csv
    </p>
    <div id="errorMessages" class="error-message"></div>
    <div class="form-group d-flex justify-content-between align-items-center">
      <button type="submit" class="btn btn-primary">Загрузить</button>
      <button type="button" class="btn btn-outline-primary" id="toggleExcludedProviders" data-bs-toggle="offcanvas"
        data-bs-target="#excludedProvidersOffcanvas">
        <i class="bi bi-gear-fill me-1"></i> Исключенные поставщики
      </button>
    </div>
  </form>
  <div id="responseMessage">Идёт парсинг и обработка...</div>
  <div class="loader"></div>
</div>

<!-- Offcanvas for Excluded Providers -->
<div class="excluded-providers-container">
  {% include 'parser/excluded_providers_form.html' %}
</div>

{% endblock content %}
