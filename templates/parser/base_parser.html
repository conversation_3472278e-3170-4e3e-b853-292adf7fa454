<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>{% block title %}{% endblock title %}</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="{{ url_for('static', filename='parser/parser_styles.css') }}" />
    <script>
        // Page identifier that will be set by each page
        window.pageType = "{% block page_type %}default{% endblock page_type %}";
    </script>
</head>

<body class="light-theme">
{% include '_back_button.html' %}

<div class="theme-toggle">
    <input type="checkbox" id="themeSwitch"/>
    <label for="themeSwitch" id="themeIcon">&#9728;</label>
</div>
{% block content %}{% endblock content %}

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>

<!-- Parser-specific js -->
<script src="{{ url_for('static', filename='parser/parser_scripts.js') }}?v=1.0.1"></script>
</body>
</html>
