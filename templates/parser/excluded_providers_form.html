<!-- Offcanvas for Excluded Providers -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="excludedProvidersOffcanvas" aria-labelledby="excludedProvidersOffcanvasLabel" style="width: 450px;">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="excludedProvidersOffcanvasLabel">Настройка исключенных поставщиков</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <div id="excludedProvidersSection">
            <div id="excludedProvidersLoading" class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Загрузка...</span>
                </div>
            </div>

            <div id="excludedProvidersContent" style="display: none;">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    Исключенные поставщики не будут учитываться при парсинге данных.
                </div>

                <div id="providersTabContent" class="mt-3">
                    <!-- Content will be dynamically generated -->
                </div>

                <div class="mt-4 d-flex align-items-center gap-3">
                    <div class="position-relative">
                        <button type="button" class="btn btn-primary" id="saveExcludedProviders">
                            <i class="bi bi-save me-1"></i> Сохранить
                        </button>
                        <span id="saveIndicator" class="position-absolute top-0 start-100 translate-middle p-1 bg-warning rounded-circle" style="display: none;">
                            <span class="visually-hidden">Есть несохраненные изменения</span>
                        </span>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" id="cancelExcludedProviders">
                        <i class="bi bi-x-circle me-1"></i> Отмена
                    </button>
                </div>
            </div>

            <div id="excludedProvidersMessage" class="alert mt-3" style="display: none;">
                <!-- Success or error message will be shown here -->
            </div>
        </div>
    </div>
</div>

