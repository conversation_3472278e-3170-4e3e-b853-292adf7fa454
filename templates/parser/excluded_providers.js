// Global variable to store the current state of excluded providers
let excludedProviders = {};
let originalExcludedProviders = {};

// Mock data for testing
const mockData = {
    "Emex": ["JFMG", "KKMO", "NYZD", "RJ<PERSON>"],
    "Part-kom": ["13072", "14165", "15602", "13978"],
    "Part-kom_OPT": ["13072", "14165", "15602", "13978"],
    "Autopiter": ["CCER", "LRLB"]
};

function initExcludedProviders() {
    // Set up event listeners
    document.getElementById('toggleExcludedProviders').addEventListener('click', toggleExcludedProvidersSection);
    document.getElementById('saveExcludedProviders').addEventListener('click', saveExcludedProviders);
    document.getElementById('cancelExcludedProviders').addEventListener('click', cancelChanges);

    // Load excluded providers data
    loadExcludedProviders();
}

function toggleExcludedProvidersSection() {
    const section = document.getElementById('excludedProvidersSection');
    if (section.style.display === 'none') {
        section.style.display = 'block';
        loadExcludedProviders();
    } else {
        section.style.display = 'none';
    }
}

function loadExcludedProviders() {
    const loadingElement = document.getElementById('excludedProvidersLoading');
    const contentElement = document.getElementById('excludedProvidersContent');
    const messageElement = document.getElementById('excludedProvidersMessage');

    loadingElement.style.display = 'block';
    contentElement.style.display = 'none';
    messageElement.style.display = 'none';

    // Fetch excluded providers from the API
    fetch('/get_excluded_providers')
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch excluded providers');
            }
            return response.json();
        })
        .then(data => {
            excludedProviders = data;
            originalExcludedProviders = JSON.parse(JSON.stringify(data)); // Deep copy
            renderExcludedProviders();

            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';
        })
        .catch(error => {
            console.error('Error fetching excluded providers:', error);
            showMessage('Ошибка при загрузке данных: ' + error.message, 'danger');
            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';

            // Fallback to mock data in case of error
            excludedProviders = JSON.parse(JSON.stringify(mockData));
            originalExcludedProviders = JSON.parse(JSON.stringify(mockData));
            renderExcludedProviders();
        });
}

function showMessage(message, type = 'success') {
    const messageElement = document.getElementById('excludedProvidersMessage');
    messageElement.className = `alert alert-${type} mt-3`;
    messageElement.textContent = message;
    messageElement.style.display = 'block';

    // Auto-hide message after 5 seconds
    // setTimeout(() => {
    //     messageElement.style.display = 'none';
    // }, 5000);
}

function renderExcludedProviders() {
    const container = document.getElementById('providersTabContent');
    container.innerHTML = '';

    // Create tabs for each parser
    const parsers = Object.keys(excludedProviders);

    if (parsers.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">Нет данных об исключенных поставщиках</div>';
        return;
    }

    // Create accordion for each parser
    let accordionHtml = '<div class="accordion" id="excludedProvidersAccordion">';

    parsers.forEach((parser, index) => {
        const providers = excludedProviders[parser];
        const headingId = `heading${parser.replace('-', '_')}`;
        const collapseId = `collapse${parser.replace('-', '_')}`;
        const isFirst = index === 0;

        accordionHtml += `
            <div class="accordion-item">
                <h2 class="accordion-header" id="${headingId}">
                    <button class="accordion-button ${isFirst ? '' : 'collapsed'}" type="button"
                            data-bs-toggle="collapse" data-bs-target="#${collapseId}"
                            aria-expanded="${isFirst ? 'true' : 'false'}" aria-controls="${collapseId}">
                        ${parser} (${providers.length} поставщиков)
                    </button>
                </h2>
                <div id="${collapseId}" class="accordion-collapse collapse ${isFirst ? 'show' : ''}"
                     aria-labelledby="${headingId}" data-bs-parent="#excludedProvidersAccordion">
                    <div class="accordion-body">
                        <div class="mb-3">
                            <div class="input-group">
                                <input type="text" class="form-control" id="newProvider${parser.replace('-', '_')}"
                                       placeholder="ID поставщика">
                                <button class="btn btn-outline-primary" type="button"
                                        onclick="addProvider('${parser}')">Добавить</button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>ID поставщика</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody id="providersList${parser.replace('-', '_')}">
                                    ${providers.map(provider => `
                                        <tr>
                                            <td>${provider}</td>
                                            <td>
                                                <button class="btn btn-sm btn-danger"
                                                        onclick="removeProvider('${parser}', '${provider}')">
                                                    Удалить
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    accordionHtml += '</div>';
    container.innerHTML = accordionHtml;
}

function addProvider(parser) {
    const inputElement = document.getElementById(`newProvider${parser.replace('-', '_')}`);
    const providerId = inputElement.value.trim();

    if (!providerId) {
        showMessage('Введите ID поставщика', 'warning');
        return;
    }

    if (!excludedProviders[parser].includes(providerId)) {
        excludedProviders[parser].push(providerId);
        renderExcludedProviders();
    } else {
        showMessage('Этот поставщик уже в списке исключенных', 'warning');
    }

    inputElement.value = '';
}

function removeProvider(parser, provider) {
    excludedProviders[parser] = excludedProviders[parser].filter(p => p !== provider);
    renderExcludedProviders();
}

function cancelChanges() {
    // Reset to original state
    excludedProviders = JSON.parse(JSON.stringify(originalExcludedProviders));
    renderExcludedProviders();
    showMessage('Изменения отменены', 'info');
}

function saveExcludedProviders() {
    if (JSON.stringify(excludedProviders) === JSON.stringify(originalExcludedProviders)) {
        showMessage('Нет изменений для сохранения', 'info');
        return;
    }

    const loadingElement = document.getElementById('excludedProvidersLoading');
    const contentElement = document.getElementById('excludedProvidersContent');

    loadingElement.style.display = 'block';
    contentElement.style.display = 'none';

    // Track successful updates
    let successCount = 0;
    let failCount = 0;
    const totalParsers = Object.keys(excludedProviders).length;

    // Save each parser's excluded providers
    const savePromises = Object.keys(excludedProviders).map(parser => {
        return fetch(`/update_excluded_providers/${parser}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                providers: excludedProviders[parser]
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || `Ошибка при сохранении для ${parser}`);
                });
            }
            return response.json();
        })
        .then(data => {
            successCount++;
            return data;
        })
        .catch(error => {
            console.error(`Error updating excluded providers for ${parser}:`, error);
            failCount++;
            throw error;
        });
    });

    // Wait for all save operations to complete
    Promise.allSettled(savePromises)
        .then(results => {
            loadingElement.style.display = 'none';
            contentElement.style.display = 'block';

            if (failCount === 0) {
                // All updates were successful
                originalExcludedProviders = JSON.parse(JSON.stringify(excludedProviders)); // Update original state
                showMessage('Изменения успешно сохранены');
            } else if (successCount === 0) {
                // All updates failed
                showMessage('Не удалось сохранить изменения', 'danger');
            } else {
                // Some updates succeeded, some failed
                showMessage(`Сохранено ${successCount} из ${totalParsers} парсеров`, 'warning');
                // Reload to get the current state
                loadExcludedProviders();
            }
        });
}
