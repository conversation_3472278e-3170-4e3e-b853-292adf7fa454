<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Best Price Uploader</title>
  <!-- Bootstrap CSS -->
  <link
    href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    rel="stylesheet"
  />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.2/font/bootstrap-icons.css">
</head>
<body>
  <div class="container py-5">
    <h1 class="mb-4">Upload Excel &amp; Select Date Range</h1>
    <form id="upload-form" class="needs-validation" novalidate>
      <!-- File input -->
      <div class="mb-3">
        <label for="file" class="form-label">
          Загрузите Excel-файл
          <i 
            class="bi bi-question-circle ms-1 text-muted" 
            data-bs-toggle="tooltip" 
            data-bs-html="true"
            title="<strong>Пример формата:</strong><br>
                   <table class='table table-sm table-bordered mt-2 mb-1'>
                     <thead class='table-light'>
                       <tr><th>brand</th><th>article</th></tr>
                     </thead>
                     <tbody>
                       <tr><td>LUK</td><td>622322900</td></tr>
                       <tr><td>GATES</td><td>KP15631XS1</td></tr>
                       <tr><td>VALEO</td><td>046900</td></tr>
                     </tbody>
                   </table>">
          </i>
        </label>
        <input
          type="file"
          class="form-control"
          id="file"
          accept=".xlsx"
          required
        />
        <div class="form-text">
          <small>Ожидаемые колонки: <code>brand</code> и <code>article</code> (по-английски)</small>
        </div>
        <div class="invalid-feedback">Please upload a valid .xlsx file with the required columns.</div>
      </div>

      <!-- Platforms multi-select -->
      <div class="mb-3">
        <label for="platforms" class="form-label">Platforms</label>
        <select
          class="form-select"
          id="platforms"
          multiple
          required
        >
          <option value="Cross-doc" selected>Cross-doc</option>
          <option value="Part-kom" selected>Part-kom</option>
          <option value="Emex" selected>Emex</option>
        </select>
        <div class="invalid-feedback">
          Please select at least one platform.
        </div>
      </div>

      <!-- Date range -->
      <div class="row mb-3">
        <div class="col">
          <label for="start_date" class="form-label">Start Date</label>
          <input
            type="date"
            class="form-control"
            id="start_date"
            required
          />
          <div class="invalid-feedback">Select a start date.</div>
        </div>
        <div class="col">
          <label for="end_date" class="form-label">End Date</label>
          <input
            type="date"
            class="form-control"
            id="end_date"
            required
          />
          <div class="invalid-feedback">Select an end date.</div>
        </div>
      </div>

      <!-- Submit button & spinner -->
      <button type="submit" class="btn btn-primary">
        Submit
      </button>
      <div
        id="spinner"
        class="spinner-border text-primary ms-3 d-none"
        role="status"
      >
        <span class="visually-hidden">Loading...</span>
      </div>

      <!-- Error & Success messages -->
      <div id="error-msg" class="alert alert-danger mt-3 d-none"></div>
      <div id="success-msg" class="alert alert-success mt-3 d-none"></div>
    </form>
  </div>

  <!-- Bootstrap JS and dependencies -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    const form       = document.getElementById('upload-form');
    const spinner    = document.getElementById('spinner');
    const errorMsg   = document.getElementById('error-msg');
    const successMsg = document.getElementById('success-msg');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      hideMessages();
      // Bootstrap form validation
      if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
      }

      // extract values
      const fileInput = document.getElementById('file');
      const startDate = document.getElementById('start_date').value;
      const endDate   = document.getElementById('end_date').value;
      const platformsSelect = document.getElementById('platforms');
      const platforms = Array.from(platformsSelect.selectedOptions).map(o => o.value);

      if (!fileInput.files.length) {
        showError('File is required.');
        return;
      }
      if (platforms.length === 0) {
        showError('Please select at least one platform.');
        return;
      }
      if (!startDate || !endDate) {
        showError('Both start and end dates are required.');
        return;
      }

      // build form data
      const fd = new FormData();
      fd.append('file', fileInput.files[0]);
      platforms.forEach(p => fd.append('platforms', p));
      fd.append('start_date', startDate);
      fd.append('end_date', endDate);

      // show spinner
      spinner.classList.remove('d-none');

      try {
        const resp = await fetch('http://185.175.47.222:5000/start_best_price', {
          method: 'POST',
          headers: { 'app-key': 'torgzap_vlastelin_777' },
          body: fd
        });
        if (!resp.ok) {
          throw new Error(`Server returned ${resp.status}`);
        }

        // get blob and trigger automatic download
        const blob = await resp.blob();
        const url  = window.URL.createObjectURL(blob);
        const a    = document.createElement('a');
        a.href     = url;

        // extract filename
        const cd = resp.headers.get('Content-Disposition') || '';
        let filename = 'download.xlsx';
        const match = cd.match(/filename="?(.+)"?/);
        if (match) filename = match[1];

        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);

        // show success message
        showSuccess('File download started.');
      } catch (err) {
        showError(
          err.message.startsWith('Server')
            ? 'Server error: ' + err.message
            : 'Network error: ' + err.message
        );
      } finally {
        spinner.classList.add('d-none');
      }
    });

    function showError(msg) {
      errorMsg.textContent = msg;
      errorMsg.classList.remove('d-none');
    }

    function showSuccess(msg) {
      successMsg.textContent = msg;
      successMsg.classList.remove('d-none');
    }

    function hideMessages() {
      errorMsg.classList.add('d-none');
      successMsg.classList.add('d-none');
    }

    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
      var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
          html: true,
          container: 'body',
          boundary: 'window',
          sanitize: false
        });
      });
    });
  </script>
</body>
</html>