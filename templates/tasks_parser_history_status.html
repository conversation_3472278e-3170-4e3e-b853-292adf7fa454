<!DOCTYPE html>
<html>
<head>
    <title>Tasks Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .task-container {
            width: 80%;
            margin: 0 auto;
        }
        .task-header {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .task-detail {
            margin-bottom: 10px;
        }
        .task-status {
            color: #333;
        }
        .task-status.SUCCESS {
            color: green;
        }
        .task-status.FAILURE {
            color: red;
        }
        .task-status.pending {
            color: orange;
        }
        .task-table {
            width: 100%;
            border-collapse: collapse;
        }
        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .task-table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .download-button {
            padding: 5px 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            cursor: pointer;
        }
        .download-button:hover {
            background-color: #0056b3;
        }
        .detail-button {
            padding: 5px 10px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .detail-button:hover {
            background-color: #218838;
        }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
        .action-dropdown {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
        .action-dropdown option {
            padding: 5px;
        }
    </style>
    <script>

        function updateTaskStatus() {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('format', 'json'); // Set the format to JSON

            fetch(currentUrl, {
                method: 'GET',
                headers: { "app-key": 'torgzap_vlastelin_777' }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    const tbody = document.querySelector('.task-table tbody');
                    tbody.innerHTML = ''; // Clear existing rows

                    // Сортируем задачи по дате 'received' в убывающем порядке
                    const sortedTasks = Object.entries(data).sort(([, a], [, b]) => {
                        const dateA = a.received ? new Date(a.received) : new Date(0);
                        const dateB = b.received ? new Date(b.received) : new Date(0);
                        return dateB - dateA; // Descending order
                    });

                    for (const [task_id, task] of sortedTasks) {
                        const stateClass = task.state ? task.state.toLowerCase() : 'unknown';

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${task_id}</td>
                            <td class="task-detail">${task.filename || 'N/A'}</td>
                            <td class="task-detail">${task.platforms.join(', ')}</td>
                            <td class="task-status ${stateClass}">${task.status || 'Unknown'}</td>
                            <td>${task.progress || 'N/A'}</td>
                            <td>
                                ${task.url ? `<a href="${task.url}" class="download-button" target="_blank">Скачать</a>` : 'Нет результата'}
                            </td>
                            <td>${task.received || 'N/A'}</td>
                            <td>${task.succeeded || 'N/A'}</td>
                            <td>${task.runtime || 'N/A'}</td>
                            <td>
                                <select class="action-dropdown" onchange="handleAction(this, '${task_id}')">
                                    <option value="">Действие</option>
                                    ${['PROGRESS', 'STARTED'].includes(task.status) ? `
                                        <option value="force_delete" style="color: red;">Удалить</option>
                                        <option value="stop" style="color: orange;">Остановить</option>
                                        <option value="restart" style="color: green;">Перезапустить</option>
                                    ` : `
                                        <option value="delete" style="color: red;">Удалить</option>
                                        <option value="restart" style="color: green;">Перезапустить</option>
                                    `}
                                </select>
                            </td>
                        `;
                        tbody.appendChild(row);
                    }
                })
                .catch(error => {
                    console.error('Error fetching task status:', error);
                });
        }

        function handleAction(selectElement, task_id) {
            const action = selectElement.value;
            if (action === 'delete') {
                deleteTask(task_id);
            } else if (action === 'restart') {
                restartTask(task_id);
            } else if (action === 'stop') {
                stopTask(task_id);
            } else if (action === 'force_delete') {
                forceDeleteTask(task_id);
            }
            selectElement.value = ''; // Reset the dropdown
        }

        function deleteTask(task_id) {
            fetch(`/delete_task/${task_id}`, {
                method: 'DELETE',
            })
            .then(data => {
                if (data) {
                    // Remove the task row from the table
                    const row = document.querySelector(`.task-table tbody tr[data-task-id="${task_id}"]`);
                    if (row) {
                        row.remove();
                    }
                    // alert('Task deleted successfully'); // Удаляем алерт
                }
            })
            .catch(error => {
                console.error('Error deleting task:', error);
                // alert('Error deleting task'); // Удаляем алерт
            });
        }

        function restartTask(task_id) {
            fetch(`/restart_task/${task_id}`, {
                method: 'POST',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data) {
                    // alert('Task restarted successfully'); // Удаляем алерт
                }
            })
            .catch(error => {
                console.error('Error restarting task:', error);
                // alert('Error restarting task'); // Удаляем алерт
            });
        }

        function stopTask(task_id) {
            fetch(`/stop_task/${task_id}`, {
                method: 'POST',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data) {
                    // alert('Task stopped successfully'); // Удаляем алерт
                }
            })
            .catch(error => {
                console.error('Error stopping task:', error);
                // alert('Error stopping task'); // Удаляем алерт
            });
        }

        function forceDeleteTask(task_id) {
            fetch(`/force_delete_task/${task_id}`, {
                method: 'DELETE',
            })
            .then(data => {
                if (data) {
                    // Remove the task row from the table
                    const row = document.querySelector(`.task-table tbody tr[data-task-id="${task_id}"]`);
                    if (row) {
                        row.remove();
                    }
                    // alert('Task force deleted successfully'); // Удаляем алерт
                }
            })
            .catch(error => {
                console.error('Error force deleting task:', error);
                // alert('Error force deleting task'); // Удаляем алерт
            });
        }

        setInterval(updateTaskStatus, 30000); // Update every 30 seconds
    </script>
</head>
<body>
    {% include '_back_button.html' %}
    <div class="task-container">
        <div class="task-header">Статусы проценок по парсеру</div>
        <table class="task-table">
            <thead>
                <tr>
                    <th>Task UID</th>
                    <th>Имя входного файла</th>
                    <th>Платформы для проценки</th>
                    <th>Статус</th>
                    <th>Прогресс</th>
                    <th>Результат</th>
                    <th>Дата запуска</th>
                    <th>Дата завершения</th>
                    <th>Время выполнения</th>
                    <th>Действия</th>
                </tr>
            </thead>
            <tbody>
                {% for task_id, task in tasks_info.items() %}
                    <tr data-task-id="{{ task_id }}">
                        <td>{{ task_id }}</td>
                        <td class="task-detail">{{ task['filename'] or 'N/A' }}</td>
                        <td class="task-detail">{{ task['platforms'] | join(', ') }}</td>
                        <td class="task-status {{ task['state'] | lower }}">{{ task['status'] or 'N/A' }}</td>
                        <td>{{ task['progress'] or 'N/A' }}</td>
                        <td>
                            {% if task['url'] %}
                                {% set result_url = task['url'].strip("'") %}
                                {% if result_url.startswith('http://') or result_url.startswith('https://') %}
                                    <a href="{{ result_url }}" class="download-button" target="_blank">Скачать</a>
                                {% else %}
                                    {{ result_url }}
                                {% endif %}
                            {% else %}
                                Нет результата
                            {% endif %}
                        </td>
                        <td>{{ task['received'] or 'N/A' }}</td>
                        <td>{{ task['succeeded'] or 'N/A' }}</td>
                        <td>{{ task['runtime'] or 'N/A' }}</td>
                        <td>
                            <select class="action-dropdown" onchange="handleAction(this, '{{ task_id }}')">
                                <option value="">Действие</option>
                                {% if task['status'] in ['PROGRESS', 'STARTED'] %}
                                    <option value="force_delete" style="color: red;">Удалить</option>
                                    <option value="stop" style="color: orange;">Остановить</option>
                                    <option value="restart" style="color: green;">Перезапустить</option>
                                {% else %}
                                    <option value="delete" style="color: red;">Удалить</option>
                                    <option value="restart" style="color: green;">Перезапустить</option>
                                {% endif %}
                            </select>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>
