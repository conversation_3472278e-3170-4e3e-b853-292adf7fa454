<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demand Quantity Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f4f8;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0094ff;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input[type="file"] {
            display: block;
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group button {
            background-color: #0094ff;
            color: #fff;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .form-group button:hover {
            background-color: #007bb5;
        }
        .alert {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            color: #fff;
            font-weight: bold;
        }
        .alert.error {
            background-color: #ff4d4d;
        }
        .alert.success {
            background-color: #4caf50;
        }
        .instructions {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #0094ff;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-left: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Upload Excel File for Demand Quantity</h1>
        <div class="instructions">
            <p><strong>Instructions:</strong></p>
            <p>The uploaded file must be in .xls, .xlsx, or .csv format. The file should have the following columns as the first two columns: Brand and Article.</p>
        </div>
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Select File:</label>
                <input type="file" id="file" name="file" required>
            </div>
            <div class="form-group">
                <button type="submit" id="uploadButton">Upload and Process</button>
                <div id="loader" class="loader" style="display: none;"></div> <!-- Лоадер, скрыт по умолчанию -->
            </div>
            <div id="alertContainer"></div>
        </form>
    </div>
    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            const uploadButton = document.getElementById('uploadButton');
            const loader = document.getElementById('loader');
            const alertContainer = document.getElementById('alertContainer');

            // Показываем лоадер и отключаем кнопку отправки
            uploadButton.disabled = true;
            loader.style.display = 'inline-block';

            fetch('/demand_quantity', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.json(); // Предполагаем, что сервер возвращает {url: 'ссылка на файл'}
                } else {
                    return response.json().then(data => {
                        throw new Error(data.error || 'An error occurred');
                    });
                }
            })
            .then(data => {
                if (data.url) {
                    // Создаем кнопку для скачивания файла
                    const downloadButton = document.createElement('button');
                    downloadButton.textContent = 'Скачать файл';
                    downloadButton.addEventListener('click', () => {
                        const a = document.createElement('a');
                        a.href = data.url;
                    });

                    // Очищаем контейнер и добавляем кнопку
                    alertContainer.innerHTML = ''; // Очищаем контейнер
                    alertContainer.appendChild(downloadButton);
                } else {
                    throw new Error('URL не предоставлен');
                }
            })
            .catch(error => {
                alertContainer.innerHTML = `<div class="alert error">${error.message}</div>`;
            })
            .finally(() => {
                // Скрываем лоадер и возвращаем возможность отправки
                uploadButton.disabled = false;
                loader.style.display = 'none';
            });
        });
    </script>
</body>
</html>
