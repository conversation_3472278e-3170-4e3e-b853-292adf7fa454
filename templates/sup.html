<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <title>Управление поставщиками</title>
  <!-- Подключаем Vue.js через CDN -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
  <!-- Подключаем Font Awesome для иконок -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-..." crossorigin="anonymous" referrerpolicy="no-referrer" />
  <style>
    /* Общие стили страницы */
    body {
      margin: 0;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f0f8ff;
      color: #333;
    }
    h1 {
      color: #1565c0;
      text-align: center;
      margin-bottom: 30px;
    }
    /* Форма добавления поставщика */
    .supplier-form {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
    }
    .supplier-form input[type="text"] {
      padding: 8px 12px;
      font-size: 16px;
      border: 1px solid #ccc;
      border-radius: 4px;
      width: 300px;
      margin-right: 10px;
    }
    .supplier-form button {
      padding: 8px 16px;
      font-size: 16px;
      color: #fff;
      background-color: #1565c0;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    .supplier-form button:hover {
      background-color: #0d47a1;
    }
    /* Таблица поставщиков */
    .supplier-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background-color: #fff;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .supplier-table th, 
    .supplier-table td {
      padding: 12px 15px;
      border: 1px solid #e0e0e0;
      text-align: left;
    }
    .supplier-table th {
      background-color: #2196f3;
      color: #fff;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    .supplier-table tbody tr:nth-child(even) {
      background-color: #f7f9fc;
    }
    .action-buttons {
      display: flex;
      gap: 10px;
      font-size: 18px;
    }
    .action-buttons i {
      cursor: pointer;
      color: #1565c0;
    }
    .action-buttons i:hover {
      color: #0d47a1;
    }
    /* Режим редактирования строки */
    .edit-input {
      padding: 6px 10px;
      font-size: 16px;
      border: 1px solid #ccc;
      border-radius: 4px;
      width: 100%;
    }
    .edit-actions {
      display: flex;
      gap: 10px;
    }
    .edit-actions i {
      cursor: pointer;
      font-size: 18px;
      color: #1565c0;
    }
    .edit-actions i:hover {
      color: #0d47a1;
    }
    /* Контекстное меню для удаления */
    .context-menu {
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      border-radius: 4px;
      z-index: 300;
      min-width: 150px;
    }
    .context-menu ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .context-menu li {
      padding: 8px 12px;
      cursor: pointer;
    }
    .context-menu li:hover {
      background-color: #f0f0f0;
    }
  </style>
</head>
<body>
  {% raw %}
  <div id="app">
    <h1>Управление поставщиками</h1>
    <!-- Форма добавления поставщика -->
    <div class="supplier-form">
      <input type="text" v-model="newSupplierName" placeholder="Введите название поставщика">
      <button @click="addSupplier">Добавить поставщика</button>
    </div>
    <!-- Таблица поставщиков -->
    <table class="supplier-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Название поставщика</th>
          <th>Действия</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(supplier, index) in suppliers" :key="supplier.id" @contextmenu.prevent="openContextMenu(supplier.id, $event)">
          <template v-if="!supplier.editing">
            <td>{{ supplier.id }}</td>
            <td>{{ supplier.name }}</td>
            <td class="action-buttons">
              <i class="fas fa-edit" title="Редактировать" @click="editSupplier(supplier)"></i>
              <i class="fas fa-trash-alt" title="Удалить" @click="deleteSupplier(supplier.id)"></i>
            </td>
          </template>
          <template v-else>
            <td>{{ supplier.id }}</td>
            <td>
              <input type="text" v-model="supplier.editName" class="edit-input">
            </td>
            <td class="edit-actions">
              <i class="fas fa-check" title="Сохранить" @click="saveSupplier(supplier)"></i>
              <i class="fas fa-times" title="Отменить" @click="cancelEdit(supplier)"></i>
            </td>
          </template>
        </tr>
      </tbody>
    </table>

    <!-- Контекстное меню для удаления поставщика -->
    <div v-if="contextMenuVisible" class="context-menu" :style="{ top: contextMenuY + 'px', left: contextMenuX + 'px' }">
      <ul>
        <li @click="confirmDelete(contextMenuSupplierId)">Удалить поставщика</li>
      </ul>
    </div>
  </div>
  {% endraw %}

  <script>
    new Vue({
      el: '#app',
      data: {
        newSupplierName: "",
        suppliers: [],
        contextMenuVisible: false,
        contextMenuX: 0,
        contextMenuY: 0,
        contextMenuSupplierId: null
      },
      created() {
        this.fetchSuppliers();
      },
      methods: {
        // Получаем список поставщиков с API
        fetchSuppliers() {
          fetch('http://**************:8007/api/suppliers')
            .then(response => {
              if (!response.ok) {
                throw new Error("Ошибка при получении списка поставщиков");
              }
              return response.json();
            })
            .then(data => {
              // Ожидается, что data – массив объектов поставщиков [{id:"1", name:"..."}, ...]
              // Приводим id к числу, если нужно
              this.suppliers = data.map(supplier => ({
                id: Number(supplier.id),
                name: supplier.name,
                editing: false,
                editName: ""
              }));
            })
            .catch(err => {
              console.error(err);
              alert(err.message);
            });
        },
        // Добавляем нового поставщика (POST)
        addSupplier() {
          const name = this.newSupplierName.trim();
          if (name === "") {
            alert("Введите название поставщика.");
            return;
          }
          fetch('http://**************:8007/api/suppliers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name })
          })
          .then(response => {
            if (!response.ok) {
              throw new Error("Ошибка при добавлении поставщика");
            }
            return response.json();
          })
          .then(newSupplier => {
            // Предполагается, что сервер возвращает добавленного поставщика с id
            this.suppliers.push({
              id: Number(newSupplier.id),
              name: newSupplier.name,
              editing: false,
              editName: ""
            });
            this.newSupplierName = "";
          })
          .catch(err => {
            console.error(err);
            alert(err.message);
          });
        },
        // Переход в режим редактирования поставщика
        editSupplier(supplier) {
          supplier.editing = true;
          supplier.editName = supplier.name;
        },
        // Сохранение изменений (PUT)
        saveSupplier(supplier) {
          const newName = supplier.editName.trim();
          if (newName === "") {
            alert("Название не может быть пустым.");
            return;
          }
          fetch(`http://**************:8007/api/suppliers/${supplier.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: newName })
          })
          .then(response => {
            if (!response.ok) {
              throw new Error("Ошибка при сохранении изменений");
            }
            return response.json();
          })
          .then(updatedSupplier => {
            supplier.name = updatedSupplier.name;
            supplier.editing = false;
          })
          .catch(err => {
            console.error(err);
            alert(err.message);
          });
        },
        // Отмена редактирования
        cancelEdit(supplier) {
          supplier.editing = false;
          supplier.editName = "";
        },
        // Удаление поставщика (DELETE) после подтверждения в контекстном меню
        deleteSupplier(id) {
          if (confirm("Вы действительно хотите удалить поставщика?")) {
            fetch(`http://**************:8007/api/suppliers/${id}`, {
              method: 'DELETE'
            })
            .then(response => {
              if (!response.ok) {
                throw new Error("Ошибка при удалении поставщика");
              }
              // Удаляем поставщика из локального списка
              this.suppliers = this.suppliers.filter(supplier => supplier.id !== id);
            })
            .catch(err => {
              console.error(err);
              alert(err.message);
            });
          }
        },
        // Открываем контекстное меню для удаления по правой кнопке
        openContextMenu(supplierId, event) {
          event.preventDefault();
          event.stopPropagation();
          this.contextMenuSupplierId = supplierId;
          this.contextMenuX = event.clientX;
          this.contextMenuY = event.clientY;
          this.contextMenuVisible = true;
        },
        // Подтверждаем удаление поставщика через контекстное меню
        confirmDelete(supplierId) {
          this.deleteSupplier(supplierId);
          this.contextMenuVisible = false;
          this.contextMenuSupplierId = null;
        },
        // Глобальный обработчик кликов, чтобы скрывать контекстное меню
        documentClick() {
          if (this.contextMenuVisible) {
            this.contextMenuVisible = false;
            this.contextMenuSupplierId = null;
          }
        }
      }
    });
  </script>
</body>
</html>