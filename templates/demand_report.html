<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Формирование потребности</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">


    <style>
        abbr {
            text-underline-offset: 0.2em;
        }
        .quick-date {
            text-decoration: underline;
            cursor: pointer;
            margin-right: 10px;
        }
        input:user-invalid {
          border: 1px solid red;
        }

    </style>
</head>
<body class="light">
{% include '_back_button.html' %}
<div class="container mt-5">
    <div class="card light">
        <div class="card-header light">
            <h2>Формирование потребности</h2>
        </div>
        <div class="card-body">
            <form id="demandReportForm" enctype="multipart/form-data" onsubmit="return false;">
                <div class="mb-3">
                    <label for="brandsFile" class="form-label">Файл со списком брендов <span class="text-danger">*</span></label>
                    <input class="form-control" type="file" id="brandsFile" name="brands_file" accept=".txt,.csv,.xlsx" required>
                    <div class="form-text">Загрузите файл .txt, .csv или .xlsx. В колонке должны быть бренды (один бренд на строку).</div>
                </div>
                <div class="mb-3">
                    <label for="kFilter" class="form-label">
                        <abbr title="Показывает, как часто товар продается в месяц, когда он есть в наличии">Коэффициент регулярности продаж</abbr> (k_)
                    </label>
                    <input class="form-control" type="number" id="kFilter" value="1.0" min="0" step="0.01">
                    <div class="form-text">Только позиции с k_ &ge; этому значению попадут в отчет</div>
                </div>
                <div class="mb-3 row">
                    <div class="col-md-6">
                        <label for="startDate" class="form-label">Дата начала</label>
                        <input type="date" class="form-control" id="startDate">
                        <div class="form-text mt-1">
                            Быстрый выбор:
                            <span class="quick-date text-muted" id="quickCurrentYear"></span>
                            <span class="quick-date text-muted" id="quick3months">3 мес. назад</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="endDate" class="form-label">Дата окончания</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                </div>
                <a class="btn btn-outline-primary mt-3" id="openSuppliersOffcanvasBtn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-gear-fill" viewBox="0 0 16 16">
  <path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/>
                </svg>
                    Настройки рейтинга поставщиков
                </a>
                <div class="mt-2 text-secondary">Срок доставки: от 24 до 96 часов</div>
            </form>
            <div id="ratingResult" class="mt-4"></div>
            <!-- Offcanvas for Supplier Rating Settings and MINRATE -->
            <div class="offcanvas offcanvas-end w-25" data-bs-backdrop="static" tabindex="-1" id="offcanvasSuppliers" aria-labelledby="offcanvasSuppliersLabel">
                <div class="offcanvas-header">
                    <h5 class="offcanvas-title" id="offcanvasSuppliersLabel">Настройки рейтинга поставщиков</h5>
                    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Закрыть"></button>
                </div>
                <div class="offcanvas-body" x-data="{ minRate: 90 }">
                    <div class="mb-3" x-effect="updateSupplierCheckboxes(minRate)">
                        <label for="minRate" class="form-label">
                            Минимальный
                            <abbr title="Доля записей, в которых не сработал ни один флаг, от общего числа записей по данному поставщику. Чем выше рейтинг, тем более надежным считается поставщик">рейтинг</abbr>
                            (MINRATE)
                        </label>
                        <input type="number" class="form-control" id="minRate" x-model="minRate" min="0" max="100" step="0.1">
                    </div>
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-success mt-3" id="generateBtn" type="submit" form="demandReportForm" disabled>Сформировать отчет</button>
                    </div>
                    <h6 class="mt-4">Таблица поставщиков <span id="selectedSuppliersCount"></span></h6>
                    <table class="table table-bordered table-striped mt-2" id="suppliersTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 70%;">Поставщик</th>
                                <th style="width: 17%;">Рейтинг</th>
                                <th style="width: 13%;">Вкл.</th>
                            </tr>
                        </thead>
                        <tbody id="suppliersTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    // Set default dates: end_date = today, start_date = 1 year ago
    window.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        document.getElementById('endDate').valueAsDate = today;
        document.getElementById('startDate').valueAsDate = oneYearAgo;

        // Quick date selectors
        const currentYear = today.getFullYear();
        const quickCurrentYear = document.getElementById('quickCurrentYear');
        quickCurrentYear.textContent = currentYear;
        quickCurrentYear.addEventListener('click', function() {
            document.getElementById('startDate').value = `${currentYear}-01-01`;
        });
        document.getElementById('quick3months').addEventListener('click', function() {
            const d = new Date();
            d.setMonth(d.getMonth() - 3);
            // Pad month and day for correct date format
            const m = (d.getMonth() + 1).toString().padStart(2, '0');
            const day = d.getDate().toString().padStart(2, '0');
            document.getElementById('startDate').value = `${d.getFullYear()}-${m}-${day}`;
        });
    });

    // Function to format file size
    function formatBytes(bytes, decimals = 1) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    // Function to update the count of selected suppliers
    function updateSelectedCount() {
        const tbody = document.getElementById('suppliersTableBody');
        const selectedCheckboxes = tbody.querySelectorAll('input[type="checkbox"]:checked');
        const count = selectedCheckboxes.length;
        const countSpan = document.getElementById('selectedSuppliersCount');
        if (countSpan) {
            countSpan.textContent = count > 0 ? `(${count} выбрано)` : ''; // Display count only if greater than 0
        }
    }

    // Function to populate the supplier table
    function populateSuppliersTable(suppliers) {
        const tbody = document.getElementById('suppliersTableBody');
        tbody.innerHTML = ''; // Clear existing rows

        // Find the generate button
        const generateBtn = document.getElementById('generateBtn');

        if (!suppliers || suppliers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">Нет данных</td></tr>';
            // Keep the button disabled if no suppliers are found
            if (generateBtn) {
                generateBtn.disabled = true;
            }
            updateSelectedCount(); // Update count display even if empty
            return;
        }

        suppliers.forEach(sup => {
            const tr = document.createElement('tr');

            const tdName = document.createElement('td');
            tdName.textContent = sup.name;

            const tdRating = document.createElement('td');
            tdRating.textContent = sup.sup_rating;

            const tdCheck = document.createElement('td');
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input';

            // Store supid and rating on the checkbox for easier access in updateSupplierCheckboxes
            checkbox.dataset.supid = sup.supid;
            checkbox.dataset.rating = sup.sup_rating;

            // Add event listener to update the count when the checkbox changes
            checkbox.addEventListener('change', updateSelectedCount);

            tdCheck.appendChild(checkbox);

            // Make the whole cell clickable to toggle the checkbox (except when clicking the checkbox itself)
            tdCheck.addEventListener('click', function(e) {
                if (e.target !== checkbox) {
                    checkbox.checked = !checkbox.checked;
                    // Manually trigger change event for count update if toggled via cell click
                    const event = new Event('change');
                    checkbox.dispatchEvent(event);
                }
            });

            tr.appendChild(tdName);
            tr.appendChild(tdRating);
            tr.appendChild(tdCheck);

            tbody.appendChild(tr);
        });

        // Enable the generate button after successfully populating the table
        if (generateBtn) {
            generateBtn.disabled = false;
        }
        updateSelectedCount(); // Update count display after populating table
    }

    // Update checkboxes based on MINRATE
    function updateSupplierCheckboxes(minRateValue) {
        const tbody = document.getElementById('suppliersTableBody');
        const rows = tbody.querySelectorAll('tr');

        rows.forEach(row => {
            // Get the rating from the data attribute stored on the checkbox
            const checkbox = row.querySelector('input[type="checkbox"]');
            if (checkbox && checkbox.dataset.rating) {
                const rating = parseFloat(checkbox.dataset.rating);
                // Checkbox is checked if rating is >= MINRATE
                checkbox.checked = rating >= minRateValue;
            }
        });
        updateSelectedCount(); // Update count display after checkboxes are updated by MINRATE
    }

    function generatePlaceholderRows(count) {
        let html = '';
        // random numbers between 6 and 12 (inclusive), e.g. [6, 7, 8, 9, 10, 11, 12]
        const col1Widths = Array.from({ length: 13 }, () => Math.floor(Math.random() * (12 - 6 + 1)) + 6); // Corrected range
        console.log("col1Widths:", col1Widths);
        const col2Width = 5;
        const col3Width = 4;

        for (let i = 0; i < count; i++) {
            const col1 = col1Widths[i % col1Widths.length]; // Cycle through first column widths
            html += `
                <tr class="placeholder-wave">
                    <td><span class="placeholder col-${col1}"></span></td>
                    <td><span class="placeholder col-${col2Width}"></span></td>
                    <td><span class="placeholder col-${col3Width}"></span></td>
                </tr>
            `;
        }
        return html;
    }

    function validateInputs() {
        const brandsFileInput = document.getElementById('brandsFile');
        const kFilterInput = document.getElementById('kFilter');
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const statusArea = document.getElementById('ratingResult');

        // Clear previous validation/error messages unless it's a report success message
        if (!statusArea.querySelector('.alert-success')) {
            statusArea.innerHTML = '';
        }

        if (!brandsFileInput.files || brandsFileInput.files.length === 0) {
            statusArea.innerHTML = '<div class="alert alert-warning">Выберите файл со списком брендов (.txt, .csv, .xlsx).</div>';
            return false;
        }

        if (kFilterInput.value === '' || kFilterInput.value === null) {
             statusArea.innerHTML = '<div class="alert alert-warning">Введите значение для параметра k_.</div>';
             return false;
        }
        // Check if the value is a valid number after trimming
        const kFilterValue = parseFloat(kFilterInput.value);
        if (isNaN(kFilterValue) || kFilterValue < 0) {
            statusArea.innerHTML = '<div class="alert alert-warning">Введите корректное числовое значение для параметра k_ (неотрицательное).</div>';
            return false;
        }


        if (!startDate || !endDate) {
            statusArea.innerHTML = '<div class="alert alert-warning">Выберите обе даты.</div>';
            return false;
        }
        if (startDate > endDate) {
            statusArea.innerHTML = '<div class="alert alert-warning">Дата начала не может быть позже даты окончания.</div>';
            return false;
        }

        return true; // Validation passed
    }

    // Event listener for the "Настройки рейтинга поставщиков" button (Trigger fetch on mousedown)
    // We use 'mousedown' instead of 'click' to ensure validation runs before the offcanvas is potentially opened.
    document.getElementById('openSuppliersOffcanvasBtn').addEventListener('mousedown', async function() {
        const suppliersTableBody = document.getElementById('suppliersTableBody');
        const brandsFileInput = document.getElementById('brandsFile');
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const statusArea = document.getElementById('ratingResult');


        // Run validation before attempting to open offcanvas or fetch data
        if (!validateInputs()) {
            // Validation failed, validateInputs already displayed the error
            return;
        }

        // Manually trigger the offcanvas *after* validation passes.
        const offcanvasElement = document.getElementById('offcanvasSuppliers');
        const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        bsOffcanvas.show();

        // Placeholder table
        suppliersTableBody.innerHTML = `
            <tr>
                <td colspan="3" class="text-center text-muted">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    Ищем всех поставщиков этих брендов и расчитываем их рейтинг. Ожидайте...
                </td>
            </tr>
        ` + generatePlaceholderRows(15); // Generate placeholder rows dynamically

        try {
            const formData = new FormData();
            formData.append('brands_file', brandsFileInput.files[0]);
            formData.append('start_date', startDate);
            formData.append('end_date', endDate);

            const resp = await fetch('/api/demand_report/suppliers_rating', {
                method: 'POST',
                body: formData
            });

            const data = await resp.json();

            // Always clear the loading spinner from the table body after fetch response
            suppliersTableBody.innerHTML = '';

            if (!data.error && data.suppliers) {
                populateSuppliersTable(data.suppliers);

                // Get the current MINRATE value from the input and update checkboxes
                const minRateInput = document.getElementById('minRate');
                const currentMinRate = minRateInput ? parseFloat(minRateInput.value) : 0;
                updateSupplierCheckboxes(currentMinRate);

            } else {
                // Display error message OUTSIDE the offcanvas in the status area
                statusArea.innerHTML = `<div class='alert alert-danger'>Ошибка загрузки поставщиков: ${data.error || 'Не удалось рассчитать рейтинг поставщиков.'}</div>`;
            }
        } catch (e) {
             // Always clear the loading spinner from the table body on fetch error
            suppliersTableBody.innerHTML = '';
            // Display fetch error OUTSIDE the offcanvas in the status area
            statusArea.innerHTML = `<div class='alert alert-danger'>Ошибка при запросе поставщиков: ${e.message}</div>`;
        }
    });

    // Report generation submit handler: Collects selected suppliers and submits the form
    document.getElementById('demandReportForm').addEventListener('submit', async function(event) {
       event.preventDefault(); // Prevent default form submission

       // Run validation before submitting the form
       if (!validateInputs()) {
            return;
       }

       const kFilter = parseFloat(document.getElementById('kFilter').value);
       const brandsFileInput = document.getElementById('brandsFile');
       const minRateInput = document.getElementById('minRate'); // Get the MINRATE input
       const currentMinRate = minRateInput ? parseFloat(minRateInput.value) : 'N/A'; // Get current MINRATE value

       const statusArea = document.getElementById('ratingResult'); // Re-get statusArea as it might have been cleared by validateInputs
       statusArea.innerHTML = ''; // Clear messages before showing spinner

       const generateBtn = document.getElementById('generateBtn');
       const openSuppliersBtn = document.getElementById('openSuppliersOffcanvasBtn'); // Get the offcanvas button

       generateBtn.disabled = true;
       openSuppliersBtn.classList.add('disabled');

       // Close the offcanvas when report generation starts
       const offcanvasElement = document.getElementById('offcanvasSuppliers');
       const bsOffcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
       if (bsOffcanvas) {
           bsOffcanvas.hide();
       }

       // Update loading message with alert and spinner, including MINRATE
       statusArea.innerHTML = `
           <div class="alert alert-info d-flex align-items-center" role="alert">
               <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
               Формируем отчет (MINRATE=${currentMinRate}). Ожидайте...
           </div>
       `;

       try {
           // Collect selected supplier IDs from checked checkboxes
           const selectedSupids = Array.from(document.querySelectorAll('#suppliersTableBody input[type="checkbox"]:checked'))
                                    .map(checkbox => checkbox.dataset.supid)
                                    .filter(supid => supid !== undefined);

           console.log("Collected Selected Suppliers for Report:", selectedSupids); // Log for verification

           const formData = new FormData();
           formData.append('brands_file', brandsFileInput.files[0]);
           formData.append('k_filter', kFilter);
           // Get dates again as the function needs them, or pass them as arguments to validateInputs
           formData.append('start_date', document.getElementById('startDate').value);
           formData.append('end_date', document.getElementById('endDate').value);
           // Append selected supplier IDs as a comma-separated string for the backend
           formData.append('selected_supids', selectedSupids.join(','));


           // The report generation endpoint is '/api/demand_report/generate'
           const resp = await fetch('/api/demand_report/generate', {
               method: 'POST',
               body: formData
           });
           const data = await resp.json();
            if (!data.error && data.url) {
               // Use the new data to display a more detailed success message
               const formattedFileSize = formatBytes(data.file_size);
               statusArea.innerHTML = `
                    <div class='alert alert-success'>
                        <strong>Отчет готов:</strong> <a href='${data.url}'>Скачать файл</a>
                        <br><strong>Тип:</strong> ${data.file_type}
                        <br><strong>Кол-во строк:</strong> ${data.num_rows}
                        <br><strong>Размер:</strong> ${formattedFileSize}
                    </div>
               `;
           } else {
               statusArea.innerHTML = `<div class='alert alert-danger'>Ошибка: ${data.error || 'Не удалось сформировать отчет.'}</div>`;
           }
       } catch (e) {
            statusArea.innerHTML = `<div class='alert alert-danger'>Ошибка: ${e.message}</div>`;
       } finally {
           document.getElementById('generateBtn').disabled = false;
           document.getElementById('openSuppliersOffcanvasBtn').classList.remove('disabled');
       }
   });
</script>
</body>
</html>
