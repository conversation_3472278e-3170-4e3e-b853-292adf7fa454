<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Редактирование задачи</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        .loader {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            margin-top: 20px;
            display: none;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }

        h2 {
            text-align: center;
            color: #444;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .select2-container--default .select2-selection--multiple {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 4px;
        }

        .form-group button {
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .form-group button:hover {
            background-color: #0056b3;
        }

        .return-button {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
        }

        .return-button:hover {
            background-color: #0056b3;
        }

        .error-message {
            color: red;
            font-size: 16px;
            margin-top: 10px;
        }

        @media (max-width: 600px) {
            .loader {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="{{ url_for('percentage.schedulle_tasks_cross_doc') }}" class="return-button">Назад к задачам</a>
        <h2>Редактирование задачи</h2>
        <form id="editTaskForm">
            <div class="form-group">
                <label for="file">Файл</label>
                <p>Текущий файл: <a href="{{ task_info.file_url }}">{{ task_info.settings.filename }}</a></p>
                <input type="file" id="file" name="file">
            </div>

            <div class="form-group">
                <label for="groupType">Выберите группу:</label>
                <select name="groupType" id="groupType">
                    {% for sup in sups %}
                        <option value="{{ sup }}" {% if sup in task_info.settings.platforms %}selected{% endif %}>{{ sup }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <label for="schedulleType">Тип расписания</label>
                <select id="schedulleType" name="schedulleType" multiple style="width: 100%">
                    <option value="everyday" {% if 'everyday' in task_info.type %}selected{% endif %}>Ежедневный</option>
                    <option value="everyweek|0" {% if 'everyweek|0' in task_info.type %}selected{% endif %}>Еженедельный - по понедельникам</option>
                    <option value="everyweek|1" {% if 'everyweek|1' in task_info.type %}selected{% endif %}>Еженедельный - по вторникам</option>
                    <option value="everyweek|2" {% if 'everyweek|2' in task_info.type %}selected{% endif %}>Еженедельный - по средам</option>
                    <option value="everyweek|3" {% if 'everyweek|3' in task_info.type %}selected{% endif %}>Еженедельный - по четвергам</option>
                    <option value="everyweek|4" {% if 'everyweek|4' in task_info.type %}selected{% endif %}>Еженедельный - по пятницам</option>
                    <option value="everyweek|5" {% if 'everyweek|5' in task_info.type %}selected{% endif %}>Еженедельный - по субботам</option>
                    <option value="everyweek|6" {% if 'everyweek|6' in task_info.type %}selected{% endif %}>Еженедельный - по воскресеньям</option>
                </select>
            </div>
            <div id="errorMessages" class="error-message"></div>
            <div class="form-group">
                <button type="submit">Сохранить изменения</button>
            </div>
        </form>
        <div id="responseMessage">Идёт парсинг и обработка...</div>
        <div class="loader"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
    <script>
        $(document).ready(function () {
            // Инициализация Select2 для выбора платформ
            $('#groupType').select2({
                placeholder: "Выберите платформу",
                allowClear: true
            });

            // Инициализация Select2 для расписания
            $('#schedulleType').select2({
                placeholder: "Выберите тип расписания",
                allowClear: true
            });

            document.getElementById('editTaskForm').addEventListener('submit', function(event) {
                event.preventDefault();

                const schedulleType = $('#schedulleType').val(); // Получаем массив значений
                const platforms = $('#groupType').val();
                const errorMessages = document.getElementById('errorMessages');
                errorMessages.innerHTML = '';

                const formData = new FormData();
                formData.append('platforms', JSON.stringify(platforms));
                formData.append('schedule_type', JSON.stringify(schedulleType));
                const fileInput = document.getElementById('file');
                if (fileInput.files[0]) {
                    formData.append('file', fileInput.files[0]);
                }
                const file = fileInput.files[0];
                let fileExtension = '';
                if (file) {
                    fileExtension = file.name.split('.').pop().toLowerCase();
                    if (!['xlsx', 'csv'].includes(fileExtension)) {
                        errorMessages.innerHTML = 'Пожалуйста, выберите файл в формате .xlsx или .csv.';
                    }
                }

                const validateAndSend = () => {
                    showLoading(true);

                    fetch('/update_percentage_parser/{{ task_info.id }}', { // Измененный URL
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        showLoading(false);
                        if (!data.error) {
                            window.location.href = '/schedulle_tasks_cross_doc';
                        } else {
                            errorMessages.innerHTML = data.error;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showLoading(false);
                        errorMessages.innerHTML = data.error;
                    });
                };

                if (fileExtension === 'xlsx') {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheet = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheet];
                        const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0];
                        const expectedHeaders = ['Бренд', 'Артикул'];

                        if (JSON.stringify(headers) !== JSON.stringify(expectedHeaders)) {
                            errorMessages.innerHTML = 'Файл должен содержать шапку: 1 - Бренд, 2 - Артикул';
                        } else {
                            validateAndSend();
                        }
                    };
                    reader.readAsArrayBuffer(file);
                } else if (fileExtension === 'csv') {
                    Papa.parse(file, {
                        header: false,
                        complete: function(results) {
                            const headers = results.data[0];
                            const expectedHeaders = ['Бренд', 'Артикул'];

                            if (JSON.stringify(headers) !== JSON.stringify(expectedHeaders)) {
                                errorMessages.innerHTML = 'Файл должен содержать шапку: 1 - Бренд, 2 - Артикул';
                            } else {
                                validateAndSend();
                            }
                        }
                    });
                } else {
                    validateAndSend();
                }
            });

            function showLoading(isLoading) {
                const loader = document.querySelector('.loader');
                const responseMessage = document.getElementById('responseMessage');

                if (isLoading) {
                    loader.style.display = 'block';
                    responseMessage.style.display = 'none';
                    document.body.style.overflowY = 'hidden';
                } else {
                    loader.style.display = 'none';
                    responseMessage.style.display = 'block';
                    document.body.style.overflowY = 'auto';
                }
            }
        });
    </script>
</body>
</html>
