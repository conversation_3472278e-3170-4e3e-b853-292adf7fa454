<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <title>Загрузка файла и построение таблицы</title>
  <!-- Vue через CDN -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
  <style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        padding: 20px;
        background: #f9f9f9;
    }
    /* Стили для выпадающего списка выбора опции */
    .option-select {
        margin-bottom: 20px;
    }
    /* Стили для селекта валюты */
    .currency-select {
        margin-bottom: 10px;
    }
    /* Стили для блока с галочкой и инпутом для ручного ввода */
    .manual-brand {
        margin-bottom: 20px;
    }
    .manual-brand input[type="text"] {
        margin-top: 5px;
        padding: 5px;
        font-size: 14px;
        width: 250px;
    }
    /* Блок для таблицы с фиксированными размерами и прокруткой */
    .table-container {
        max-width: 100%;
        max-height: 500px;
        border: 1px solid #d1d1d1;
        border-radius: 4px;
        background: #fff;
        overflow: auto;
        margin-top: 20px;
    }
    table {
        border-collapse: collapse;
        width: 100%;
        min-width: 600px;
    }
    
    th, td {
        padding: 8px 12px;
        border: 1px solid #d1d1d1;
        text-align: left;
    }
    th {
        background-color: #f3f3f3;
    }
    /* Фиксированный заголовок таблицы */
    thead th {
        position: sticky;
        top: 0;
        background-color: #f3f3f3;
        z-index: 1;
        cursor: pointer; /* указатель мыши при наведении */
    }
    tr:nth-child(even) {
        background-color: #fcfcfc;
    }
    /* Примеры стилей для классов столбцов */
    .index {
        background-color: #808080;
    }
    .price {
        background-color: #00FF00;
    }
    .quantity {
        background-color: #20B2AA;
    }
    .brand {
        background-color: #FFB6C1;
    }
    .article {
        background-color: #FFA07A;
    }
    .alternative_article {
        background-color: #FF7F50;
    }
    .description {
        background-color: #FFD700;
    }
    .amount {
        background-color: #006400;
    }
    .weight {
        background-color: #FF00FF;
    }
    .unknown {
        background-color: #FAFAFA;
    }
    /* Значок требуемого столбца */
    .required-badge {
        position: absolute;
        top: 2px;
        right: 2px;
        color: #d32f2f;
        font-weight: bold;
        font-size: 16px;
    }
    /* Стили для всплывающего окна выбора класса */
    .dropdown-popup {
        position: absolute;
        background: #fff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        z-index: 100;
        min-width: 200px;
    }
    .dropdown-popup ul {
        list-style: none;
        margin: 0;
        padding: 0;
    }
    .dropdown-popup li {
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }
    .dropdown-popup li:hover {
        background-color: #f0f0f0;
    }
    /* Стили для swatch (цветного квадратика) в списке и легенде */
    .legend-swatch {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border: 1px solid #d1d1d1;
        border-radius: 3px;
    }
    /* Стили для легенды */
    .legend {
        margin-top: 20px;
        padding: 10px;
        border: 1px solid #d1d1d1;
        border-radius: 4px;
        background: #fff;
        max-width: 100%;
    }
    .legend h3 {
        margin-top: 0;
    }
    .legend ul {
        list-style: none;
        padding: 0;
    }
    .legend li {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    .legend li:last-child {
        margin-bottom: 0;
    }
    input[type="file"] {
      margin-bottom: 20px;
    }
    /* Стили для кнопки отправки */
    .send-button {
        margin-top: 20px;
    }
     /* Стили для сообщения об ошибке */
    .error-message {
        color: red;
        font-weight: bold;
        margin-top: 10px;
    }
    .success-message {
        color: green;
        font-weight: bold;
        margin-top: 10px;
    }
    /* Стили для модального окна */
    .modal-overlay {
        position: fixed;
        top: 0; left: 0;
        width: 100%; height: 100%;
        background: rgba(0,0,0,0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 200;
    }
    .modal {
        background: #fff;
        padding: 20px;
        border-radius: 4px;
        max-width: 400px;
        width: 100%;
        text-align: center;
    }
    .modal button {
        margin: 10px;
    }
    /* Стили для контекстного меню для удаления строки */
    .context-menu {
        position: absolute;
        background: #fff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        border-radius: 4px;
        z-index: 300;
        min-width: 150px;
    }
    .context-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .context-menu li {
        padding: 8px 12px;
        cursor: pointer;
    }
    .context-menu li:hover {
        background-color: #f0f0f0;
    }

    /* Блок для формы загрузки файла и дополнительных опций */
    .controls {
        margin-bottom: 20px;
    }
    .controls input[type="file"],
    .controls select,
    .controls input[type="checkbox"],
    .controls input[type="text"],
    .controls button {
        margin: 5px 0;
    }

    /* Спиннер загрузки */
    .spinner-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 500;
    }
    .spinner {
        border: 8px solid #f3f3f3;
        border-top: 8px solid #1565c0;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 1s linear infinite;
        margin-right: 15px;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .spinner-text {
        font-size: 18px;
        color: #1565c0;
    }

  </style>
</head>
<body>
  {% raw %}
    <div id="app" @click.stop="documentClick">
        <!-- Индикатор загрузки при отправке данных -->
        <div v-if="sending" class="spinner-overlay">
            <div class="spinner"></div>
            <div class="spinner-text">Отправка данных, пожалуйста, подождите...</div>
        </div>

        <div class="controls">
            <!-- Новый выпадающий список перед загрузкой файла -->
            <div class="option-select">
                <label for="optionSelect">Выберите поставщика: </label>
                <select id="optionSelect" v-model="selectedOption">
                    <option v-for="item in selectionItems" :key="item.id" :value="item.id">
                        {{ item.name }}
                    </option>
                </select>
            </div>

            <!-- Новый селект с выбором валюты -->
            <div class="currency-select">
                <label for="currencySelect">Выберите валюту: </label>
                <select id="currencySelect" v-model="selectedCurrency">
                    <option v-for="currency in currencyItems" :key="currency.id" :value="currency.abbreviation">
                        {{ currency.abbreviation }} - {{ currency.symbol }}
                    </option>
                </select>
            </div>

            <!-- Галочка для указания бренда вручную -->
            <div class="manual-brand">
                <input type="checkbox" id="manualBrandCheckbox" v-model="manualBrandEnabled">
                <label for="manualBrandCheckbox">Указать бренд вручную</label>
                <!-- Если галочка выбрана, появляется инпут для ввода бренда -->
                <div v-if="manualBrandEnabled">
                    <input type="text" v-model="manualBrand" placeholder="Введите бренд">
                </div>
            </div>
        

            <!-- Поле выбора файла -->
            <div>
                <input type="file" @change="handleFileUpload">
            </div>
        </div>
        <!-- Сообщение о загрузке -->
        <p v-if="loading">Загружаем файл, пожалуйста, подождите...</p>
        
        <!-- Отображение таблицы, если данные получены -->
        <div v-if="tableData.length" class="table-container" ref="tableContainer">
            <table>
                <thead>
                <tr>
                    <!-- Перебираем первую строку массива tableData (заголовки) -->
                    <th v-for="(header, index) in tableData[0]" :key="index"
                        :class="colClasses[header]"
                        @click.stop="openDropdown(index, $event)">
                        {{ header }}
                        <!-- Если для этого столбца он назначен как требуемый для своего класса, выводим значок -->
                        <span v-if="isRequired(index)" class="required-badge">★</span>
                    </th>
                </tr>
                </thead>
                <tbody>
                <!-- Начинаем с 1-й строки, т.к. 0-я строка — заголовки -->
                <tr v-for="(row, rowIndex) in tableData.slice(1)" :key="rowIndex" @contextmenu.prevent="openContextMenu(rowIndex + 1, $event)">
                    <td v-for="(cell, cellIndex) in row" :key="cellIndex"
                        :class="colClasses[cellIndex]">
                    {{ cell }}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <!-- Кнопка для отправки данных -->
        <div class="send-button">
            <button @click="sendData">Отправить данные</button>
        </div>

         <!-- Вывод сообщения об успехе -->
        <div v-if="successMessage" class="success-message">{{ successMessage }}</div>
    
        <!-- Сообщение об ошибке валидации -->
        <div v-if="error" class="error-message">{{ error }}</div>


        <!-- Всплывающее окно для выбора класса столбца -->
        <div v-if="dropdownVisible" class="dropdown-popup"
            :style="{ top: dropdownY + 'px', left: dropdownX + 'px' }"
            @click.stop>
        <ul>
            <li v-for="(item, idx) in legendItems" :key="idx"
            @click="chooseColumnClass(dropdownCol, item.class)">
            <span>
                <span :class="['legend-swatch', item.class]"></span>
                {{ item.description }}
            </span>
            <!-- Если выбранный класс требует указания одного обязательного столбца -->
            <span v-if="requiredClasses.includes(item.class)">
                <!-- Если для этого класса уже назначен требуемый столбец и это не наш столбец, показываем кнопку -->
                <span v-if="colClasses[dropdownCol] === item.class && requiredColumns[item.class] !== dropdownCol"
                    class="set-required"
                    @click.stop="setRequiredColumn(dropdownCol, item.class)">
                Закрепить
                </span>
                <!-- Если для этого класса ещё не назначен требуемый столбец и текущий столбец выбран, делаем его сразу -->
                <span v-else-if="colClasses[dropdownCol] === item.class && !requiredColumns[item.class]">
                (основной)
                </span>
            </span>
            <!-- Если класс не требует уникальности, ничего не выводим справа -->
            </li>
        </ul>
        </div>


        <!-- Контекстное меню для удаления строки -->
        <div v-if="contextMenuVisible" class="context-menu"
            :style="{ top: contextMenuY + 'px', left: contextMenuX + 'px' }"
            @click.stop>
            <ul>
                <li @click="removeRow(contextMenuRowIndex)">Удалить строку</li>
            </ul>
        </div>


        <!-- Легенда с пояснениями к цветам -->
        <div v-if="legendItems.length" class="legend">
            <h3>Объяснение цветов столбцов</h3>
            <ul>
                <li v-for="(item, index) in legendItems" :key="index">
                <!-- Свой блок с цветом -->
                <span :class="['legend-swatch', item.class]"></span>
                <span>{{ item.description }}</span>
                </li>
            </ul>
        </div>

        <!-- Вывод ошибки, если есть -->
        <p v-if="error" style="color: red;">{{ error }}</p>

        <!-- Модальное окно предупреждения -->
        <div v-if="showModal" class="modal-overlay">
            <div class="modal">
                <p>
                    Отсутствуют столбцы для <strong>quantity</strong> и/или <strong>alternative_article</strong>.
                    Вы действительно хотите отправить данные?
                </p>
                <button @click="sendDataConfirmed">Все равно отправить</button>
                <button @click="cancelSend">Отменить</button>
            </div>
        </div>

    </div>
  {% endraw %}
  <script>
    new Vue({
      el: '#app',
      data: {
        // Данные для выпадающего списка (подтягиваются с API)
        selectionItems: [],
        selectedOption: null,
        // Данные для селекта валюты (здесь заданы статически)
        currencyItems: [
          { id: 1, name: 'Доллар', abbreviation: 'USD', symbol: '$' },
          { id: 2, name: 'Евро', abbreviation: 'EUR', symbol: '€' },
          { id: 3, name: 'Рубли', abbreviation: 'RUB', symbol: '₽' },
          { id: 4, name: 'Воны', abbreviation: 'KRW', symbol: '₩' },
          { id: 5, name: 'Лиры', abbreviation: 'TRY', symbol: '₺' },
          { id: 6, name: 'Дирхамы', abbreviation: 'AED', symbol: 'د.إ' },
          { id: 7, name: 'Китайские юани', abbreviation: 'CNY', symbol: 'CN¥' },
          { id: 8, name: 'Японские йены', abbreviation: 'JPY', symbol: 'JP¥' }
        ],
        selectedCurrency: null,
        // Галочка и ручной ввод бренда
        manualBrandEnabled: false,
        manualBrand: "",
        tableData: [],  // Двумерный массив, где первая строка — заголовки
        colClasses: {}, // Объект: название колонки -> имя CSS класса
        // Пример статической легенды. При необходимости можно сделать её динамической.
        legendItems: [
          {class: 'index', description: 'index - индекс'},
          {class: 'brand', description: 'brand - бренды'},
          {class: 'article', description: 'article - артикулы'},
          {class: 'alternative_article', description: 'alternative_article - артикулы поставщика'},
          {class: 'price', description: 'price - цены'},
          {class: 'quantity', description: 'quantity - количество'},
          {class: 'description', description: 'description - описание'},
          {class: 'weight', description: 'weight - вес'},
          {class: 'amount', description: 'amount - стоимость всего'},
          {class: 'unknown', description: 'unknown - неизвестный тип'}
        ],
        loading: false,
        sending: false,  // переменная для индикации отправки данных
        error: '',
        successMessage: "",
        // Для типов, где требуется выбрать один обязательный столбец
        requiredClasses: ['brand', 'article', 'alternative_article', 'price', 'quantity'],
        // Объект: для каждого типа (из requiredClasses) хранится индекс обязательного столбца (если назначен)
        requiredColumns: {},
        // Свойства для управления всплывающим окном выбора
        dropdownVisible: false,
        dropdownX: 0,
        dropdownY: 0,
        dropdownCol: null,  // индекс столбца, для которого открыто окно
        // Для модального окна подтверждения отправки
        showModal: false,
        // Флаг, показывающий, что отправка подтверждена через модальное окно
        confirmedSend: false,
        // Сохраняем имя загруженного файла
        fileName: "",
        // Свойства для контекстного меню удаления строки
        contextMenuVisible: false,
        contextMenuX: 0,
        contextMenuY: 0,
        contextMenuRowIndex: null,
        // Свойства для пагинации
        fileId: "",
        currentOffset: 0,
        totalRows: 0,
        chunkSize: 5000,
        isFetchingMore: false
      },
      mounted() {
        // Добавляем глобальный обработчик contextmenu, чтобы закрывать наше меню при правом клике вне него
        document.addEventListener('contextmenu', this.handleGlobalContextMenu);
      },
      beforeDestroy() {
        document.removeEventListener('contextmenu', this.handleGlobalContextMenu);
      },
      created() {
        this.fetchOptions();
      },
      methods: {
        fetchOptions() {
          fetch('http://87.242.110.159:8007/api/get_suppliers') // Замените URL на нужный API-эндпоинт
            .then(response => {
              if (!response.ok) {
                throw new Error('Ошибка при загрузке поставщиков');
              }
              return response.json();
            })
            .then(data => {
              this.selectionItems = data;
              if (data.length > 0) {
                // Можно установить первый элемент как выбранный по умолчанию
                this.selectedOption = data[0].id;
              }
            })
            .catch(err => {
              console.error(err);
              this.error = err.message;
            });
        },
        handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            // Сохраняем имя файла
            this.fileName = file.name;
            // Сброс предыдущих данных и ошибок
            this.tableData = [];
            this.colClasses = {};
            this.error = '';
            this.loading = true;
            this.hideDropdown();

            const formData = new FormData();
            formData.append('file', file);
            
            // Замените URL на ваш API-эндпоинт
            fetch('http://87.242.110.159:5000/classify_cols', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                throw new Error('Ошибка при загрузке файла');
                }
                return response.json();
            })
            .then(data => {
                if (data.data && data.headers && data.file_id) {
                    this.fileId = data.file_id;
                    this.totalRows = data.total;
                    const headers = data.headers;
                    let tableArray = [headers];
                    const numRows = data.data[headers[0]] ? data.data[headers[0]].length : 0;
                    for (let i = 0; i < numRows; i++) {
                        let row = headers.map(h => data.data[h][i]);
                        tableArray.push(row);
                    }
                    this.tableData = tableArray;
                    this.currentOffset = numRows;
                    this.colClasses = data.result;
                    this.assignDefaultRequired();
                    this.$nextTick(() => {
                    if (this.$refs.tableContainer) {
                        this.$refs.tableContainer.addEventListener('scroll', this.handleScroll);
                    }
                    });
            } else {
                throw new Error('Неверный формат данных от сервера');
            }
            })
            .catch(err => {
                console.error(err);
                this.error = err.message;
            })
            .finally(() => {
                this.loading = false;
            });
        },

        handleScroll() {
            const container = this.$refs.tableContainer;
            if (container.scrollTop + container.clientHeight >= container.scrollHeight - 100) {
                this.loadMoreData();
            }
        },

        loadMoreData() {
            if (this.isFetchingMore || this.currentOffset >= this.totalRows) return;
            this.isFetchingMore = true;
            fetch(`http://87.242.110.159:5000/get_data?file_id=${this.fileId}&offset=${this.currentOffset}&limit=${this.chunkSize}`)
                .then(response => {
                if (!response.ok) {
                    throw new Error("Ошибка при загрузке данных");
                }
                return response.json();
                })
                .then(result => {
                    const headers = this.tableData[0];
                    const dataChunk = result.data;
                    const numRows = dataChunk[headers[0]] ? dataChunk[headers[0]].length : 0;
                    if (!dataChunk || !dataChunk[headers[0]]) {
                        console.error("Некорректный формат данных, полученных с сервера:", result);
                        return;  // Выходим, если данные не соответствуют ожидаемой структуре
                    }
                    for (let i = 0; i < numRows; i++) {
                        let row = headers.map(h => dataChunk[h][i]);
                        this.tableData.push(row);
                    }
                    this.currentOffset += numRows;
                })
                .catch(err => {
                    console.error(err);
                    this.error = err.message;
                })
                .finally(() => {
                    this.isFetchingMore = false;
                });
        },

        // Если по типу имеется только один столбец, устанавливаем его как обязательный
        assignDefaultRequired() {
            this.requiredClasses.forEach(type => {
                let cols = Object.keys(this.colClasses).filter(i => this.colClasses[i] === type);
                if (cols.length === 1) {
                this.$set(this.requiredColumns, type, parseInt(cols[0]));
                }
            });
        },
        openDropdown(colIndex, event) {
            this.dropdownCol = colIndex;
            // Позиционируем окно относительно заголовка столбца
            const rect = event.currentTarget.getBoundingClientRect();
            this.dropdownX = rect.left;
            this.dropdownY = rect.bottom + window.scrollY;
            this.dropdownVisible = true;
        },
        chooseColumnClass(colIndex, chosenClass) {
            // Обновляем класс столбца в colClasses
            this.$set(this.colClasses, colIndex, chosenClass);
            // Если выбранный класс требует обязательного столбца и для него ещё не назначен обязательный столбец – назначаем
            if (this.requiredClasses.includes(chosenClass)) {
                if (this.requiredColumns[chosenClass] === undefined) {
                this.$set(this.requiredColumns, chosenClass, colIndex);
                }
            } else {
                // Если выбран класс, не требующий обязательного столбца, удаляем его из requiredColumns, если был назначен
                Object.keys(this.requiredColumns).forEach(type => {
                if (this.requiredColumns[type] === colIndex) {
                    this.$delete(this.requiredColumns, type);
                }
                });
            }
            this.hideDropdown();
        },
        // Метод для установки данного столбца как обязательного для выбранного типа
        setRequiredColumn(colIndex, classType) {
            // Устанавливаем данный столбец как обязательный для типа classType
            this.$set(this.requiredColumns, classType, colIndex);
            // Также обновляем colClasses, чтобы для данного столбца был выбран нужный класс
            this.$set(this.colClasses, colIndex, classType);
        },
        hideDropdown() {
            this.dropdownVisible = false;
            this.dropdownCol = null;
        },
        documentClick(event) {
            if (this.dropdownVisible) {
                this.hideDropdown();
            }
            if (this.contextMenuVisible) {
                this.contextMenuVisible = false;
                this.contextMenuRowIndex = null;
          }
        },
        // Проверяет, является ли столбец обязательным для своего класса (если класс входит в requiredClasses)
        isRequired(colIndex) {
            const type = this.colClasses[colIndex];
            return this.requiredClasses.includes(type) && this.requiredColumns[type] === colIndex;
        },

        sendData() {
            this.error = "";
            // Проверяем селекты
            if (!this.selectedOption) {
                this.error = "Пожалуйста, выберите опцию.";
                return;
            }
            if (!this.selectedCurrency) {
                this.error = "Пожалуйста, выберите валюту.";
                return;
            }
            // Проверяем обязательные столбцы: article и price
            if (this.requiredColumns["article"] === undefined) {
                this.error = "Пожалуйста, выделите столбец с артикулами.";
                return;
            }
            if (this.requiredColumns["price"] === undefined) {
                this.error = "Пожалуйста, выделите столбец с ценами.";
                return;
            }
            // Если для бренда нет выделенного столбца, то пользователь должен указать бренд вручную
            if (this.requiredColumns["brand"] === undefined) {
                if (!this.manualBrandEnabled || this.manualBrand.trim() === "") {
                this.error = "Пожалуйста, либо выделите столбец с брендами, либо укажите бренд вручную.";
                return;
                }
            }
            // Если отсутствуют столбцы для quantity или alternative_article – показать модальное окно
            if (this.requiredColumns["quantity"] === undefined || this.requiredColumns["alternative_article"] === undefined) {
                this.showModal = true;
                return;
            }
            // Если валидация прошла – отправляем данные
            this.sendDataConfirmed();
        },

        // Метод, который формирует payload с переименованием столбцов и отправляет данные по API
        sendDataConfirmed() {
            this.showModal = false;
            this.sending = true;  // включаем индикатор отправки данных
            payload = {
                supid: parseInt(this.selectedOption, 10),
                cur: this.selectedCurrency,
                brand: (this.manualBrandEnabled && this.manualBrand.trim() !== "") ? this.manualBrand : "",
                file_id: this.fileId,
                cols: this.requiredColumns
            }
            // Формируем второй payload для отправки классов столбцов и их индексов
            const classesPayload = {
                cols: this.colClasses,
                filename: this.fileName
            };

            Promise.all([
                fetch('http://87.242.110.159:5000/send_data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                }).then(response => {
                    if (!response.ok) {
                        throw new Error("Ошибка при отправке данных на load_sup_data");
                    }
                    return response.json();
                }),
                fetch('http://87.242.110.159:5000/dump_dataset', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(classesPayload)
                }).then(response => {
                if (!response.ok) {
                    throw new Error("Ошибка при отправке данных на send_classes");
                }
                return response.json();
                })
            ])
            .then(results => {
                console.log("Данные успешно отправлены:", results);
                this.successMessage = "Данные успешно отправлены!";
                // Здесь можно вывести уведомление пользователю о успешной отправке
            })
            .catch(err => {
                console.error(err);
                this.error = err.message;
            })
            .finally(() => {
                this.sending = false;  // выключаем индикатор после завершения
            });
        },

        cancelSend() {
            // Скрываем модальное окно и не отправляем данные
            this.showModal = false;
        },

        // Метод для открытия контекстного меню по правой кнопке на строке (передаем реальный индекс строки в tableData)
        openContextMenu(rowIndex, event) {
            event.preventDefault();
            event.stopPropagation();
            this.contextMenuRowIndex = rowIndex;
            this.contextMenuX = event.clientX;
            this.contextMenuY = event.clientY;
            this.contextMenuVisible = true;
        },
        // Метод удаления строки из таблицы (удаляется элемент из tableData с индексом rowIndex)
        removeRow(rowIndex) {
            this.tableData.splice(rowIndex, 1);
            this.contextMenuVisible = false;
            this.contextMenuRowIndex = null;
        }

      }
    });
  </script>
</body>
</html>