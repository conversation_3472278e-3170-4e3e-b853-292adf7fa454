<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Выбор группы поставщиков</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }
        .container {
            max-width: 600px;
            margin: 5em auto;
            padding: 2em;
            border: 1px solid #ccc;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            background-color: #f9f9f9;
        }
        .dark-theme .container {
            background-color: #1e1e1e;
            border: 1px solid #444;
        }
        .dark-theme {
            background-color: #121212;
            color: #ffffff;
        }
        .light-theme {
            background-color: #ffffff;
            color: #000000;
        }
        h1 {
            text-align: center;
            font-size: 2em;
            margin-bottom: 1em;
        }
        label {
            display: block;
            font-size: 1.2em;
            margin-bottom: 0.5em;
        }
        select {
            width: 100%;
            padding: 0.5em;
            font-size: 1.2em;
            margin-bottom: 1.5em;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #ffffff;
            color: #000000;
        }
        .dark-theme select {
            background-color: #333;
            color: #ffffff;
            border: 1px solid #555;
        }
        button {
            width: 100%;
            padding: 0.75em;
            border: none;
            border-radius: 5px;
            background-color: #007BFF;
            color: #ffffff;
            font-size: 1.2em;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .theme-toggle {
            position: absolute;
            top: 1em;
            right: 1em;
        }
        .theme-toggle input[type="checkbox"] {
            position: absolute;
            left: -9999px;
        }
        .theme-toggle label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .theme-toggle label::before {
            content: '';
            width: 50px;
            height: 25px;
            border-radius: 15px;
            background-color: #ccc;
            margin-right: 0.5em;
            transition: background-color 0.3s;
        }
        .theme-toggle input[type="checkbox"]:checked + label::before {
            background-color: #007BFF;
        }
        .theme-toggle label::after {
            content: '';
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background-color: #ffffff;
            position: absolute;
            top: 0;
            left: 0;
            transition: transform 0.3s;
        }
        .theme-toggle input[type="checkbox"]:checked + label::after {
            transform: translateX(25px);
        }
        .file-upload {
            margin-top: 1.5em;
            overflow: hidden;
            position: relative;
        }
        .file-upload input[type="file"] {
            cursor: pointer;
            display: block;
            font-size: 1.2em;
            position: absolute;
            top: 0;
            right: 0;
            opacity: 0;
        }
        .file-upload label {
            background-color: #007BFF;
            border: none;
            border-radius: 5px;
            color: #ffffff;
            cursor: pointer;
            display: inline-block;
            font-size: 1.2em;
            padding: 0.75em;
        }
        .file-upload label:hover {
            background-color: #0056b3;
        }
        .file-name {
            margin-top: 0.5em;
            font-size: 1.1em;
        }
        .error-message {
            color: #ff0000;
            font-size: 1.1em;
            margin-top: 0.5em;
        }
        .loader {
        border: 16px solid #f3f3f3;
border-radius: 50%;
        border-top: 16px solid #3498db;
        width: 120px;
        height: 120px;
        animation: spin 2s linear infinite;
        display: none;
        margin: 20px auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .download-button {
        display: none;
        text-align: center;
        margin-top: 20px;
    }

    .download-button a {
        display: inline-block;
        padding: 0.75em 1.5em;
        border-radius: 5px;
        background-color: #28a745;
        color: #fff;
        text-decoration: none;
        font-size: 1.2em;
        cursor: pointer;
    }

    .download-button a:hover {
        background-color: #218838;
    }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
</style>
</head>
{% include '_back_button.html' %}
<body class="light-theme">
    <div class="theme-toggle">
        <input type="checkbox" id="themeToggle" onclick="toggleTheme()">
        <label for="themeToggle"></label>
    </div>
    <div class="container">
        <h1>Выбор группы поставщиков</h1>
        <form id="supplierForm" enctype="multipart/form-data">
            <label for="suppliers">Выберите группу поставщиков:</label>
            <select name="id" id="suppliers">
                {% for sup in sups %}
                    <option value="{{ sup }}">{{ sup }}</option>
                {% endfor %}
            </select>
            <div class="file-upload">
                <label for="file">Загрузить файл:</label>
                <input type="file" id="file" name="file" accept=".xlsx,.xls" onchange="displayFileName(this)">
            </div>
            <p class="file-name" id="fileNameLabel">Выберите файл для загрузки</p>

            <button type="button" onclick="submitForm()">Начать проценку</button>
            <div class="loader" id="loader"></div>
            <div class="download-button" id="downloadButton">
            <a id="downloadLink" href="#" download>Скачать файл</a>
            </div>
            <p class="error-message" id="errorMessage" style="display: none;"></p>
        </form>
        <p>* Файл загрузки должен иметь следующий шаблон: 1 - Бренд, 2 - Артикул</p>
    </div>
<script>
        async function submitForm() {
            var formData = new FormData();
            var selectedSupplier = document.getElementById('suppliers').value;
            var fileInput = document.getElementById('file');
            var errorMessage = document.getElementById('errorMessage');
            var loader = document.getElementById('loader');
            var downloadButton = document.getElementById('downloadButton');
            var downloadLink = document.getElementById('downloadLink');

            // Скрыть сообщение об ошибке и кнопку скачивания при повторном нажатии
            errorMessage.style.display = 'none';
            downloadButton.style.display = 'none';
            loader.style.display = 'block';

            if (!fileInput.files[0]) {
                alert('Пожалуйста, выберите файл для загрузки.');
                loader.style.display = 'none';
                return;
            }

            try {
                formData.append('file', fileInput.files[0]);
                formData.append('sups', selectedSupplier);

                var actionUrl = '{{ url_for("percentage.get_percentage") }}';

                fetch(actionUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    loader.style.display = 'none';
                    if (data.error) {
                        errorMessage.textContent = data.error;
                        errorMessage.style.display = 'block';
                    } else  {
                        window.location.href = '/cross_dock/tasks?auto=false&refresh=1';
                    }
                })
                .catch(error => {
                    loader.style.display = 'none';
                    console.error('Error during file upload:', error);
                });
            } catch (error) {
                loader.style.display = 'none';
                console.error('Error loading suppliers:', error);
            }
        }

        function toggleTheme() {
            var body = document.body;
            body.classList.toggle('dark-theme');
            body.classList.toggle('light-theme');
        }

        function displayFileName(input) {
            var fileName = input.files[0].name;
            var fileNameLabel = document.getElementById('fileNameLabel');
            fileNameLabel.textContent = fileName;
        }
    </script>

</body>
</html>