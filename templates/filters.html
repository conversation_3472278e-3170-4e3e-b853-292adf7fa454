<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filters</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #121212;
            color: #e0e0e0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            transition: background-color 0.3s, color 0.3s;
            font-size: 20px;
        }
        body.light-theme {
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 800px;
            width: 100%;
            background-color: #1e1e1e;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            transition: background-color 0.3s;
        }
        body.light-theme .container {
            background-color: #fff;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: inherit;
            margin-bottom: 30px;
            font-size: 36px;
            transition: color 0.3s;
        }
        h1:hover {
            color: #007bff;
        }
        form {
            margin-top: 30px;
        }
        label {
            display: block;
            margin-bottom: 16px;
            color: inherit;
            font-weight: 500;
            font-size: 24px;
            transition: color 0.3s;
        }
        label:hover {
            color: #007bff;
        }
        .operator-container {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }
        .operator-container select,
        .operator-container input,
        .operator-container input[type="date"] {
            margin-right: 16px;
            width: 100%;
            padding: 12px;
            border: 2px solid #555;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 20px;
            background-color: #282828;
            color: #e0e0e0;
            transition: border-color 0.3s, background-color 0.3s, color 0.3s;
        }
        body.light-theme .operator-container select,
        body.light-theme .operator-container input,
        body.light-theme .operator-container input[type="date"] {
            background-color: #fafafa;
            color: #333;
            border-color: #ccc;
        }
        .operator-container input[type="text"]:focus,
        .operator-container input[type="number"]:focus,
        .operator-container input[type="date"]:focus {
            border-color: #007bff;
            outline: none;
        }
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #555;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 20px;
            background-color: #282828;
            color: #e0e0e0;
            transition: border-color 0.3s, background-color 0.3s, color 0.3s;
        }
        body.light-theme select {
            background-color: #fafafa;
            color: #333;
            border-color: #ccc;
        }
        select:focus {
            border-color: #007bff;
            outline: none;
        }
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #555;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 20px;
            background-color: #282828;
            color: #e0e0e0;
            transition: border-color 0.3s, background-color 0.3s, color 0.3s;
        }
        body.light-theme .search-input {
            background-color: #fafafa;
            color: #333;
            border-color: #ccc;
        }
        .search-input:focus {
            border-color: #007bff;
            outline: none;
        }
        .toggle-container {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }
        .toggle {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            color: inherit;
            transition: color 0.3s;
        }
        .toggle:hover {
            color: #007bff;
        }
        .toggle-switch {
            width: 48px;
            height: 24px;
            border-radius: 12px;
            background-color: #555;
            margin-right: 16px;
            position: relative;
            transition: background-color 0.3s;
        }
        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background-color: #fff;
            top: 1px;
            left: 1px;
            transition: transform 0.3s;
        }
        .toggle.dark .toggle-switch {
            background-color: #007bff;
        }
        .toggle.dark .toggle-switch::before {
            transform: translateX(24px);
        }
        /* Стили для кнопок */
        button {
            display: block;
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-family: 'Roboto', sans-serif;
            font-size: 24px;
            cursor: pointer;
            transition: background-color 0.3s, color 0.3s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #007bff;
            color: white;
        }
        /* Стили для сообщений об ошибках */
        .error-message {
            display: none;
            color: #ff0000;
            font-size: 18px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="toggle-container">
        <div class="toggle" id="themeToggle">
            <div class="toggle-switch"></div>
            <span>Dark Mode</span>
        </div>
    </div>
    <div class="container">
        <h1 class="report_type" data-report-type="{{ report_type }}">Filters {{report_type}}</h1>
        <form id="filterForm">
            {% for field, values in fields_values_for_filter.items() %}
                <label for="{{ field.split(', '[0]) }}">{{ field.split(', ')[1] }}</label>
                {% if field == 'supid, ID Поставщика' %}
                    <input type="text" id="{{ field }}_search" class="search-input" placeholder="Search {{ field }}">
                    <select id="dropdown_{{ field }}" name="{{ field }}" class="dropdown-select" data-field="{{ field }}" multiple>
                        <option class="select-all-option" value="select_all">Выбрать все</option>
                        {% for value in values %}
                            <option value="{{ value[1] }}">{{ value[0], value[1] }}</option>
                        {% endfor %}
                    </select>
                {% else %}
                    <div class="operator-container">
                        <select name="operator_{{ field }}" class="operator-select" data-field="{{ field }}">
                            <option value="=">=</option>
                            <option value="!=">!=</option>
                            <option value="<"><</option>
                            <option value=">">></option>
                            <option value="like">like</option>
                            <option value="between">between</option>
                        </select>
                        {% if field == 'dateupd, Дата' %}
                            <input type="date" name="{{ field }}" class="value-input" placeholder="Enter date" style="display: none;">
                            <input type="date" name="{{ field }}_min" class="value-input between" placeholder="Min date" style="display: none;">
                            <input type="date" name="{{ field }}_max" class="value-input between" placeholder="Max date" style="display: none;">
                        {% else %}
                            <input type="text" name="{{ field }}" class="value-input" placeholder="Enter value">
                            <input type="number" name="{{ field }}_min" class="value-input between" placeholder="Min value" style="display: none;">
                            <input type="number" name="{{ field }}_max" class="value-input between" placeholder="Max value" style="display: none;">
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        </form>
        <form id="filterForm">
            <!-- ваши поля формы здесь -->
            <button id="applyFiltersBtn">Сформировать отчет</button>
        </form>
        <div id="loadingSpinner" style="display: none;">Loading...</div>
        <button id="downloadBtn" style="display: none;">Скачать файл</button>
        <div id="errorMessage" class="error-message">Слишком большая выборка. Добавьте фильтры.</div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.operator-select').forEach(select => {
                select.addEventListener('change', function (event) {
                    const field = event.target.dataset.field;
                    const operator = event.target.value;
                    const valueInput = document.querySelector(`[name="${field}"]`);
                    const minInput = document.querySelector(`[name="${field}_min"]`);
                    const maxInput = document.querySelector(`[name="${field}_max"]`);

                    if (operator === 'between') {
                        valueInput.style.display = 'none';
                        minInput.style.display = 'inline-block';
                        maxInput.style.display = 'inline-block';
                    } else {
                        valueInput.style.display = 'inline-block';
                        minInput.style.display = 'none';
                        maxInput.style.display = 'none';
                    }

                    if (field === 'dateupd, Дата, Дата, Дата') {
                        if (operator === 'between') {
                            minInput.setAttribute('type', 'date');
                            maxInput.setAttribute('type', 'date');
                        } else {
                            valueInput.setAttribute('type', 'date');
                        }
                    }
                });
            });

            document.getElementById('applyFiltersBtn').addEventListener('click', function (event) {
                event.preventDefault();
                const form = document.getElementById('filterForm');
                const formData = new FormData(form);
                // Скрыть кнопку для скачивания файла
                document.getElementById('downloadBtn').style.display = 'none';
                // Скрыть сообщение об ошибке
                document.getElementById('errorMessage').style.display = 'none';
                const filters = {};
                let result = {};
                formData.forEach((value, key) => {
                    if (key.startsWith('operator_')) {
                        const field = key.replace('operator_', '');
                        const operator = value;
                        filters[field] = { filter: operator };

                        if (operator === 'between') {
                            const minVal = formData.get(`${field}_min`);
                            const maxVal = formData.get(`${field}_max`);
                            filters[field].selected_value = `${minVal} AND ${maxVal}`;
                        } else {
                            const fieldValue = formData.get(field);
                            filters[field].selected_value = fieldValue;
                        }
                    } else if (key === 'supid, ID Поставщика') {
                        if (!filters[key]) {
                            filters[key] = { filter: 'in', selected_value: [] };
                        }
                        if (value != 'select_all') {
                            filters[key].selected_value.push(value);
                        }
                    }
                });
                const reportTypeElement = document.querySelector('h1[data-report-type]');
                const reportType = reportTypeElement.dataset.reportType;
                console.log(reportType); // Вывод report_type в консоль
                result = { 'report_type': reportType, 'filters': filters };

                // Показать колесо прокрутки
                document.getElementById('loadingSpinner').style.display = 'block';

                fetch('/apply_filters', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(result)
                }).then(response => response.json())
                .then(data => {
                    console.log('Response:', data);
                    // Скрыть колесо прокрутки
                    document.getElementById('loadingSpinner').style.display = 'none';

                    if (data.error && data.error === 'Слишком большая выборка. Добавьте фильтры') {
                        // Показать сообщение об ошибке
                        document.getElementById('errorMessage').style.display = 'block';
                    } else {
                        // Скрыть сообщение об ошибке
                        document.getElementById('errorMessage').style.display = 'none';
                        // Показать кнопку для скачивания файла
                        if (data.file_url) {
                            const downloadBtn = document.getElementById('downloadBtn');
                            downloadBtn.style.display = 'block';
                            downloadBtn.onclick = function() {
                                window.location.assign(data.file_url);
                            }
                        } else {
                            console.error('File URL is missing in the response.');
                        }
                    }
                }).catch(error => {
                    console.error('Error:', error);
                    // Скрыть колесо прокрутки в случае ошибки
                    document.getElementById('loadingSpinner').style.display = 'none';
                });
            });


            const themeToggle = document.getElementById('themeToggle');
            const body = document.body;
            
            themeToggle.addEventListener('click', () => {
                body.classList.toggle('light-theme');
                themeToggle.classList.toggle('dark');
            });

            document.querySelectorAll('.search-input').forEach(input => {
                input.addEventListener('input', function() {
                    const field = this.id.replace('_search', '');
                    const filter = this.value.toLowerCase();
                    const select = document.getElementById(`dropdown_${field}`);
                    const options = select.querySelectorAll('option:not(.select-all-option)');

                    options.forEach(option => {
                        const text = option.textContent.toLowerCase();
                        if (text.indexOf(filter) > -1) {
                            option.style.display = '';
                        } else {
                            option.style.display = 'none';
                        }
                    });
                });
            });

            document.querySelectorAll('select[multiple]').forEach(select => {
                select.addEventListener('click', function (event) {
                    const selectedOption = event.target;
                    const selectAllOption = selectedOption.parentElement.querySelector('option[value="select_all"]');
                    if (selectedOption.value === 'select_all') {
                        const allSelected = selectedOption.selected;
                        const allOptions = selectedOption.parentElement.querySelectorAll('option:not([value="select_all"])');
                        allOptions.forEach(opt => opt.selected = allSelected);
                        selectAllOption.selected = allSelected;
                    } else {
                        const allOptions = selectedOption.parentElement.querySelectorAll('option:not([value="select_all"])');
                        const selectedCount = [...allOptions].filter(opt => opt.selected).length;
                        selectAllOption.selected = selectedCount === allOptions.length;
                    }
                });
            });
        });
    </script>
</body>
</html>