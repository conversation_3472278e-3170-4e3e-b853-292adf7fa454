<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <title>Коэффициенты поставщиков</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            background: #fff;
        }
        th {
            background: #007BFF;
            color: #fff;
        }
        tr:hover {
            background: #f1f1f1;
        }
    </style>
</head>
<body>
    <h1>Коэффициенты для Поставщика ID: {{ sup_id }}</h1>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Страна</th>
                <th>Коэффициент</th>
                <th>Действия</th>
            </tr>
        </thead>
        <tbody>
            {% for row in coefficients %}
                <tr>
                    <td>{{ row[0] }}</td>
                    <td contenteditable="true">{{ row[1] }}</td>
                    <td contenteditable="true">{{ row[2] }}</td>
                    <td>
                        <button onclick="updateCoefficient({{ row[0] }})">Обновить</button>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    <script>
        function updateCoefficient(id) {
            const row = $('tr:contains(' + id + ')');
            const country = row.find('td:eq(1)').text().trim();
            const k = parseFloat(row.find('td:eq(2)').text().trim());

            if (isNaN(k)) {
                alert('Коэффициент должен быть числом');
                return;
            }

            $.ajax({
                url: '/sups/coefficients/' + id,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({country: country, k: k}),
                success: function(response) {
                    alert('Запись обновлена успешно');
                },
                error: function() {
                    alert('Ошибка обновления записи');
                }
            });
        }
    </script>
</body>
</html>
