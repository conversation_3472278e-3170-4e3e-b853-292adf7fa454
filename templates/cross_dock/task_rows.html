{% if tasks_info|length == 0 %}
    <tr>
        <td colspan="10" class="text-center py-4">
            <div class="alert alert-info" role="alert">
                Задачи не найдены. Создайте новую задачу или попробуйте изменить параметры поиска.
            </div>
        </td>
    </tr>
{% else %}
    <!-- Render all tasks directly without JavaScript delay -->
    {% for task_id, task in tasks_info.items() %}
        <tr data-task-id="{{ task_id }}" class="{% if loop.index <= 2 %}priority-task{% endif %} new-row">
            <td>{{ task_id }}</td>
            <td class="task-detail">{{ task['filename'] or 'N/A' }}</td>
            <td class="task-detail">{{ task['platforms'] | join(', ') }}</td>
            <td class="task-status {{ task['state'] | lower }}">{{ task['status'] or 'N/A' }}</td>
                {% set progress_raw = task['progress'] %}
                {% if progress_raw and progress_raw != 'N/A' %}
                    {% if '%' in progress_raw|string %}
                        {% set progress = progress_raw %}
                    {% else %}
                        {% set progress = progress_raw ~ '%' %}
                    {% endif %}
                {% else %}
                    {% set progress = '0%' %}
                {% endif %}

            {% if task['progress'] != '100.0%' %}
                <td class="progress-cell" style="--progress-width: {{ progress }}">
                    <span style="position: relative; z-index: 1;">{{ task['progress'] or 'N/A' }}</span>
                </td>
            {% else %}
                <td>{{ task['progress'] or 'N/A' }}</td>
            {% endif %}

            <td>
                {% if task['url'] %}
                    {% set result_url = task['url'].strip("'") %}
                    {% if result_url.startswith('http://') or result_url.startswith('https://') %}
                        <a href="{{ result_url }}" class="download-button" target="_blank">Скачать</a>
                    {% else %}
                        {{ result_url }}
                    {% endif %}
                {% else %}
                    Нет результата
                {% endif %}
            </td>
            <td>{{ task['received'] or 'N/A' }}</td>
            <td>{{ task['succeeded'] or 'N/A' }}</td>
            <td>{{ task['runtime'] or 'N/A' }}</td>
            <td>
                <select class="action-dropdown" onchange="handleAction(this, '{{ task_id }}')">
                    <option value="">Действие</option>
                    {% if task['status'] in ['PROGRESS', 'STARTED'] %}
                        <option value="force_delete" style="color: red;">Удалить</option>
                        <option value="stop" style="color: orange;">Остановить</option>
                        <option value="restart" style="color: green;">Перезапустить</option>
                    {% else %}
                        <option value="delete" style="color: red;">Удалить</option>
                        <option value="restart" style="color: green;">Перезапустить</option>
                    {% endif %}
                </select>
            </td>
        </tr>
    {% endfor %}
{% endif %}

<!-- Load more button if there are more pages -->
{% if page < total_pages %}
    <tr id="load-more-row">
        <td colspan="10" class="text-center py-3">
            <button
                class="btn btn-outline-primary"
                hx-get="{{ url_for('percentage.cross_dock_tasks', page=page+1, limit=limit, auto=auto) }}"
                hx-target="#tasks-container"
                hx-swap="beforeend"
                hx-indicator="#load-more-spinner"
                hx-disabled-elt="this"
                hx-on::after-request="this.closest('tr').remove()">
                <span id="load-more-spinner" class="spinner-border spinner-border-sm htmx-indicator" role="status">
                    <span class="visually-hidden">Loading...</span>
                </span>
                Загрузить еще {{ limit }}  (осталось {{ total_pages * limit - page * limit }})
            </button>
        </td>
    </tr>
{% endif %}