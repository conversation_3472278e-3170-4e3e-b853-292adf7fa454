<!DOCTYPE html>
<html>
<head>
    <title>Tasks Status</title>
    <!-- Include Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Include HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .task-container {
            width: 80%;
            margin: 0 auto;
        }
        .task-header {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .task-detail {
            margin-bottom: 10px;
        }
        .task-status {
            color: #333;
        }
        .task-status.success {
            color: green;
        }
        .task-status.failed {
            color: red;
        }
        .task-status.pending {
            color: orange;
        }
        .task-table {
            width: 100%;
            border-collapse: collapse;
        }
        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .task-table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .download-button {
            padding: 5px 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            cursor: pointer;
        }
        .download-button:hover {
            background-color: #0056b3;
        }
        .detail-button {
            padding: 5px 10px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .detail-button:hover {
            background-color: #218838;
        }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
        .action-dropdown {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
        .action-dropdown option {
            padding: 5px;
        }

        /* Animation for new rows */
        @keyframes fadeIn {
            from { opacity: 0; background-color: #f8f9fa; }
            to { opacity: 1; background-color: transparent; }
        }

        .new-row {
            animation: fadeIn 0.8s ease-in-out;
        }

        /* Progress cell styles */
        .progress-cell {
            position: relative;
            overflow: hidden;
        }

        .progress-cell::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: var(--progress-width, 0%);
            background-color: rgba(25, 135, 84, 0.3);
            transition: width 0.5s ease-in-out;
            z-index: 0;
        }

        /* HTMX specific styles */
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: inline-block;
        }
        .htmx-request.htmx-indicator {
            display: inline-block;
        }
    </style>
</head>
<body>
    {% include '_back_button.html' %}
    <div class="task-container">
        <div class="task-header d-flex justify-content-between align-items-center">
            <span>Статусы проценок по Кросс-Док</span>
            <button
                class="btn btn-sm btn-outline-danger"
                title="Кликнуть, если не видите ваш новый таск"
                hx-get="{{ url_for('percentage.cross_dock_tasks', page=1, limit=10, auto=auto, refresh=1) }}"
                hx-target="#tasks-container"
                hx-indicator="#refresh-spinner"
                hx-disabled-elt="this">
                <span id="refresh-spinner" class="spinner-border spinner-border-sm htmx-indicator" role="status"></span>
                Обновить (без кэша)
            </button>
        </div>

        <!-- Table with empty tbody that will be populated by HTMX -->
        <table class="task-table">
            <thead>
                <tr>
                    <th>Task UID</th>
                    <th>Имя входного файла</th>
                    <th>Группа поставщиков</th>
                    <th>Статус</th>
                    <th>Прогресс</th>
                    <th>Результат</th>
                    <th>Дата запуска</th>
                    <th>Дата завершения</th>
                    <th>Время выполнения</th>
                    <th>Действия</th>
                </tr>
            </thead>
            <tbody
                id="tasks-container"
                hx-get="{{ url_for('percentage.cross_dock_tasks', page=1, limit=10, auto=auto, refresh=1 if force_refresh else 0) }}"
                hx-trigger="load"
                hx-indicator="#spinner">
                <!-- Tasks will be loaded here -->
            </tbody>
        </table>

        <!-- Loading spinner -->
        <div id="spinner" class="d-flex justify-content-center my-4 htmx-indicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span class="ms-2">Загрузка данных...</span>
        </div>
    </div>

    <!-- Include Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript for task actions -->
    <script>
        function showDetail(rowsWithoutResult) {
            let detailsWindow = window.open('', '_blank', 'width=600,height=400');
            detailsWindow.document.write('<html><head><title>Rows Without Result</title></head><body>');
            detailsWindow.document.write('<table border="1" cellpadding="5" cellspacing="0">');
            detailsWindow.document.write('<tr><th>№ Строки</th><th>Бренд</th><th>Артикул</th><th>Сайт</th><th>Замены на сайте</th></tr>');

            for (let [row, sites] of Object.entries(rowsWithoutResult)) {
                let [num, brand, article] = row.split(', ');
                for (let [site, replacements] of Object.entries(sites)) {
                    detailsWindow.document.write('<tr>');
                    detailsWindow.document.write(`<td>${num}</td>`);
                    detailsWindow.document.write(`<td>${brand}</td>`);
                    detailsWindow.document.write(`<td>${article}</td>`);
                    detailsWindow.document.write(`<td>${site}</td>`);
                    detailsWindow.document.write(`<td>${replacements.join(', ')}</td>`);
                    detailsWindow.document.write('</tr>');
                }
            }

            detailsWindow.document.write('</table>');
            detailsWindow.document.write('</body></html>');
            detailsWindow.document.close();
        }

        function handleAction(selectElement, task_id) {
            const action = selectElement.value;
            if (action === 'delete') {
                deleteTask(task_id);
            } else if (action === 'restart') {
                restartTask(task_id);
            } else if (action === 'stop') {
                stopTask(task_id);
            } else if (action === 'force_delete') {
                forceDeleteTask(task_id);
            }
            selectElement.value = ''; // Reset the dropdown
        }

        function deleteTask(task_id) {
            fetch(`/delete_task_cross_doc/${task_id}`, {
                method: 'DELETE',
            })
            .then(data => {
                if (data) {
                    // Remove the task row from the table
                    const row = document.querySelector(`.task-table tbody tr[data-task-id="${task_id}"]`);
                    if (row) {
                        row.remove();
                    }
                }
            })
            .catch(error => {
                console.error('Error deleting task:', error);
            });
        }

        function restartTask(task_id) {
            fetch(`/restart_task_cross_doc/${task_id}`, {
                method: 'POST',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data) {
                    // Update the task status instead of reloading the page
                    console.log('Task restarted successfully, updating status...');
                    updateTaskStatus();
                }
            })
            .catch(error => {
                console.error('Error restarting task:', error);
            });
        }

        function stopTask(task_id) {
            fetch(`/stop_task_cross_doc/${task_id}`, {
                method: 'POST',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data) {
                    // Update the task status instead of reloading the page
                    console.log('Task stopped successfully, updating status...');
                    updateTaskStatus();
                }
            })
            .catch(error => {
                console.error('Error stopping task:', error);
            });
        }

        function forceDeleteTask(task_id) {
            fetch(`/force_delete_task_cross_doc/${task_id}`, {
                method: 'DELETE',
            })
            .then(data => {
                if (data) {
                    // Remove the task row from the table
                    const row = document.querySelector(`.task-table tbody tr[data-task-id="${task_id}"]`);
                    if (row) {
                        row.remove();
                    }
                }
            })
            .catch(error => {
                console.error('Error force deleting task:', error);
            });
        }

        // Regular update function that updates task statuses without clearing the entire table
        function updateTaskStatus() {
            {#console.log("Triggering updateTaskStatus...")#}
            // Skip if updates have been stopped or are in the process of stopping
            if (updateInterval === null || stoppingUpdates) {
                console.log('Updates have been stopped or are stopping. Skipping update.');
                return;
            }

            // Check if we've been updating for too long (60 minutes)
            const MAX_UPDATE_DURATION = 60 * 60 * 1000; // 60 minutes in milliseconds
            if (updateStartTime && (Date.now() - updateStartTime > MAX_UPDATE_DURATION)) {
                console.log('Updates have been running for over 60 minutes. Stopping for safety.');
                stoppingUpdates = true;
                clearInterval(updateInterval);
                updateInterval = null;
                return;
            }

            {#console.log('Running periodic update...');#}
            console.log("Triggering updateTaskStatus...")
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('format', 'json'); // Set the format to JSON
            currentUrl.searchParams.set('refresh', '1'); // Force bypass cache for real-time updates

            fetch(currentUrl, {
                method: 'GET',
                cache: 'no-store' // Tell browser not to use its cache either
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    console.error('Error in response:', data.error);
                    return;
                }

                const tasks = data.tasks || data;
                console.log(`Update: received data for ${Object.keys(tasks).length} tasks`);

                // Get existing task IDs in the table
                const tbody = document.querySelector('.task-table tbody');
                const existingRows = tbody.querySelectorAll('tr');
                const existingTaskIds = new Set();

                existingRows.forEach(row => {
                    if (row.dataset.taskId) {
                        existingTaskIds.add(row.dataset.taskId);
                    }
                });

                // Update existing rows and collect new tasks
                const newTasks = [];

                for (const taskId in tasks) {
                    const task = tasks[taskId];

                    if (existingTaskIds.has(taskId)) {
                        // Update existing row
                        const row = tbody.querySelector(`tr[data-task-id="${taskId}"]`);
                        if (row) {
                            const stateClass = task.state ? task.state.toLowerCase() : 'unknown';

                            // Update only the cells that might change
                            const statusCell = row.querySelector('td:nth-child(4)');
                            if (statusCell) {
                                statusCell.className = `task-status ${stateClass}`;
                                statusCell.textContent = task.status || 'N/A';
                            }

                            const progressCell = row.querySelector('td:nth-child(5)');
                            if (progressCell) {
                                // Update the text content
                                const progressSpan = progressCell.querySelector('span');
                                if (progressSpan) {
                                    progressSpan.textContent = task.progress || 'N/A';
                                } else {
                                    progressCell.textContent = task.progress || 'N/A';
                                }

                                // Update the progress bar width
                                let progressValue = task.progress || '0%';
                                if (progressValue !== 'N/A' && !progressValue.endsWith('%')) {
                                    progressValue += '%';
                                }
                                progressCell.style.setProperty('--progress-width', progressValue);
                            }

                            const resultCell = row.querySelector('td:nth-child(6)');
                            if (resultCell && task.url) {
                                resultCell.innerHTML = `<a href="${task.url}" class="download-button">Скачать</a>`;
                            }

                            const succeededCell = row.querySelector('td:nth-child(8)');
                            if (succeededCell) {
                                succeededCell.textContent = task.succeeded || 'N/A';
                            }

                            const runtimeCell = row.querySelector('td:nth-child(9)');
                            if (runtimeCell) {
                                runtimeCell.textContent = task.runtime || 'N/A';
                            }

                            // Update action dropdown if status changed
                            const actionCell = row.querySelector('td:nth-child(10)');
                            if (actionCell && task.status) {
                                const select = actionCell.querySelector('select');
                                if (select) {
                                    select.innerHTML = `
                                        <option value="">Действие</option>
                                        ${['PROGRESS', 'STARTED'].includes(task.status) ? `
                                            <option value="force_delete" style="color: red;">Удалить</option>
                                            <option value="stop" style="color: orange;">Остановить</option>
                                            <option value="restart" style="color: green;">Перезапустить</option>
                                        ` : `
                                            <option value="delete" style="color: red;">Удалить</option>
                                            <option value="restart" style="color: green;">Перезапустить</option>
                                        `}
                                    `;
                                }
                            }
                        }
                    } else {
                        // This is a new task that wasn't in the table before
                        newTasks.push({ id: taskId, task: task });
                    }
                }

                // If there are new tasks, reload the page to show them
                // In a more advanced implementation, we could add them dynamically
                if (newTasks.length > 0 && !stoppingUpdates) {
                    console.log(`Found ${newTasks.length} new tasks, reloading the page...`);
                    // Set the stopping flag to prevent multiple reloads
                    stoppingUpdates = true;
                    // Use the refresh button's URL to ensure we bypass the cache
                    const refreshUrl = document.querySelector('button[hx-get*="refresh=1"]').getAttribute('hx-get');
                    if (refreshUrl) {
                        // Trigger a click on the refresh button to reload with cache bypass
                        document.querySelector('button[hx-get*="refresh=1"]').click();
                    } else {
                        // Fallback to regular page reload
                        location.reload();
                    }
                    return; // Exit early to prevent further processing
                }

                // Check if all tasks are complete (100% progress and have a download URL)
                let allTasksComplete = true;
                let tasksInProgress = 0;

                for (const taskId in tasks) {
                    const task = tasks[taskId];
                    // If the task is not complete (no URL or status is not SUCCESS/FAILURE)
                    if (!task.url || (task.status !== 'SUCCESS' && task.status !== 'FAILURE')) {
                        allTasksComplete = false;
                        tasksInProgress++;
                    }
                }

                // If all tasks are complete, stop the periodic updates
                if (allTasksComplete && Object.keys(tasks).length > 0) {
                    console.log('All tasks are complete. Stopping periodic updates.');
                    // Set the stopping flag to prevent any further requests
                    stoppingUpdates = true;
                    // Clear the interval
                    clearInterval(updateInterval);
                    // Set to null to indicate it's been stopped
                    updateInterval = null;
                    console.log('Periodic updates have been stopped.');
                } else if (tasksInProgress > 0) {
                    console.log(`${tasksInProgress} tasks still in progress. Continuing updates...`);
                }
            })
            .catch(error => {
                console.error('Error updating task status:', error);
            });
        }

        // Variables to store the interval ID, start time, and stopping state
        let updateInterval;
        let updateStartTime;
        let stoppingUpdates = false;

        // Start the auto-update interval when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Set the start time
            updateStartTime = Date.now();
            // Update every 30 seconds
            updateInterval = setInterval(updateTaskStatus, 30000);
        });
    </script>
</body>
</html>
