<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Обновить базу ВГХ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            color: #212529;
            font-family: Arial, sans-serif;
            padding-top: 20px;
        }
        .container {
            padding: 20px;
            margin: 0 auto;
            max-width: 800px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        /* Muted style for the checked box when not holding shift */
        .form-check-input.pseudo-disabled:checked {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        /* Prominent style for the box when holding shift */
        .form-check-input:not(.pseudo-disabled) {
            border-color: #0d6efd;
            border-width: 2px;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, .25);
        }

        /* Make the label and input unclickable when visually disabled */
        .form-check-input.pseudo-disabled,
        .form-check-input.pseudo-disabled + .form-check-label {
            pointer-events: none;
            opacity: 0.7;
        }
    </style>
</head>
<body>

{% include '_back_button.html' %}

<div class="container mt-4"
     x-data="{
        isLoading: false,
        isShiftPressed: false,
        isCheckboxFocused: false,
        shiftInitiatedFocus: false
     }"
     @keydown.window.shift.prevent="
        isShiftPressed = true;
        if (document.activeElement !== $refs.adminCheckbox) {
            shiftInitiatedFocus = true;
            $refs.adminCheckbox.focus();
        }
     "
     @keyup.window.shift="
        isShiftPressed = false;
        if (shiftInitiatedFocus) {
            $refs.adminCheckbox.blur();
            shiftInitiatedFocus = false;
        }
     "
>
    <h2>Обновить базу ВГХ</h2>
    <p>Загрузите файл .xlsx для обновления или добавления информации о весогабаритных характеристиках товаров.</p>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mt-3" role="alert">
            {{ message }}
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    {% if error %}
      <div class="alert alert-danger mt-3" role="alert">
        {{ error }}
      </div>
    {% endif %}

    <div class="card mt-4">
        <div class="card-body">
            <h5 class="card-title">Шаблон для загрузки</h5>
            <p class="card-text">Скачайте шаблон, чтобы убедиться, что ваш файл имеет правильные столбцы.</p>
            <a href="{{ url_for('item_catalog.download_template') }}" class="btn btn-secondary">Скачать шаблон</a>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-body">
            <h5 class="card-title">Загрузка файла</h5>
            <form action="{{ url_for('item_catalog.upload_page') }}" method="post" enctype="multipart/form-data" @submit="isLoading = true">
                <div class="mb-3">
                    <label for="file" class="form-label">Выберите файл .xlsx</label>
                    <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                </div>
                <div class="mb-3 form-check" :class="{ 'text-muted': !(isShiftPressed || isCheckboxFocused) }">
                    <input type="checkbox" class="form-check-input" id="admin_mode" name="admin_mode"
                           x-ref="adminCheckbox"
                           @focus="isCheckboxFocused = true"
                           @blur="isCheckboxFocused = false; shiftInitiatedFocus = false"
                           :class="{ 'pseudo-disabled': !(isShiftPressed || isCheckboxFocused) }">
                    <label class="form-check-label" for="admin_mode" :class="{ 'fw-bold': isShiftPressed || isCheckboxFocused }">Режим администратора (полная перезапись)</label>
                </div>
                <button type="submit" class="btn btn-primary" :disabled="isLoading">
                    <span x-show="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span x-text="isLoading ? 'Загрузка...' : 'Загрузить'">Загрузить</span>
                </button>
            </form>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>