<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Загрузка файла</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        /* Общие стили */
        
        body {
    font-family: Arial, sans-serif;
    font-size: 18px;
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
    height: 100vh; /* Устанавливает высоту на весь экран */
    overflow-y: auto; /* Добавляет вертикальный скролл */
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: var(--container-bg);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            transition: background-color 0.3s ease;
            max-height: calc(100vh - 40px); /* Высота контейнера, с учетом отступов */
            overflow-y: auto; /* Добавляет вертикальный скролл, если контент превышает высоту */
        }
        .form-group {
            margin-bottom: 15px;
        }
        .excluded-providers {
            margin-top: 10px;
            max-height: 150px; /* Добавлен скролл для исключённых поставщиков */
            overflow-y: auto; /* Вертикальный скролл */
            border: 1px solid #ddd;
            padding: 10px;
            background: #fff;
            border-radius: 5px;
        }
        #dynamicForms {
            max-height: 300px; /* Добавлен скролл для секции форм */
            overflow-y: auto; /* Вертикальный скролл */
        }
        .form-group label {
            display: block;
            margin-bottom: 10px;
        }
        .form-group input[type="file"],
        .form-group select,
        .form-group input[type="number"] {
            display: block;
            width: 100%;
            padding: 10px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            border: none;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .form-group button:hover {
            background-color: #0056b3;
        }
        .theme-toggle {
        position: fixed;
        top: 10px;
        right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000; /* Поместить выше других элементов */
        }
        .theme-toggle input[type="checkbox"] {
            display: none;
        }
        .theme-toggle label {
            cursor: pointer;
            font-size: 24px;
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            color: var(--toggle-color);
            transition: color 0.3s ease;
        }
        .theme-toggle label:hover {
            color: var(--toggle-hover-color);
        }
        #responseMessage {
            margin-top: 20px;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        .loader {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            margin-top: 20px;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        :root {
            --bg-color-light: #f4f4f4;
            --text-color-light: #333;
            --container-bg-light: #fff;
            --toggle-color-light: #f1c40f;
            --toggle-hover-color-light: #e67e22;

            --bg-color-dark: #333;
            --text-color-dark: #f4f4f4;
            --container-bg-dark: #444;
            --toggle-color-dark: #f39c12;
            --toggle-hover-color-dark: #e74c3c;
        }
        body.light-theme {
            --bg-color: var(--bg-color-light);
            --text-color: var(--text-color-light);
            --container-bg: var(--container-bg-light);
            --toggle-color: var(--toggle-color-light);
            --toggle-hover-color: var(--toggle-hover-color-light);
        }
        body.dark-theme {
            --bg-color: var(--bg-color-dark);
            --text-color: var(--text-color-dark);
            --container-bg: var(--container-bg-dark);
            --toggle-color: var(--toggle-color-dark);
            --toggle-hover-color: var(--toggle-hover-color-dark);
        }
        .instructions {
            margin-top: 10px;
            font-size: 16px;
            color: #666;
        }
        .error-message {
            color: red;
            font-size: 16px;
            margin-top: 10px;
        }
        .provider-list {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .provider-list input {
            flex: 1;
            padding: 8px;
            font-size: 16px;
        }
        .provider-list button {
            margin-left: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
        }
        .provider-list button:hover {
            background-color: #c82333;
        }
        .provider-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
            box-sizing: border-box;
        }

        .provider-item span {
            flex-grow: 1;
        }

        .provider-item button {
            margin-left: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
        }
        #platformFiltersContainer {
            max-height: 300px;
            overflow-y: auto; /* Добавлен вертикальный скролл */
        }
        @media (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .form-group input,
            .form-group select,
            .form-group button {
                font-size: 14px;
            }
            .theme-toggle label {
                font-size: 20px;
            }
            .loader {
                width: 40px;
                height: 40px;
            }
        }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
</style>
</head>
{% include '_back_button.html' %}
<body class="light-theme">
    <div class="theme-toggle">
        <input type="checkbox" id="themeSwitch">
        <label for="themeSwitch" id="themeIcon">&#9728;</label>
    </div>
    <div class="container">
        <h2>Загрузка файла</h2>
        <form id="uploadForm">
            <div class="form-group">
                <label for="fileInput">Выберите файл:</label>
                <input type="file" name="fileInput" id="fileInput" accept=".xls, .xlsx, .csv">
            </div>
            <div class="form-group">
                <label for="schedulleType">Выберите тип планировщика:</label>
                <select id="schedulleType" name="schedulleType[]" multiple>
                    <option value="everyday">Ежедневный</option>
                    <option value="everyweek|0">Еженедельный - по понедельникам</option>
                    <option value="everyweek|1">Еженедельный - по вторникам</option>
                    <option value="everyweek|2">Еженедельный - по средам</option>
                    <option value="everyweek|3">Еженедельный - по четвергам</option>
                    <option value="everyweek|4">Еженедельный - по пятницам</option>
                    <option value="everyweek|5">Еженедельный - по субботам</option>
                    <option value="everyweek|6">Еженедельный - по воскресеньям</option>
                </select>
            </div>            
            <div class="form-group">
                <label for="groupType">Выберите группу:</label>
                <select name="groupType" id="groupType">
                    {% for sup in sups %}
                        <option value="{{ sup }}">{{ sup }}</option>
                    {% endfor %}
                </select>
            </div>
            <div id="errorMessages" class="error-message"></div>
            <div class="form-group">
                <button type="submit">Загрузить</button>
            </div>
        </form>
        <div id="responseMessage">Идёт парсинг и обработка...</div>
        <div class="loader"></div>
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#platforms').select2();
            const platformFiltersContainer = $('#platformFiltersContainer');

            $('#platforms').on('change', function() {
                const selectedPlatforms = $(this).val() || [];

                // Удаляем фильтры для площадок, которые были сняты
                platformFiltersContainer.children('.form-group').each(function() {
                    const platform = $(this).attr('id').replace('-filter', '');
                    if (!selectedPlatforms.includes(platform)) {
                        $(this).remove();
                    }
                });

                // Динамическое добавление фильтров для каждой выбранной площадки
            });

            document.getElementById('uploadForm').addEventListener('submit', function(event) {
    event.preventDefault();

    const fileInput = document.getElementById('fileInput');
    const groupType = document.getElementById('groupType').value;
    const schedulleType = Array.from(document.getElementById('schedulleType').selectedOptions).map(option => option.value);
    const errorMessages = document.getElementById('errorMessages');
    errorMessages.innerHTML = '';

    if (!fileInput.files.length) {
        errorMessages.innerHTML = 'Пожалуйста, выберите файл для загрузки.';
        return;
    }

    const file = fileInput.files[0];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!['xlsx', 'csv'].includes(fileExtension)) {
        errorMessages.innerHTML = 'Пожалуйста, выберите файл в формате .xlsx или .csv.';
        return;
    }

    const formData = new FormData();
    formData.append('excelFile', file);
    formData.append('group', groupType);
    formData.append('type', JSON.stringify(schedulleType)); // Отправляем массив как строку JSON

    const validateAndSend = () => {
        showLoading(true);

        fetch('/upload_file_scheduller_cross_dock', { // Измененный URL
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (!data.error) {
                window.location.href = '/schedulle_tasks_cross_doc';
            } else {
                errorMessages.innerHTML = data.error;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showLoading(false);
            errorMessages.innerHTML = 'Произошла ошибка при отправке данных.';
        });
    };

    // Проверка и обработка файла
    if (fileExtension === 'xlsx') {
        const reader = new FileReader();
        reader.onload = function(e) {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheet = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheet];
            const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0];
            const expectedHeaders = ['Бренд', 'Артикул'];

            if (JSON.stringify(headers) !== JSON.stringify(expectedHeaders)) {
                errorMessages.innerHTML = 'Файл должен содержать шапку: 1 - Бренд, 2 - Артикул';
            } else {
                validateAndSend();
            }
        };
        reader.readAsArrayBuffer(file);
    } else if (fileExtension === 'csv') {
        Papa.parse(file, {
            header: false,
            complete: function(results) {
                const headers = results.data[0];
                const expectedHeaders = ['Бренд', 'Артикул'];

                if (JSON.stringify(headers) !== JSON.stringify(expectedHeaders)) {
                    errorMessages.innerHTML = 'Файл должен содержать шапку: 1 - Бренд, 2 - Артикул';
                } else {
                    validateAndSend();
                }
            }
        });
    }
});

            const themeSwitch = document.getElementById('themeSwitch');
            const themeIcon = document.getElementById('themeIcon');

            themeSwitch.addEventListener('change', function() {
                if (themeSwitch.checked) {
                    document.body.classList.remove('light-theme');
                    document.body.classList.add('dark-theme');
                    themeIcon.innerText = '\u{1F319}';
                } else {
                    document.body.classList.remove('dark-theme');
                    document.body.classList.add('light-theme');
                    themeIcon.innerText = '\u{2600}';
                }
            });

            function showLoading(isLoading) {
                const loader = document.querySelector('.loader');
                const responseMessage = document.getElementById('responseMessage');

                if (isLoading) {
                    loader.style.display = 'block';
                    responseMessage.style.display = 'none';
                    document.body.style.overflowY = 'hidden';
                } else {
                    loader.style.display = 'none';
                    responseMessage.style.display = 'block';
                    document.body.style.overflowY = 'auto';
                }
            }
        });
    </script>
</body>
</html>