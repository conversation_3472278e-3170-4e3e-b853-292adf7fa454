<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Список Брендов</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            text-align: center;
        }
        table {
            width: 60%;
            margin: 20px auto;
            border-collapse: collapse;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        th, td {
            padding: 4px 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .search-container {
            margin: 20px auto;
            text-align: center;
        }
        .alias-item {
            margin-bottom: 8px;
            text-transform: uppercase; /* Текст в верхнем регистре */
        }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <h1>Список Брендов</h1>

    <div class="search-container">
        <input type="text" id="searchInput" class="form-control" placeholder="Поиск по названию бренда" style="width: 300px; display: inline-block;">
        <button class="btn btn-primary" onclick="searchBrands()">Поиск</button>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Название</th>
                <th>Emex</th>
                <th>Partkom</th>
                <th>Autopiter</th>
                <th>Псевдонимы</th>
                <th>Действия</th>
            </tr>
        </thead>
        <tbody id="brand-table-body">
            <!-- Данные будут вставлены здесь через JavaScript -->
        </tbody>
    </table>

    <script>
        const updatedData = {};
        const deletedData = {};
        const originalData = {};

        function markChanged(id, field, element) {
            const currentValue = element.textContent.trim();
            if (!originalData[id]) {
                originalData[id] = {};
            }
            if (!originalData[id][field]) {
                originalData[id][field] = element.textContent.trim();
            }
            if (!updatedData[id]) {
                updatedData[id] = {};
            }
            if (currentValue !== '') {
                updatedData[id][field] = currentValue;
            }
            if (currentValue === '') {
                markDeleted(id, field);
            } else {
                delete deletedData[id];
            }
        }

        function markDeleted(id, field) {
            if (!deletedData[id]) {
                deletedData[id] = {};
            }
            deletedData[id][field] = true;
        }

        async function fetchBrands() {
            const response = await fetch('/brands/all');
            const data = await response.json();
            updateTable(data);
        }

        async function searchBrands() {
            const searchQuery = document.getElementById('searchInput').value;
            const response = await fetch(`/brands/search?query=${encodeURIComponent(searchQuery)}`);
            const data = await response.json();
            updateTable(data);
        }

        function updateTable(data) {
            const tableBody = document.getElementById('brand-table-body');
            tableBody.innerHTML = '';
            data.forEach(record => {
                const aliases = record.zap_brands || [];

                if (!originalData[record.id]) {
                    originalData[record.id] = { aliases: [...aliases] };
                }

                const aliasList = aliases.length > 0 ?
                    `<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal${record.id}">
                        Изменить
                    </button>
                    <div class="modal fade" id="modal${record.id}" tabindex="-1" aria-labelledby="modalLabel${record.id}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="modalLabel${record.id}">Псевдонимы для ${record.brand}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Закрыть"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <button type="button" class="btn btn-primary" onclick="addAlias(${record.id})">Добавить</button>
                                        <button type="button" class="btn btn-success" onclick="saveAliases(${record.id})">Сохранить</button>
                                        <button type="button" class="btn btn-secondary" onclick="resetAliases(${record.id})">Отмена</button>
                                    </div>
                                    <ul id="alias-list-${record.id}">
                                        ${aliases.map(alias =>
                                            `<li class="alias-item d-flex justify-content-between align-items-center">
                                                <span>${alias}</span>
                                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteAlias(${record.id}, '${alias}')">-</button>
                                            </li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>`
                 : 'Нет псевдонимов';

                const row = `<tr data-id="${record.id}">
                    <td>${record.id}</td>
                    <td>${record.brand}</td>
                    <td contenteditable="true" oninput="markChanged(${record.id}, 'emex_brands', this)">${record.emex_brand || ''}</td>
                    <td contenteditable="true" oninput="markChanged(${record.id}, 'partkom_brands', this)">${record.partkom_brand || ''}</td>
                    <td contenteditable="true" oninput="markChanged(${record.id}, 'autopiter_brands', this)">${record.autopiter_brand || ''}</td>
                    <td>${aliasList}</td>
                    <td><button class="btn btn-success" onclick="saveChanges(${record.id})">Сохранить</button></td>
                </tr>`;
                tableBody.insertAdjacentHTML('beforeend', row);
            });
        }

        function addAlias(recordId) {
            const aliasList = document.getElementById(`alias-list-${recordId}`);
            const newAliasItem = document.createElement('li');
            newAliasItem.className = 'alias-item d-flex justify-content-between align-items-center';
            newAliasItem.innerHTML = `
                <input type="text" class="form-control form-control-sm" placeholder="Новый псевдоним">
                <button type="button" class="btn btn-success btn-sm me-2" onclick="confirmAlias(this)">+</button>
                <button type="button" class="btn btn-danger btn-sm" onclick="this.parentNode.remove()">-</button>
            `;
            aliasList.insertBefore(newAliasItem, aliasList.firstChild);
        }

        function confirmAlias(button) {
            const inputField = button.previousElementSibling;
            const aliasText = inputField.value.trim().toUpperCase();

            if (aliasText !== '') {
                const listItem = button.parentNode;
                listItem.innerHTML = `
                    <span>${aliasText}</span>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteAlias(null, '${aliasText}')">-</button>
                `;
            }
        }

        function deleteAlias(recordId, alias) {
            let aliasList;

            if (recordId) {
                aliasList = document.getElementById(`alias-list-${recordId}`);
            } else {
                const activeModal = document.querySelector('.modal.show');
                if (activeModal) {
                    aliasList = activeModal.querySelector('ul');
                }
            }

            if (aliasList) {
                Array.from(aliasList.children).forEach(item => {
                    const aliasSpan = item.querySelector('span');
                    if (aliasSpan && aliasSpan.textContent === alias) {
                        item.remove();
                    }
                });
            }
        }


        async function saveAliases(recordId) {
            const aliasList = document.getElementById(`alias-list-${recordId}`);
            const currentAliases = Array.from(aliasList.querySelectorAll('li span'))
                .map(item => item.textContent.trim())
                .filter(alias => alias !== '');

            // Определяем оригинальные псевдонимы
            const originalAliases = originalData[recordId]?.aliases || [];

            // Определяем добавленные псевдонимы
            const updated = currentAliases.filter(alias => !originalAliases.includes(alias));

            // Определяем удаленные псевдонимы
            const deleted = originalAliases.filter(alias => !currentAliases.includes(alias));

            try {
                await fetch(`/brands/update_aliases`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ recordId, updated, deleted })
                });
                alert('Псевдонимы успешно сохранены!');
            } catch (error) {
                console.error('Ошибка при сохранении псевдонимов:', error);
                alert('Ошибка при сохранении псевдонимов.');
            }
        }


        async function saveChanges() {
            const dataToSend = {
                updated: updatedData,
                deleted: deletedData,
            };

            try {
                await fetch('/brands/update', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(dataToSend),
                });
                alert('Изменения успешно сохранены!');
            } catch (error) {
                console.error('Ошибка при сохранении данных:', error);
                alert('Ошибка при сохранении изменений.');
            }
        }

        function resetAliases(recordId) {
            const aliasList = document.getElementById(`alias-list-${recordId}`);
            aliasList.innerHTML = '';

            const aliases = originalData[recordId]?.aliases || [];

            aliases.forEach(alias => {
                const listItem = document.createElement('li');
                listItem.className = 'alias-item d-flex justify-content-between align-items-center';
                listItem.innerHTML = `
                    <span>${alias.toUpperCase()}</span>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteAlias(${recordId}, '${alias}')">-</button>
                `;
                aliasList.appendChild(listItem);
            });
        }

        document.addEventListener("DOMContentLoaded", fetchBrands);
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
