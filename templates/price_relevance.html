<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Актуальность прайс-листов</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: var(--container-bg);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        .return-button {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            background-color: #4a76a8;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .refresh-button {
            margin-left: 10px;
            padding: 5px 15px;
            background-color: #4a76a8;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .refresh-button:disabled {
            background-color: #cccccc;
        }
        .price-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .price-table th, .price-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .price-table th {
            background-color: #4a76a8;
            color: white;
            cursor: pointer;
            position: relative; /* For icon positioning */
        }
        .price-table th .sort-icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            margin-left: 8px;
            vertical-align: middle;
            opacity: 0.6;
            transition: opacity 0.2s;
        }
        .price-table th:hover .sort-icon {
            opacity: 1;
        }
        .price-table th:hover {
            background-color: #3a6698;
        }
        .price-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        #scrollTopBtn {
            position: fixed;
            bottom: 20px;
            right: 30px;
            z-index: 99;
            border: none;
            outline: none;
            background-color: #4a76a8;
            color: white;
            cursor: pointer;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            transition: opacity 0.4s, visibility 0.4s;
            display: flex;
            align-items: center;
            justify-content: center;
            /* Start hidden */
            opacity: 0;
            visibility: hidden;
        }

        #scrollTopBtn.show {
            opacity: 1;
            visibility: visible;
        }

        #scrollTopBtn:hover {
            background-color: #3a6698;
        }
    </style>
</head>
<body>
    <div class="container" x-data="priceRelevance()">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
            {% include '_back_button.html' %}
            <button @click="refreshData()" id="refreshBtn" class="refresh-button" :disabled="loading">
                <span class="spinner-grow spinner-grow-sm d-none me-2" :class="{'d-none': !loading}" aria-hidden="true"></span>
                <span role="status" x-text="loading ? 'Обновление...' : 'Обновить данные'"></span>
            </button>
        </div>
        <h2>Актуальность прайс-листов поставщиков</h2>
        <table class="price-table" id="priceTable">
            <thead>
                <tr class="sticky-top">
                    <th @click="sortBy('supid')">ID поставщика <span class="sort-icon mb-3"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor"><path d="M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"/></svg></span></th>
                    <th @click="sortBy('sup_name')">Наименование поставщика <span class="sort-icon mb-3"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor"><path d="M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"/></svg></span></th>
                    <th @click="sortBy('dateupd')">Дата последнего прайса <span class="sort-icon mb-3"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor"><path d="M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"/></svg></span></th>
                    <th @click="sortBy('num_rows')">Количество строк в посл. прайсе <span class="sort-icon mb-3"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor"><path d="M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"/></svg></span></th>
                </tr>
            </thead>
            <tbody>
                <template x-for="(supplier, index) in sortedSuppliers" :key="index">
                    <tr>
                        <td x-text="supplier.supid"></td>
                        <td x-text="supplier.sup_name"></td>
                        <td x-text="new Date(supplier.dateupd).toLocaleDateString('ru-RU')"></td>
                        <td x-text="supplier.num_rows.toLocaleString()"></td>
                    </tr>
                </template>
            </tbody>
        </table>

        <button @click="scrollToTop()" id="scrollTopBtn" title="Наверх">&#8679;</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script>
        function priceRelevance() {
            return {
                suppliers: {{ suppliers|tojson|safe }},
                sortColumn: null,
                sortDirection: 'asc',
                loading: false,

                init() {
                    console.log('Alpine component initialized');
                    window.addEventListener('scroll', this.handleScroll.bind(this));
                },

                handleScroll() {
                    const scrollTopBtn = document.getElementById('scrollTopBtn');
                    if (window.scrollY > 200) {
                        scrollTopBtn.classList.add('show');
                    } else {
                        scrollTopBtn.classList.remove('show');
                    }
                },

                scrollToTop() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                },

                sortBy(column) {
                    if (this.sortColumn === column) {
                        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.sortColumn = column;
                        this.sortDirection = 'asc';
                    }
                },

                get sortedSuppliers() {
                    if (!this.sortColumn) {
                        return this.suppliers;
                    }

                    return [...this.suppliers].sort((a, b) => {
                        const aValue = a[this.sortColumn];
                        const bValue = b[this.sortColumn];

                        if (this.sortColumn === 'dateupd') {
                            const dateA = new Date(aValue).getTime();
                            const dateB = new Date(bValue).getTime();
                            if (this.sortDirection === 'asc') {
                                return dateA - dateB;
                            } else {
                                return dateB - dateA;
                            }
                        }

                        if (typeof aValue === 'string') {
                            return this.sortDirection === 'asc'
                                ? aValue.localeCompare(bValue)
                                : bValue.localeCompare(aValue);
                        } else {
                            return this.sortDirection === 'asc'
                                ? aValue - bValue
                                : bValue - aValue;
                        }
                    });
                },

                refreshData() {
                    this.loading = true;
                    fetch('/price_relevance/refresh')
                        .then(response => response.json())
                        .then(data => {
                            this.suppliers = data;
                        })
                        .catch(error => {
                            console.error('Error refreshing data:', error);
                            alert('Ошибка при обновлении данных');
                        })
                        .finally(() => {
                            this.loading = false;
                        });
                }
            }
        }
    </script>
</body>
</html>