<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demand Report</title>
    <style>
        body {
            font-family: '<PERSON>o', sans-serif;
            background-color: #e8f1f5; /* Бледно-голубой фон */
            color: #2e3a4e; /* Темно-синий текст */
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            transition: all 0.3s ease;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            background-color: #f9fcff; /* Белый с легким голубым оттенком */
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            border: 2px solid #89a9c2; /* Мягкая голубая рамка */
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-align: center;
            color: #4196e6; /* Голубой цвет заголовка */
            text-transform: uppercase;
            border-bottom: 2px solid #89a9c2; /* Голубая линия под заголовком */
            padding-bottom: 10px;
        }

        h2 {
            font-size: 1.8em;
            margin-bottom: 15px;
            color: #4196e6; /* Голубой цвет подзаголовка */
            border-left: 5px solid #89a9c2; /* Голубая вертикальная линия */
            padding-left: 15px;
        }

        .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            margin-right: 10px;
            background-color: #f0f8ff; /* Очень светлый голубой */
            border: 1px solid #89a9c2;
            border-radius: 5px;
            color: #2e3a4e;
            font-size: 1em;
        }

        .form-group button {
            padding: 10px 15px;
            background-color: #41729f; /* Основной голубой цвет */
            border: none;
            border-radius: 5px;
            color: #ffffff;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        .form-group button:hover {
            background-color: #2e5777; /* Более темный оттенок для кнопки при наведении */
        }

        .result {
            margin-top: 15px;
            font-size: 1em;
        }

        .error {
            color: #d32f2f;
        }

        .hidden {
            display: none;
        }

        .add-brand-btn, .generate-btn {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background-color: #076bc9;
            border: none;
            border-radius: 5px;
            color: #ffffff;
            cursor: pointer;
            font-size: 1.2em;
            transition: background-color 0.3s ease;
        }

        .add-brand-btn:hover, .generate-btn:hover {
            background-color: #2e5777;
        }

        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 10px;
            background-color: #09d65e; /* Светлый голубой цвет для ссылок */
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        .download-link:hover {
            background-color: #3aca59; /* Темный оттенок при наведении */
        }

        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #6ac7f1; /* Голубой цвет загрузчика */
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
            margin-left: 10px;
            display: inline-block;
            vertical-align: middle;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.5em;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
            }

            .form-group input {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .add-brand-btn, .generate-btn {
                font-size: 1em;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 1.5em;
            }

            h2 {
                font-size: 1.2em;
            }

            .add-brand-btn, .generate-btn {
                font-size: 0.9em;
            }
        }
    </style>
    <script>
        // JavaScript код остается без изменений
        async function submitFilteredDemand(formId, endpoint) {
            const form = document.getElementById(formId);
            const brands = Array.from(form.querySelectorAll('.brand-input')).map(input => input.value).filter(val => val);
            if (brands.length === 0) {
                alert("Please add at least one brand.");
                return;
            }

            const loader = document.createElement('div');
            loader.className = 'loader';
            form.querySelector('.generate-btn').after(loader);

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ brands })
            });

            loader.remove();
            const result = await response.json();
            const resultContainer = form.querySelector('.result');

            if (result.error === false) {
                resultContainer.innerHTML = `<a href="${result.url}" download class="download-link">Download File</a>`;
            } else {
                resultContainer.innerHTML = `<span class="error">${result.error}</span>`;
            }
        }

        async function submitDemand(endpoint, resultId) {
            const button = document.querySelector(`#${resultId}`).previousElementSibling;
            const loader = document.createElement('div');
            loader.className = 'loader';
            button.after(loader);

            const response = await fetch(endpoint, {
                method: 'POST'
            });

            loader.remove();
            const result = await response.json();
            const resultContainer = document.getElementById(resultId);

            if (result.error === false) {
                resultContainer.innerHTML = `<a href="${result.url}" download class="download-link">Download File</a>`;
            } else {
                resultContainer.innerHTML = `<span class="error">${result.error}</span>`;
            }
        }

        function addBrandInput(formId) {
            const form = document.getElementById(formId);
            const inputGroup = document.createElement('div');
            inputGroup.classList.add('form-group');
            inputGroup.innerHTML = `
                <input type="text" class="brand-input" placeholder="Brand Name" required>
                <button type="button" onclick="this.parentElement.remove()">Remove</button>
            `;
            form.querySelector('.brand-inputs').appendChild(inputGroup);
        }
    </script>
</head>
<body>

<div class="container">
    {% if demand_type == 'dif' %}
        <h1>Генерация потребности по DIF</h1>

        <!-- Block 1: DIF with filters -->
        <div>
            <h2>Генерация потребности по DIF с фильтрами по брендам</h2>
            <form id="dif-filtered-form">
                <div class="brand-inputs">
                    <div class="form-group">
                        <input type="text" class="brand-input" placeholder="Brand Name" required>
                        <button type="button" onclick="this.parentElement.remove()">Remove</button>
                    </div>
                </div>
                <button type="button" class="add-brand-btn" onclick="addBrandInput('dif-filtered-form')">Add Brand</button>
                <button type="button" class="generate-btn" onclick="submitFilteredDemand('dif-filtered-form', '/get_demand_filtered?demand_type=dif')">Generate Demand</button>
                <div class="result"></div>
            </form>
        </div>

        <!-- Block 2: DIF original brands -->
        <div>
            <h2>Генерация потребности DIF по оригинальным брендам</h2>
            <button type="button" class="generate-btn" onclick="submitDemand('/get_demand?demand_type=dif', 'dif-original-result')">Generate Demand</button>
            <div id="dif-original-result" class="result"></div>
        </div>

    {% elif demand_type == 'emex' %}
        <h1>EMEX Формирование потребности</h1>

        <!-- Block 1: EMEX with filters -->
        <div>
            <h2>Сгенерировать потребность EMEX с фильтром по брендам</h2>
            <form id="emex-filtered-form">
                <div class="brand-inputs">
                    <div class="form-group">
                        <input type="text" class="brand-input" placeholder="Brand Name" required>
                        <button type="button" onclick="this.parentElement.remove()">Remove</button>
                    </div>
                </div>
                <button type="button" class="add-brand-btn" onclick="addBrandInput('emex-filtered-form')">Add Brand</button>
                <button type="button" class="generate-btn" onclick="submitFilteredDemand('emex-filtered-form', '/get_demand_filtered?demand_type=emex')">Generate Demand</button>
                <div class="result"></div>
                <button type="button" class="generate-btn"
                        onclick="submitDemand('/get_check_demand_emex?type=not_original', 'emex-not_original-months')">
                    Generating a check
                </button>
                <div id="emex-not_original-months" class="result"></div>
            </form>
        </div>

        <!-- Block 2: EMEX original brands -->
        <div>
            <h2>Генерация потребности EMEX по оригинальным брендам</h2>
            <button type="button" class="generate-btn" onclick="submitDemand('/get_demand?demand_type=emex', 'emex-original-result')">Generate Demand</button>
            <div id="emex-original-result" class="result"></div>
            <button type="button" class="generate-btn" onclick="submitDemand('/get_check_demand_emex?type=original', 'emex-original-months')">Generating a check</button>
            <div id="emex-original-months" class="result"></div>
        </div>

    {% else %}
        <p>Invalid demand type specified.</p>
    {% endif %}
</div>

</body>
</html>