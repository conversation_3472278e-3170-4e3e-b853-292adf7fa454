<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Загрузка файла</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <style>
        /* Общие стили */
        body {
            font-family: Arial, sans-serif;
            font-size: 18px;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
            overflow-y: hidden;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: var(--container-bg);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            transition: background-color 0.3s ease;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .excluded-providers {
            margin-top: 10px;
            max-height: 150px; /* Добавлен скролл для исключённых поставщиков */
            overflow-y: auto; /* Вертикальный скролл */
            border: 1px solid #ddd;
            padding: 10px;
            background: #fff;
            border-radius: 5px;
        }
        #dynamicForms {
            max-height: 300px; /* Добавлен скролл для секции форм */
            overflow-y: auto; /* Вертикальный скролл */
        }
        .form-group label {
            display: block;
            margin-bottom: 10px;
        }
        .form-group input[type="file"],
        .form-group select,
        .form-group input[type="number"] {
            display: block;
            width: 100%;
            padding: 10px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            border: none;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .form-group button:hover {
            background-color: #0056b3;
        }
        .theme-toggle {
        position: fixed;
        top: 10px;
        right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000; /* Поместить выше других элементов */
        }
        .theme-toggle input[type="checkbox"] {
            display: none;
        }
        .theme-toggle label {
            cursor: pointer;
            font-size: 24px;
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            color: var(--toggle-color);
            transition: color 0.3s ease;
        }
        .theme-toggle label:hover {
            color: var(--toggle-hover-color);
        }
        #responseMessage {
            margin-top: 20px;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        .loader {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            margin-top: 20px;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        :root {
            --bg-color-light: #f4f4f4;
            --text-color-light: #333;
            --container-bg-light: #fff;
            --toggle-color-light: #f1c40f;
            --toggle-hover-color-light: #e67e22;

            --bg-color-dark: #333;
            --text-color-dark: #f4f4f4;
            --container-bg-dark: #444;
            --toggle-color-dark: #f39c12;
            --toggle-hover-color-dark: #e74c3c;
        }

        body {
            font-family: 'Arial', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }

        body.light-theme {
            --bg-color: var(--bg-color-light);
            --text-color: var(--text-color-light);
            --container-bg: var(--container-bg-light);
            --toggle-color: var(--toggle-color-light);
            --toggle-hover-color: var(--toggle-hover-color-light);
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: var(--container-bg);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            transition: background-color 0.3s ease;
            max-height: calc(100vh - 40px); /* Высота контейнера, с учетом отступов */
            overflow-y: auto; /* Добавляет вертикальный скролл, если контент превышает высоту */
    }

        body.dark-theme {
            --bg-color: var(--bg-color-dark);
            --text-color: var(--text-color-dark);
            --container-bg: var(--container-bg-dark);
            --toggle-color: var(--toggle-color-dark);
            --toggle-hover-color: var(--toggle-hover-color-dark);
        }

        .instructions {
            margin-top: 10px;
            font-size: 16px;
            color: #666;
        }
        .error-message {
            color: red;
            font-size: 16px;
            margin-top: 10px;
        }
        .provider-list {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        }
        .provider-list input {
            flex: 1;
            padding: 8px;
            font-size: 16px;
        }
        .provider-list button {
            margin-left: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
        }
        .provider-list button:hover {
            background-color: #c82333;
        }
        .provider-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 5px;
        width: 100%;
        box-sizing: border-box;
    }

        .provider-item span {
            flex-grow: 1;
        }

        .provider-item button {
            margin-left: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
        }
        #platformFiltersContainer {
            max-height: 300px;
            overflow-y: auto; /* Добавлен вертикальный скролл */
        }
        @media (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .form-group input,
            .form-group select,
            .form-group button {
                font-size: 14px;
            }
            .theme-toggle label {
                font-size: 20px;
            }
            .loader {
                width: 40px;
                height: 40px;
            }
        }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
</style>
</head>
<body>

<div class="container">
    <h2>Загрузка файла</h2>
    <form id="uploadForm">
        <div class="form-group">
            <label for="platforms">Выберите площадки:</label>
            <select id="platforms" name="platforms" multiple="multiple">
                <option value="Cross-doc">Cross doc</option>
                <option value="Emex">Emex</option>
                <option value="Part-kom">Part-kom</option>
            </select>
        </div>
        <div class="form-group">
            <label for="fileInput">Выберите файл:</label>
            <input type="file" id="fileInput" accept=".xls, .xlsx, .csv">
        </div>
        <!-- Динамически добавляемые фильтры для площадок -->
        <div id="platformFiltersContainer"></div>
        <div class="form-group">
            <label>Объединение минимумов:</label>
            <input type="checkbox" id="exportAll" name="exportAll">
        </div>
        <p class="instructions">
            * Файл загрузки должен иметь шапку: 1 - Бренд, 2 - Артикул. Формат .xlsx или .csv
        </p>
        <div id="errorMessages" class="error-message"></div>
        <div class="form-group">
            <button type="submit">Загрузить</button>
        </div>
    </form>
    <div id="responseMessage">Идёт парсинг и обработка...</div>
    <div class="loader"></div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
<script>
     $(document).ready(function() {
        $('#platforms').select2();
        const platformFiltersContainer = $('#platformFiltersContainer');

        // Площадки по умолчанию
        const defaultPlatforms = ['Cross-doc', 'Emex', 'Part-kom'];
        $('#platforms').val(defaultPlatforms).trigger('change');

        function updateFilters() {
            const selectedPlatforms = $('#platforms').val() || [];

            // Удаляем старые поля, если площадка была убрана
            platformFiltersContainer.children('.form-group').each(function() {
                const platform = $(this).attr('id').replace('-filter', '');
                if (!selectedPlatforms.includes(platform)) {
                    $(this).remove();
                }
            });

            // Добавляем новые поля percent
            selectedPlatforms.forEach(platform => {
                if (!$(`#${platform}-filter`).length) {
                    platformFiltersContainer.append(`
                        <div class="form-group" id="${platform}-filter">
                            <label for="${platform}-percent">Процент выбросов для ${platform} (2 > 1 * value):</label>
                            <input type="number" id="${platform}-percent" name="${platform}-percent" value="1.25" min="1.01" max="100.00" step="0.01">
                        </div>
                    `);
                }
            });
        }

        // Обновляем фильтры при изменении выбора площадок
        $('#platforms').on('change', updateFilters);

        // Вызываем обновление фильтров сразу при загрузке страницы
        updateFilters();

        $('#uploadForm').on('submit', function(event) {
            event.preventDefault();

            const fileInput = document.getElementById('fileInput');
            const errorMessages = document.getElementById('errorMessages');
            errorMessages.innerHTML = '';

            if (!fileInput.files.length) {
                errorMessages.innerHTML = 'Пожалуйста, выберите файл для загрузки.';
                return;
            }

            const file = fileInput.files[0];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!['xlsx', 'csv'].includes(fileExtension)) {
                errorMessages.innerHTML = 'Пожалуйста, выберите файл в формате .xlsx или .csv.';
                return;
            }

            const platforms = $('#platforms').val();
            const exportAll = document.getElementById('exportAll').checked;

            // Собираем percent для каждой площадки
            const platformFilters = {};
            platforms.forEach(platform => {
                platformFilters[platform] = parseFloat($(`#${platform}-percent`).val()) || 25;
            });

            const formData = new FormData();
            formData.append('excelFile', file);
            formData.append('platforms', JSON.stringify(platforms));
            formData.append('filters', JSON.stringify(platformFilters));
            formData.append('exportAll', exportAll);

            showLoading(true);

            fetch('/upload_file_history', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (!data.error) {
                    window.location.href = 'task_status_parser_history?auto=false';
                } else {
                    errorMessages.innerHTML = `Ошибка: ${data.error}`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showLoading(false);
                errorMessages.innerHTML = 'Произошла ошибка при загрузке файла.';
            });
        });

        function showLoading(isLoading) {
            const loader = document.querySelector('.loader');
            const responseMessage = document.getElementById('responseMessage');

            loader.style.display = isLoading ? 'block' : 'none';
            responseMessage.style.display = isLoading ? 'none' : 'block';
        }
    });

    
</script>

</body>
</html>
