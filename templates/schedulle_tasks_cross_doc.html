<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tasks Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: #444;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 12px;
            text-align: left;
        }

        th {
            background-color: #007BFF;
            color: #fff;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .loading {
            text-align: center;
            display: none;
        }

        .download-button {
            background-color: #28a745;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            text-align: center;
        }

        .download-button:hover {
            background-color: #218838;
        }
        .return-button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007BFF;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            margin: 5px 0 0 5px;
        }
        .return-button:hover {
            background-color: #0056b3;
        }
    </style>
    <script>
        async function handleAction(taskId, action) {
        const loading = document.getElementById('loading');
        loading.style.display = 'block';

        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete this task?')) return;

            try {
                const response = await fetch(`/schedulle_tasks_cross_doc/${taskId}`, {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();
                if (response.ok) {
                    document.getElementById(`task-${taskId}`).remove();
                    alert('Task deleted successfully');
                } else {
                    alert(`Error: ${result.error}`);
                }
            } catch (error) {
                alert('An unexpected error occurred.');
            } finally {
                loading.style.display = 'none';
            }
        } else if (action === 'start') {
            try {
                const response = await fetch(`/start_task_now_cross_doc/${taskId}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                if (response.ok) {
                    window.location.href = `/cross_dock/tasks?auto=true`;
                } else {
                    alert('Не удалось запустить парсинг');
                }
            } catch (error) {
                alert('Не удалось запустить парсинг');
            } finally {
                loading.style.display = 'none';
            }
        } else if (action === 'history') {
            window.location.href = `/task_history_cross_doc/${taskId}`;
        } else if (action === 'update') {
            window.location.href = `/update_percentage_parser/${taskId}`;
        }
}
    </script>
</head>
{% include '_back_button.html' %}
<body>
    <div class="container">
        <h1>Управление задачами Кросс Док</h1>
        <div id="loading" class="loading">Processing...</div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Имя файла</th>
                    <th>Исходник</th>
                    <th>Группа поставщиков</th>
                    <th>Тип</th>
                    <th>Дата создания</th>
                    <th>Действия</th>
                </tr>
            </thead>
            <tbody>
                {% for task in tasks_info %}
                <tr id="task-{{ task.id }}">
                    <td>{{ task.id }}</td>
                    <td>{{ task.filename }}</td>
                    <td>
                        <a href="{{ task.file_url }}" class="download-button" download>Скачать исходник</a>
                    </td>
                    <td>{{ task.settings.platforms[0] }}</td>
                    <td>{{ task.type }}</td>
                    <td>{{ task.created }}</td>
                    <td>
                        <select onchange="handleAction({{ task.id }}, this.value)">
                            <option value="">Выберите действие</option>
                            <option value="delete">Удалить</option>
                            <option value="start">Запустить</option>
                            <option value="update">Изменить</option>
                            <option value="history">Посмотреть историю</option>
                        </select>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>
