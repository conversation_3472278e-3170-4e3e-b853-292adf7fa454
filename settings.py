import datetime
import json
from urllib.parse import urlparse

import pandas as pd
from clickhouse_driver import Client
from flask import Blueprint, render_template, request, jsonify
from flask_jwt_extended import jwt_required
from loguru import logger

from func_for_async import (
    check_emex_file,
    save_emex_file,
    download_summary_sups,
    get_sup_lists,
    validate_emex_chunk,
)
from services.class_queries import ClicData
from services.config_info import ClickInfo

settings_bp = Blueprint("settings", __name__)


@settings_bp.route("/settings", methods=["GET"])
@jwt_required()
async def settings():
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
    )
    client = ClicData(client=client, settings={}, filters={})
    sup_lists = await get_sup_lists(client=client)
    return render_template("settings.html", sup_lists=sup_lists)


@settings_bp.route("/upload_txt_emex_validate", methods=["POST"])
@jwt_required()
def upload_txt_emex_validate():
    chunk = request.data
    if not chunk:
        return jsonify({"success": False, "error": "Проверочный чанк пуст."}), 400
    result = validate_emex_chunk(chunk)
    return jsonify(result)


@settings_bp.route("/upload_txt_emex_check", methods=["POST"])
@jwt_required()
async def upload_txt_emex_check():
    logger.info("Начало проверки файла Emex.")
    if "txtFile" not in request.files:
        return jsonify({"error": "Файл не найден."}), 400
    file = request.files["txtFile"]
    if not file.filename:
        return jsonify({"error": "Файл не выбран."}), 400

    logger.info(f"Чтение файла: {file.filename}")
    df = pd.read_csv(file, sep="\t", encoding="cp1251")
    df = df.fillna("")
    df["Дата"] = pd.to_datetime(df["Дата"])
    df["Дата"] = df["Дата"].dt.date
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
    )
    client = ClicData(client=client, settings={}, filters={})
    new_records, last_month_discrepancies = await check_emex_file(
        client=client, df_new=df
    )
    new_records_count = new_records.shape[0]
    last_month_discrepancies_count = last_month_discrepancies.shape[0]
    logger.info(f"Найдено {new_records_count} новых записей.")
    logger.info(
        f"Найдено {last_month_discrepancies_count} новых записей за предыдущий месяц."
    )
    logger.info("Проверка файла Emex завершена.")
    return jsonify(
        {
            "result": {
                "Количество новых записей": new_records_count,
                "Количество новых записей за прошлый месяц": last_month_discrepancies_count,
            }
        }
    )



@settings_bp.route("/upload_txt_emex_save", methods=["POST"])
@jwt_required()
async def upload_txt_emex_save():
    logger.info("Начало сохранения файла Emex.")
    if "txtFile" not in request.files:
        logger.error("Файл не найден в запросе.")
        return jsonify({"error": "Файл не найден."}), 400
    file = request.files["txtFile"]
    if not file.filename:
        logger.error("Файл не выбран.")
        return jsonify({"error": "Файл не выбран."}), 400

    client_ = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
        connect_timeout=99999,
        send_receive_timeout=99999,
    )
    client = ClicData(client=client_, settings={}, filters={})
    current_year = datetime.datetime.today().year
    current_month = datetime.datetime.today().month

    check_query = f"""
        SELECT upload 
        FROM sup_stat.upload_tracker 
        WHERE year = {current_year} AND month = {current_month}
    """
    upload_status = client_.execute(check_query)
    if upload_status and upload_status[0][0]:
        logger.warning(f"Файл Emex уже был загружен в {current_month}/{current_year}.")
        return jsonify({"Ошибка": "Файл уже был загружен в этом месяце"})

    logger.info(f"Чтение файла: {file.filename}")
    df = pd.read_csv(file, sep="\t", encoding="cp1251")
    result = await save_emex_file(client=client, df_new=df)
    final_result = await result
    logger.info(f"Сохранение файла Emex успешно завершено. Результат: {final_result}")
    return jsonify({"result": final_result})


@settings_bp.route("/download_suppliers_summary_excel", methods=["POST"])
@jwt_required()
async def download_suppliers_summary_excel():
    data = json.loads(request.data)
    brands = data["brands"]
    originalBrandsOnly = data["originalBrandsOnly"]
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
    )
    settings = {"brands": brands, "OriginalBrandsOnly": originalBrandsOnly}
    client = ClicData(client=client, settings=settings, filters={})
    result = await download_summary_sups(client=client)
    o = urlparse(request.base_url)
    if result["url"] is not None:
        result["url"] = f'http://{o.hostname}:8060/static/{result["url"]}'
    return jsonify(result)