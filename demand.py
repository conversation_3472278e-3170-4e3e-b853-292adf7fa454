from os import environ
from urllib.parse import urlparse

import pandas as pd
from clickhouse_driver import Client
from flask import Blueprint, render_template, jsonify, request, redirect, url_for
from flask_jwt_extended import jwt_required
from loguru import logger
from werkzeug.utils import secure_filename

from func_for_async import (
    get_sup_lists,
    get_demand_emex_brands,
    get_demand_dif_brands,
    get_demand_emex_not_filtered,
    get_demand_dif_not_filtered,
    get_interval_to_original_months_emex,
    get_consolidated_demand_with_filters,
)
from services.class_queries import ClicData
from services.config_info import ClickInfo

demand_bp = Blueprint("demand", __name__)


@demand_bp.route("/generate_demand", methods=["GET"])
@jwt_required()
async def demand_report():
    logger.info("Генерация отчета о потребности.")
    demand_type = request.args.get("demand_type")
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings={}, filters={})
    access_token_cookie = request.cookies.get("access_token_cookie")
    refresh_token_cookie = request.cookies.get("access_token_cookie")

    if not access_token_cookie or not refresh_token_cookie:
        logger.warning("Отсутствуют токены доступа, перенаправление на страницу входа.")
        return redirect(url_for("auth.login"))

    sups = list(await get_sup_lists(client))
    if sups is None:
        logger.error("Не удалось получить списки поставщиков.")
        return redirect(url_for("auth.login"))
    logger.debug(f"Списки поставщиков: {sups}")
    return render_template("demand_type.html", demand_type=demand_type)


@demand_bp.route("/get_demand", methods=["POST"])
@jwt_required()
async def get_demand_report_not_filtered():
    demand_type = request.args.get("demand_type")
    logger.info(f"Получение отчета о потребности (без фильтров) для типа: {demand_type}")
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings={}, filters={})
    if demand_type == "emex":
        result = await get_demand_emex_not_filtered(client=client)
    else:
        result = await get_demand_dif_not_filtered(client=client)
    o = urlparse(request.base_url)
    if result["url"] is not None:
        result["url"] = f'http://{o.hostname}:8060/static/{result["url"]}'
    logger.success("Отчет о потребности (без фильтров) успешно создан.")
    return jsonify(result)


@demand_bp.route("/get_demand_filtered", methods=["POST"])
@jwt_required()
async def get_demand_report_filtered():
    demand_type = request.args.get("demand_type")
    brands = request.json.get("brands")
    logger.info(f"Получение отчета о потребности (с фильтрами) для типа: {demand_type}, бренды: {brands}")
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings={}, filters={})
    if demand_type == "emex":
        result = await get_demand_emex_brands(client=client, brands=brands)
    else:
        result = await get_demand_dif_brands(client=client, brands=brands)
    o = urlparse(request.base_url)
    if result["url"] is not None:
        result["url"] = f'http://{o.hostname}:8060/static/{result["url"]}'
    logger.success("Отчет о потребности (с фильтрами) успешно создан.")
    return jsonify(result)


@demand_bp.route("/get_check_demand_emex", methods=["POST"])
@jwt_required()
async def get_original_months_emex():
    type = request.args.get("type")
    logger.info(f"Получение оригинальных месяцев EMEX для типа: {type}")
    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    client = ClicData(client=client, settings={}, filters={})

    result = await get_interval_to_original_months_emex(client=client, type=type)

    o = urlparse(request.base_url)
    if result["url"] is not None:
        result["url"] = f'http://{o.hostname}:8060/static/{result["url"]}'
    logger.success("Оригинальные месяцы EMEX успешно получены.")
    return jsonify(result)


@demand_bp.route("/api/demand_report/generate", methods=["POST"])
@jwt_required()
async def generate_demand_report() -> "flask.wrappers.Response":
    logger.info("Запуск конечной точки generate_demand_report...")

    if "brands_file" not in request.files:
        logger.warning("Файл с брендами отсутствует в request.files")
        return (
            jsonify(
                {
                    "error": "Файл со списком брендов обязателен. Пожалуйста, загрузите файл (.txt, .csv, .xlsx) со списком брендов."
                }
            ),
            400,
        )

    file = request.files["brands_file"]
    if file.filename == "":
        logger.warning("Имя файла brands_file пустое")
        return jsonify({"error": "Файл не выбран. Пожалуйста, выберите файл со списком брендов."}), 400

    filename = secure_filename(file.filename)
    file_ext = filename.rsplit(".", 1)[-1].lower()
    brands: list[str] = []
    try:
        if file_ext == "txt":
            content = file.read().decode("utf-8")
            brands = [line.strip() for line in content.splitlines() if line.strip()]
            logger.info(f"Генерация отчета из текстового файла с {len(brands)} брендами: {brands}")
        elif file_ext == "csv":
            df = pd.read_csv(file, header=None)
            if not df.empty and df.shape[1] > 0:
                 brands = df.iloc[:, 0].dropna().astype(str).str.strip().unique().tolist()
                 logger.info(f"Генерация отчета из CSV-файла с {len(brands)} брендами: {brands}")
            else:
                 logger.warning("CSV-файл пуст или не содержит столбцов")
                 return jsonify({"error": "CSV файл пуст или не содержит данных."}), 400

        elif file_ext in ("xlsx", "xls"):
            df = pd.read_excel(file, header=None)
            if not df.empty and df.shape[1] > 0:
                 brands = df.iloc[:, 0].dropna().astype(str).str.strip().unique().tolist()
                 logger.info(f"Генерация отчета из Excel-файла с {len(brands)} брендами: {brands}")
            else:
                 logger.warning("Excel-файл пуст или не содержит столбцов")
                 return jsonify({"error": "Excel файл пуст или не содержит данных."}), 400
        else:
            logger.warning(f"Неподдерживаемое расширение файла: {file_ext}")
            return (
                jsonify({"error": "Неподдерживаемый формат файла. Пожалуйста, загрузите .txt, .csv или .xlsx файл."}),
                400,
            )
    except Exception as e:
        logger.exception(f"Ошибка при обработке файла: {str(e)}")
        return jsonify({"error": f"Ошибка при обработке файла: {str(e)}"}), 400

    if not brands:
        logger.warning("Бренды из файла не извлечены")
        return jsonify({"error": "Список брендов пуст или не удалось извлечь бренды из файла."}), 400

    try:
        k_filter = float(request.form.get("k_filter", 1.0))
    except ValueError:
        logger.warning(f"Неверное значение k_filter: {request.form.get('k_filter')}")
        return jsonify({"error": "Неверное значение для коэффициента регулярности продаж (k_). Пожалуйста, введите число."}), 400

    start_date_str = request.form.get("start_date")
    end_date_str = request.form.get("end_date")

    selected_supids_str = request.form.get("selected_supids", "")
    selected_supids: list[int] | None = None
    if selected_supids_str:
        try:
            selected_supids = [int(sid.strip()) for sid in selected_supids_str.split(",") if sid.strip()]
            selected_supids = [sid for sid in selected_supids if isinstance(sid, int)]
            if not selected_supids:
                 logger.warning("Список выбранных ID поставщиков пуст после парсинга")
                 selected_supids = None
            else:
                 logger.info(f"Получены выбранные ID поставщиков: {selected_supids}")

        except ValueError:
            logger.warning(f"Неверный формат selected_supids: {selected_supids_str}")
            selected_supids = None
    else:
        logger.info("Выбранные ID поставщиков не получены.")


    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    clic_data_instance = ClicData(client=client, settings={}, filters={})

    result = await get_consolidated_demand_with_filters(
        clic_data_instance,
        brands=brands,
        k_filter=k_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        selected_supids=selected_supids,
    )

    o = urlparse(request.base_url)
    port = environ.get("PORT_NGINX")
    if not port:
        if o.scheme == "https":
            port = "443"
        else:
            port = "80"

    if result.get("url") is not None:
         if (o.scheme == "http" and port != "80") or (o.scheme == "https" and port != "443") or port == "8060":
              result["url"] = f'{o.scheme}://{o.hostname}:{port}/static/{result["url"]}'
         else:
               result["url"] = f'{o.scheme}://{o.hostname}/static/{result["url"]}'


    logger.info("Запрос на генерацию отчета обработан.")
    return jsonify(result)


@demand_bp.route("/api/demand_report/suppliers_rating", methods=["POST"])
@jwt_required()
async def suppliers_rating() -> "flask.wrappers.Response":
    logger.info("Запуск конечной точки suppliers_rating...")
    if "brands_file" not in request.files:
        logger.warning("Файл с брендами отсутствует в request.files")
        return (
            jsonify(
                {
                    "error": "Файл с брендами обязателен. Пожалуйста, загрузите файл (.txt, .csv, .xlsx) со списком брендов."
                }
            ),
            400,
        )

    file = request.files["brands_file"]
    if file.filename == "":
        logger.warning("Имя файла brands_file пустое")
        return jsonify({"error": "Файл не выбран. Пожалуйста, выберите файл со списком брендов."}), 400

    filename = secure_filename(file.filename)
    file_ext = filename.rsplit(".", 1)[-1].lower()
    brands: list[str] = []
    try:
        if file_ext == "txt":
            content = file.read().decode("utf-8")
            brands = [line.strip() for line in content.splitlines() if line.strip()]
            logger.info(f"Найдено {len(brands)} брендов в текстовом файле: {brands}")
        elif file_ext == "csv":
            df = pd.read_csv(file, header=None)
            if not df.empty and df.shape[1] > 0:
                 brands = df.iloc[:, 0].dropna().astype(str).str.strip().unique().tolist()
                 logger.info(f"Найдено {len(brands)} брендов в CSV-файле: {brands}")
            else:
                 logger.warning("CSV-файл пуст или не содержит столбцов")
                 return jsonify({"error": "CSV файл пуст или не содержит данных."}), 400
        elif file_ext in ("xlsx", "xls"):
            df = pd.read_excel(file, header=None)
            if not df.empty and df.shape[1] > 0:
                 brands = df.iloc[:, 0].dropna().astype(str).str.strip().unique().tolist()
                 logger.info(f"Найдено {len(brands)} брендов в Excel-файле: {brands}")
            else:
                 logger.warning("Excel-файл пуст или не содержит столбцов")
                 return jsonify({"error": "Excel файл пуст или не содержит данных."}), 400
        else:
            logger.warning(f"Неподдерживаемое расширение файла: {file_ext}")
            return (
                jsonify({"error": "Неподдерживаемый формат файла. Пожалуйста, загрузите .txt, .csv или .xlsx файл."}),
                400,
            )
    except Exception as e:
        logger.exception(f"Ошибка при обработке файла: {str(e)}")
        return jsonify({"error": f"Ошибка при обработке файла: {str(e)}"}), 400

    if not brands:
        logger.warning("Бренды из файла не извлечены")
        return jsonify({"error": "Список брендов пуст или не удалось извлечь бренды из файла."}), 400

    start_date = request.form.get("start_date")
    end_date = request.form.get("end_date")
    if not start_date or not end_date:
        logger.warning("Отсутствуют start_date или end_date в данных формы")
        return jsonify({"error": "Не указаны даты начала и окончания."}), 400

    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])
    clic_data_instance = ClicData(client=client, settings={}, filters={})

    brands_tuple = tuple(clic_data_instance._normalize_brands(brands))

    if not brands_tuple:
        logger.warning("Нет брендов после нормализации")
        return jsonify({"error": "Список брендов пуст после нормализации."}), 400

    logger.info(f"Разобранные бренды (нормализованные): {brands_tuple}")
    logger.info(f"Диапазон дат: {start_date} - {end_date}")

    client = Client(ClickInfo.host, user=ClickInfo.settings["user"], password=ClickInfo.settings["password"])

    try:
        query = """
        SELECT
            dif.supid,
            sl.name,
            countIf(
                NOT f0
                AND NOT f1
                AND NOT f3
                AND NOT f4
                AND NOT f5
                AND NOT f6
                AND NOT f7
                AND NOT f8
                AND NOT f9
                AND NOT f10
            ) AS count_no_flags,
            countIf(
                NOT f0
            ) AS count_sales,
            round(
                  if(count_sales = 0, 0, (count_no_flags / count_sales) * 100),
                  2
                ) AS sup_rating
        FROM dif.dif_step_3 AS dif
        JOIN sup_stat.sup_list AS sl
            ON dif.supid = sl.dif_id
        WHERE
            dif.dateupd BETWEEN %(start_date)s AND %(end_date)s
            AND dif.b IN %(brands)s
        GROUP BY
            dif.supid,
            sl.name
        ORDER BY sl.name;
        """

        logger.info(f"Фильтры для рейтинга: {start_date} - {end_date}, бренды: {brands_tuple}")

        ratings_rows = client.execute(query, {"start_date": start_date, "end_date": end_date, "brands": brands_tuple})
        result = [{"supid": row[0], "name": row[1], "sup_rating": float(row[4])} for row in ratings_rows]
        logger.info(f"Рассчитаны рейтинги для {len(result)} поставщиков")
    except Exception as e:
        logger.exception(f"Ошибка при расчете рейтинга поставщиков: {str(e)}")
        return jsonify({"error": f"Ошибка при расчете рейтинга поставщиков: {str(e)}"}), 500

    if not result:
        logger.warning("Рейтинги не рассчитаны ни для одного поставщика")
        return jsonify({"error": "Не удалось рассчитать рейтинг ни для одного поставщика. Проверьте даты и бренды."}), 404

    response = {"suppliers": result}
    logger.info(f"Возвращено {len(result)} поставщиков с рейтингами")
    return jsonify(response)