# VGH Upload Functionality Plan

This document outlines the plan to implement the functionality for uploading a file with car parts information to the ClickHouse database.

## User Flow

1.  **UI Enhancement**: A new section "Обновить базу ВГХ" will be added to the main page (`templates/index.html`).
2.  **Navigation**: Clicking the button will redirect the user to a new page for `item_catalog` updates.
3.  **Template Download**: The new page will provide a downloadable `.xlsx` template with the required columns.
4.  **File Upload**: The user will upload the filled-out `.xlsx` file.
5.  **Processing Modes**:
    - **Default Mode (Update/Insert)**: Updates existing records where values are `NULL` or empty and adds new records.
    - **Admin Mode (Overwrite)**: Overwrites existing records with data from the file.

## Development Plan

### Phase 1: Setup and UI

1.  **Create `item_catalog.py`**: This file will contain the Flask blueprint, routes, and logic for the new feature.
2.  **Create `templates/item_catalog_upload.html`**: This template will include the file upload form, a checkbox for admin mode, and a link to download the template file.
3.  **Modify `templates/index.html`**: Add a new widget to link to the upload page.
4.  **Register Blueprint**: Register the new blueprint in the main Flask application.
5.  **Create Excel Template**: Create the `item_catalog_template.xlsx` file that users can download.

### Phase 2: Test-Driven Development

1.  **Create `tests/test_item_catalog.py`**: This file will contain all the tests for the new functionality.
2.  **Write Tests**:
    - Test for the upload page route.
    - Test the template download route.
    - Test the "default" processing mode (update/insert).
    - Test the "admin" processing mode (overwrite).
    - Test with invalid file formats or incorrect data.

### Phase 3: Backend Implementation

1.  **Template Download Route**: Implement the endpoint to serve the `item_catalog_template.xlsx` file.
2.  **File Upload Route**: Implement the endpoint to handle file uploads.
3.  **File Processing Logic**:
    - Read the uploaded `.xlsx` file using `pandas`.
    - Validate the columns and data types.
4.  **Database Interaction**:
    - Implement the logic for both "default" and "admin" modes using the `clickhouse_connect` library.
    - For "default" mode, fetch existing data, merge with new data, and insert back.
    - For "admin" mode, directly insert the new data, letting `ReplacingMergeTree` handle overwrites.

### Phase 4: Integration and Refinement

1.  **UI/UX**: Ensure the new page is styled with Bootstrap and provides clear user feedback (e.g., success/error messages).
2.  **Code Quality**: Review the code for simplicity, clarity, and adherence to project conventions.
3.  **Documentation**: Add comments where necessary to explain complex logic.
