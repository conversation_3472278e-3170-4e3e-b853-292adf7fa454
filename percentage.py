import io
import json
import os
import re
from collections import OrderedDict
from datetime import datetime

import aiohttp
import pandas as pd
from cachetools import TTLCache
from clickhouse_driver import Client
from dotenv import load_dotenv
from flask import Blueprint, render_template, jsonify, request, redirect, url_for
from flask_jwt_extended import jwt_required
from loguru import logger
from pytz import timezone

from func_for_async import get_sup_lists
from services.class_queries import ClicData
from services.config_info import ClickInfo, ParserInfo
from services.config_info import SharedDirectoryHostInfo
from services.sets import week_days

load_dotenv()
moscow_tz = timezone("Europe/Moscow")
app_key = os.getenv("CLIENT_APP_KEY_ADMIN")
PORT_SCHED = os.getenv("PORT_SCHED")
percentage_bp = Blueprint("percentage", __name__)

tasks_cache = TTLCache(maxsize=100, ttl=3600000)  # 1000 hour cache


async def fetch_tasks_with_cache(auto, force_refresh=False):
    cache_key = f"tasks_{auto}"
    if not force_refresh and cache_key in tasks_cache:
        logger.info(f"Использование кэшированных данных для auto={auto}")
        return tasks_cache[cache_key]

    parser = ParserInfo().host
    flower_url = f"{parser}/tasks/cross_doc?auto={auto}"
    logger.info(f"Получение данных из: {flower_url}")

    async with aiohttp.ClientSession() as session:
        headers = {"app-key": app_key}
        start_time = datetime.now()
        async with session.get(flower_url, headers=headers) as response:
            if response.status != 200:
                logger.error(f"API вернуло ошибку: {response.status}")
                raise Exception(f"API вернуло код состояния {response.status}")

            tasks_info = await response.json()
            fetch_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Данные получены за {fetch_time:.2f} секунд. Найдено {len(tasks_info)} задач.")

            tasks_cache[cache_key] = tasks_info
            return tasks_info


def get_unique_brand_by_brand(client, brand):
    normalized_brand = str(brand).upper().replace(" ", "")
    query = f"""
        SELECT ub.brand
        FROM dif.unique_brands ub
        JOIN dif.zap_brand_cross zbc ON ub.id = zbc.parent_id
        WHERE UPPER(REPLACE(zbc.brand, ' ', '')) = '{normalized_brand}'
    """
    result = client.execute(query)
    if result:
        return result[0][0]
    return None


@percentage_bp.route("/percentage", methods=["GET"])
@jwt_required()
async def percentage():
    logger.info("Отображение страницы проценки.")
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
        connect_timeout=99999,
        send_receive_timeout=99999,
    )
    client = ClicData(client=client, settings={}, filters={})
    access_token_cookie = request.cookies.get("access_token_cookie")
    refresh_token_cookie = request.cookies.get("access_token_cookie")

    if not access_token_cookie or not refresh_token_cookie:
        logger.warning("Отсутствуют токены доступа, перенаправление на страницу входа.")
        return redirect(url_for("auth.login"))

    sups = list(await get_sup_lists(client))
    if sups is None:
        logger.error("Не удалось получить списки поставщиков.")
        return redirect(url_for("auth.login"))
    return render_template("percentage.html", sups=sups)


@percentage_bp.route("/get_percentage", methods=["POST"])
@jwt_required()
async def get_percentage():
    logger.info("Запрос на получение проценки.")
    file = request.files.get("file")
    platforms = [request.form.get("sups")]

    if not file:
        logger.warning("Файл не загружен.")
        return jsonify({"error": "Файл не загружен"}), 400

    filename = file.filename
    file_ext = filename.rsplit(".", 1)[1].lower()

    if file_ext == "xlsx":
        try:
            df = pd.read_excel(file, engine="openpyxl")
        except Exception as e:
            logger.error(f"Ошибка чтения файла XLSX: {e}")
            return jsonify({"error": f"Ошибка чтения файла: {str(e)}"}), 400
    elif file_ext == "xls":
        try:
            df = pd.read_excel(file, engine="xlrd")
        except Exception as e:
            logger.error(f"Ошибка чтения файла XLS: {e}")
            return jsonify({"error": f"Ошибка чтения файла: {str(e)}"}), 400
    elif file_ext == "csv":
        try:
            df = pd.read_csv(file, delimiter=";")
        except pd.errors.EmptyDataError:
            logger.error("Пустой CSV-файл.")
            return jsonify({"error": "Ошибка чтения файла: Прикрепите непустой файл формата .csv"}), 400
        except Exception as e:
            logger.error(f"Ошибка чтения файла CSV: {e}")
            return jsonify({"error": f"Ошибка чтения файла: {str(e)}"}), 400
    else:
        logger.warning(f"Неподдерживаемый формат файла: {file_ext}")
        return (
            jsonify(
                {"error": "Неподдерживаемый формат файла. Пожалуйста, прикрепите файл формата .xlsx, .xls или .csv"}
            ),
            400,
        )

    expected_columns = ["Бренд", "Артикул"]
    df = df.fillna("")

    unique_brands = df["Бренд"].unique()
    logger.debug(f"Уникальные бренды в файле: {unique_brands}")

    brand_mapping = {}
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
        connect_timeout=99999,
        send_receive_timeout=99999,
    )
    for brand in unique_brands:
        new_brand = get_unique_brand_by_brand(client, brand)
        if new_brand:
            brand_mapping[brand] = new_brand

    logger.debug(f"Сопоставление брендов: {brand_mapping}")

    df["Бренд"] = df["Бренд"].replace(brand_mapping.keys(), brand_mapping.values())
    df["Бренд"] = df["Бренд"].astype("string")
    df["Артикул"] = df["Артикул"].astype("string")
    df["Артикул"] = df["Артикул"].apply(lambda x: re.sub(r"[^a-zA-Z0-9]", "", x))
    actual_columns = df.columns.tolist()

    incorrect_columns = [
        expected_col for expected_col, actual_col in zip(expected_columns, actual_columns) if expected_col != actual_col
    ]
    if incorrect_columns:
        logger.warning(f"Неверные столбцы в файле: {incorrect_columns}")
        return (
            jsonify(
                {"error": f'Файл не соответствует шаблону. Неверная позиция поля(ей): {", ".join(incorrect_columns)}'}
            ),
            400,
        )
    if len(expected_columns) > len(actual_columns):
        logger.warning("В файле отсутствуют необходимые столбцы.")
        return jsonify({"error": f'Не хватает столбцов: {", ".join(set(expected_columns) - set(actual_columns))}'}), 400
    if len(expected_columns) < len(actual_columns):
        logger.warning("В файле присутствуют лишние столбцы.")
        return jsonify({"error": f'Лишние поля: {", ".join(set(actual_columns) - set(expected_columns))}'}), 400

    df_dict = df.to_dict(orient="records")
    df_json = json.dumps({"data": df_dict, "filename": filename, "platforms": platforms, "auto": False})
    parser = ParserInfo().host
    async with aiohttp.ClientSession() as session:
        headers = {"app-key": app_key}
        async with session.post(f"{parser}/start_percentage_cross", json=df_json, headers=headers) as response:
            response_json = await response.text()
            logger.debug(f"Ответ от сервиса парсера: {response_json}")
            logger.success("Запрос на процентку успешно отправлен.")
            return jsonify({"error": False}), 200


@percentage_bp.route("/task_status_cross_doc", methods=["GET"])
@jwt_required()
async def task_status_cross_doc():
    auto = request.args.get("auto")
    logger.info(f"Устаревший маршрут статуса задачи. Перенаправление на новый. Auto: {auto}")
    return redirect(url_for("percentage.cross_dock_tasks", auto=auto))


@percentage_bp.route("/cross_dock/tasks", methods=["GET"])
@jwt_required()
async def cross_dock_tasks():
    page = int(request.args.get("page", 1))
    limit = int(request.args.get("limit", 10))
    auto = request.args.get("auto", "false")
    force_refresh = (
        request.args.get("refresh", "0") == "1" or request.args.get("force_refresh", "false").lower() == "true"
    )

    logger.info(
        f"Запрос задач кросс-докинга. Страница: {page}, Лимит: {limit}, Auto: {auto}, Принудительное обновление: {force_refresh}"
    )

    is_htmx = request.headers.get("HX-Request") == "true"

    if is_htmx or request.args.get("format") == "json":
        try:
            try:
                tasks_info = await fetch_tasks_with_cache(auto, force_refresh)
            except Exception as e:
                logger.error(f"Ошибка при получении задач: {str(e)}")
                error_message = f"Ошибка при получении задач: {str(e)}"

                if request.args.get("format") == "json":
                    return jsonify({"error": error_message}), 500

                return render_template("cross_dock/error.html", error=error_message, auto=auto)

            task_ids = list(tasks_info.keys())
            unique_task_ids = set(task_ids)
            if len(task_ids) != len(unique_task_ids):
                duplicates = []
                seen = set()
                for task_id in task_ids:
                    if task_id in seen:
                        duplicates.append(task_id)
                    else:
                        seen.add(task_id)
                logger.warning(f"Найдено {len(duplicates)} дублирующихся ID задач в данных: {duplicates}")

            sort_start = datetime.now()
            sorted_task_ids = sorted(
                tasks_info,
                key=lambda k: (
                    datetime.strptime(tasks_info[k].get("received", ""), "%a, %d %b %Y %H:%M:%S GMT")
                    if tasks_info[k].get("received")
                    else datetime.min
                ),
                reverse=True,
            )

            sorted_tasks = OrderedDict((task_id, tasks_info[task_id]) for task_id in sorted_task_ids)
            sort_time = (datetime.now() - sort_start).total_seconds()
            logger.info(f"Данные отсортированы за {sort_time:.2f} секунд")

            start_idx = (page - 1) * limit
            end_idx = start_idx + limit
            paginated_tasks = dict(list(sorted_tasks.items())[start_idx:end_idx])

            total_tasks = len(sorted_tasks)
            total_pages = (total_tasks + limit - 1) // limit

            if request.args.get("format") == "json":
                return jsonify(
                    {
                        "tasks": paginated_tasks,
                        "pagination": {
                            "page": page,
                            "limit": limit,
                            "total_tasks": total_tasks,
                            "total_pages": total_pages,
                        },
                    }
                )

            logger.debug(f"Возвращено {len(paginated_tasks)} задач для страницы {page} из {total_pages}")

            return render_template(
                "cross_dock/task_rows.html",
                tasks_info=paginated_tasks,
                page=page,
                total_pages=total_pages,
                limit=limit,
                auto=auto,
            )
        except Exception as e:
            logger.error(f"Ошибка при получении задач: {str(e)}")
            error_message = f"Ошибка при запросе данных: {str(e)}"

            if request.args.get("format") == "json":
                return jsonify({"error": error_message}), 500

            return render_template("cross_dock/error.html", error=error_message, auto=auto)

    logger.info("Возвращение пустого шаблона для начальной загрузки страницы")
    return render_template(
        "cross_dock/task_status.html",
        tasks_info={},
        page=page,
        total_pages=1,
        limit=limit,
        auto=auto,
        force_refresh=force_refresh,
    )


@percentage_bp.route("/percentage_scheduller", methods=["GET"])
@jwt_required()
async def parser_scheduller_cross_doc():
    logger.info("Отображение страницы планировщика проценки.")
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
        connect_timeout=99999,
        send_receive_timeout=99999,
    )
    client = ClicData(client=client, settings={}, filters={})
    sup_lists = await get_sup_lists(client=client)
    return render_template("percentage_scheduller.html", sups=sup_lists)


@percentage_bp.route("/schedulle_tasks_cross_doc", methods=["GET"], endpoint="schedulle_tasks_cross_doc")
@jwt_required()
async def view_tasks():
    logger.info("Просмотр задач кросс-докинга.")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/tasks_cross_doc", headers=headers
            ) as response:
                tasks_info = await response.json()
                tasks_info = [dict(task) for task in tasks_info]
        logger.debug(f"Получена информация о задачах: {tasks_info}")
        for task in tasks_info:
            types = []
            for type_ in task["type"]:
                if not "everyweek" in type_:
                    types.append(type_)
                else:
                    types.append(week_days[int(type_.split("|")[-1])])
            task["type"] = ", ".join(types)

        if request.args.get("format") == "json":
            return jsonify(tasks_info)

        return render_template("schedulle_tasks_cross_doc.html", tasks_info=tasks_info)
    except Exception as e:
        logger.error(f"Ошибка при просмотре задач: {e}")
        return jsonify({"error": str(e)}), 400


@percentage_bp.route("/update_percentage_parser/<int:task_id>", methods=["GET"])
@jwt_required()
async def update_task(task_id):
    logger.info(f"Обновление задачи парсера проценки: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    client = Client(
        ClickInfo.host,
        user=ClickInfo.settings["user"],
        password=ClickInfo.settings["password"],
        connect_timeout=99999,
        send_receive_timeout=99999,
    )
    client = ClicData(client=client, settings={}, filters={})

    sups = list(await get_sup_lists(client))
    if sups is None:
        logger.error("Не удалось получить списки поставщиков.")
        return redirect(url_for("auth.login"))
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/tasks_cross_doc/{task_id}", headers=headers
            ) as response:
                task_info = await response.json()
                task_info = dict(task_info)

        if request.args.get("format") == "json":
            return jsonify(task_info)

        return render_template("update_cross_doc.html", task_info=task_info, sups=sups)
    except Exception as e:
        logger.error(f"Ошибка при обновлении задачи: {e}")
        return jsonify({"error": str(e)}), 400


@percentage_bp.route("/update_percentage_parser/<int:task_id>", methods=["POST"])
@jwt_required()
async def update_task_post(task_id):
    logger.info(f"Отправка обновленной задачи парсера проценки: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    platforms = request.form.get("platforms")
    schedule_type = request.form.get("schedule_type")
    file = request.files.get("file")

    async with aiohttp.ClientSession() as session:
        headers = {"app-key": app_key}
        async with session.get(
            f"{shared_directory_server}:{PORT_SCHED}/tasks_cross_doc/{task_id}", headers=headers
        ) as response:
            task_info = await response.json()
            task_info = dict(task_info)

    updated_settings = task_info["settings"]

    try:
        if type(json.loads(platforms)) == list:
            updated_settings["platforms"] = json.loads(platforms) if platforms else []
        else:
            updated_settings["platforms"] = [json.loads(platforms)] if platforms else []
        schedule_type = json.loads(schedule_type) if schedule_type else []
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка декодирования JSON: {e}")
        return jsonify({"error": f"Ошибка декодирования JSON: {str(e)}"}), 400

    if not updated_settings["platforms"]:
        logger.warning("Платформы не указаны.")
        return jsonify({"error": "Не указаны платформы (platforms)"}), 400

    try:
        form_data = aiohttp.FormData()
        form_data.add_field("id", str(task_id))
        form_data.add_field("settings", json.dumps(updated_settings))
        form_data.add_field("schedule_type", json.dumps(schedule_type))

        if file:
            form_data.add_field(
                "file",
                file.stream,
                filename=file.filename,
                content_type=file.content_type or "application/octet-stream",
            )

        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.post(
                f"{shared_directory_server}:{PORT_SCHED}/update_cross_doc", data=form_data, headers=headers
            ) as response:
                response_json = await response.json()
                if response.status >= 300:
                    logger.error(f"Ошибка при обновлении задачи: {response_json.get('error', 'Неизвестная ошибка')}")
                    return jsonify({"error": response_json.get("error", "Неизвестная ошибка")}), response.status
                logger.success("Задача успешно обновлена.")
                return jsonify(response_json), 200
    except Exception as e:
        logger.error(f"Ошибка при обновлении задачи: {e}")
        return jsonify({"error": str(e)}), 500


@percentage_bp.route("/upload_file_scheduller_cross_dock", methods=["POST"])
@jwt_required()
async def upload_file_cross_doc_scheduller():
    logger.info("Загрузка файла для планировщика кросс-докинга.")
    shared_directory_server = SharedDirectoryHostInfo().host

    if "excelFile" not in request.files:
        logger.warning("Файл не найден в запросе.")
        return jsonify({"error": "No file part"}), 400

    file = request.files["excelFile"]

    if file.filename == "":
        logger.warning("Файл не выбран.")
        return jsonify({"error": "No selected file"}), 400

    platforms = request.form.get("group")
    type_scheduller = request.form.get("type")

    platforms = [platforms] if platforms else []
    type_scheduller = json.loads(type_scheduller) if type_scheduller else []

    if not platforms:
        logger.warning("Платформы не указаны.")
        return jsonify({"error": "No platforms specified"}), 400

    try:
        file_content = file.read()
        form_data = aiohttp.FormData()
        form_data.add_field("file", io.BytesIO(file_content), filename=file.filename, content_type=file.content_type)
        settings_data = {"platforms": platforms, "filename": file.filename, "auto": True}
        form_data.add_field("settings", json.dumps(settings_data), content_type="application/json")
        form_data.add_field("type", json.dumps(type_scheduller))
        form_data.add_field("filename", file.filename)
        form_data.add_field("created", datetime.now(moscow_tz).strftime("%Y-%m-%d %H:%M:%S"))

        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.post(
                f"{shared_directory_server}:{PORT_SCHED}/save_cross_doc", data=form_data, headers=headers
            ) as response:
                response_json = await response.text()
                logger.success("Файл для планировщика успешно загружен.")
                return jsonify({"response": response_json})

    except Exception as e:
        # Construct debugging information
        form_data_debug = {}
        for field in form_data.fields:
            # Attempt to retrieve the content of each field
            try:
                value = form_data[field]
                # If the value is a file-like object, handle it specially
                if isinstance(value, io.BytesIO):
                    value = "binary content (not shown)"
                form_data_debug[field] = value
            except Exception as field_exception:
                form_data_debug[field] = f"Could not retrieve value: {str(field_exception)}"

        return jsonify({"error": str(e), "form_data_debug": form_data_debug}), 400


@percentage_bp.route("/schedulle_tasks_cross_doc/<int:task_id>", methods=["DELETE"])
@jwt_required()
async def delete_task(task_id):
    logger.info(f"Удаление задачи кросс-докинга: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.delete(
                f"{shared_directory_server}:{PORT_SCHED}/tasks_cross_doc/{task_id}", headers=headers
            ) as response:
                result = await response.json()

        if response.status == 200:
            logger.success(f"Задача {task_id} успешно удалена.")
            return jsonify({"message": "Task deleted successfully"}), 200
        logger.error(f"Ошибка при удалении задачи {task_id}: {result.get('error', 'Неизвестная ошибка')}")
        return jsonify({"error": result.get("error", "Unknown error occurred")}), response.status
    except Exception as e:
        logger.error(f"Ошибка при удалении задачи {task_id}: {e}")
        return jsonify({"error": str(e)}), 400


@percentage_bp.route("/start_task_now_cross_doc/<int:task_id>", methods=["GET"])
@jwt_required()
async def start_task_now(task_id):
    logger.info(f"Запуск задачи кросс-докинга: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/start_task_cross_doc/{task_id}", headers=headers
            ) as response:
                result = await response.json()

        if response.status == 200:
            logger.success(f"Задача {task_id} успешно запущена.")
            return jsonify({"message": "Task star successfully"}), 200
        logger.error(f"Ошибка при запуске задачи {task_id}: {result.get('error', 'Неизвестная ошибка')}")
        return jsonify({"error": result.get("error", "Unknown error occurred")}), response.status
    except Exception as e:
        logger.error(f"Ошибка при запуске задачи {task_id}: {e}")
        return jsonify({"error": str(e)}), 400


@percentage_bp.route("/task_history_cross_doc/<int:task_id>", methods=["GET"])
@jwt_required()
async def task_history(task_id):
    logger.info(f"Получение истории задачи кросс-докинга: {task_id}")
    shared_directory_server = SharedDirectoryHostInfo().host
    try:
        async with aiohttp.ClientSession() as session:
            headers = {"app-key": app_key}
            async with session.get(
                f"{shared_directory_server}:{PORT_SCHED}/task_filename_cross_doc/{task_id}", headers=headers
            ) as response:
                filename = (await response.json())["filename"]
        if response.status == 200:
            parser = ParserInfo().host
            flower_url = f"{parser}/tasks/cross_doc?auto=true"
            logger.debug(f"URL Flower: {flower_url}")
            async with aiohttp.ClientSession() as session:
                headers = {"app-key": app_key}
                async with session.get(flower_url, headers=headers) as response:
                    tasks_info = await response.json()
                filtered_tasks = {task_id: info for task_id, info in tasks_info.items() if info["filename"] == filename}
                sorted_task_ids = sorted(filtered_tasks, key=lambda k: filtered_tasks[k]["id"], reverse=True)
                sorted_tasks = OrderedDict((task_id, filtered_tasks[task_id]) for task_id in sorted_task_ids)
            return render_template(
                "task_status_cross_doc_id_for_scheduller.html", tasks_info=sorted_tasks, task_id=task_id
            )
        else:
            logger.error("Неизвестная ошибка при получении истории задачи.")
            return jsonify({"error": "Unknown error occurred"}), 400
    except Exception as e:
        logger.error(f"Ошибка при получении истории задачи: {e}")
        return jsonify({"error": str(e)}), 400